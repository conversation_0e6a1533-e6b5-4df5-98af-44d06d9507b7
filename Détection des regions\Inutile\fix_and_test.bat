@echo off
echo ===================================================
echo DIAGNOSTIC ET CORRECTION DU DÉTECTEUR
echo ===================================================
echo.

echo 1. Test Python basique...
python -c "print('Python fonctionne')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python ne fonctionne pas
    pause
    exit /b 1
)

echo.
echo 2. Test PaddleOCR...
python -c "from paddleocr import PaddleOCR; print('PaddleOCR OK')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PaddleOCR ne fonctionne pas
    pause
    exit /b 1
)

echo.
echo 3. Test import détecteur...
python -c "from detector import Detector; print('Import détecteur OK')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème import détecteur
    echo Vérification des erreurs...
    python -c "try: from detector import Detector; except Exception as e: print('Erreur:', e)"
    pause
    exit /b 1
)

echo.
echo 4. Test initialisation détecteur...
python -c "from detector import Detector; d = Detector(use_cuda=False); print('Détecteur initialisé')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème initialisation détecteur
    pause
    exit /b 1
)

echo.
echo 5. Test détection simple...
python -c "from detector import Detector; import numpy as np; import cv2; d = Detector(use_cuda=False); img = np.ones((100,200,3), dtype=np.uint8)*255; cv2.putText(img, 'A', (50,70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0,0,0), 3); result = d.detect_text_fast(img); print('Résultat:', result)"

echo.
echo ===================================================
echo ✅ TESTS TERMINÉS
echo ===================================================
echo Si tous les tests passent, votre détecteur fonctionne !
echo Vous pouvez maintenant utiliser votre application.
echo ===================================================
echo.

pause
