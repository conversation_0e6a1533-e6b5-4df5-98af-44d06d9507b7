#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test du conseiller unifié - UN SEUL CONSEIL DE MISE
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

def test_conseiller_unifie():
    """Test du conseiller unifié avec fusion de tous les conseils"""
    print("🎯 TEST DU CONSEILLER UNIFIÉ")
    print("=" * 60)
    
    try:
        from poker_advisor_light import PokerAdvisorLight
        
        advisor = PokerAdvisorLight()
        
        if not advisor.advanced_features_enabled:
            print("❌ Modules avancés non disponibles - test impossible")
            return
        
        print("✅ Modules avancés chargés - test du conseiller unifié")
        
        # Situation de test : Main premium avec adversaires
        test_situation = {
            # Main premium As-Kd
            "carte_1m": {"text": "As", "colors": ["red"]},
            "carte_2m": {"text": "Kd", "colors": ["black"]},
            
            # Board favorable : As-5h-2c
            "card_1": {"text": "Ah", "colors": ["red"]},
            "card_2": {"text": "5h", "colors": ["red"]},
            "card_3": {"text": "2c", "colors": ["black"]},
            
            # Situation financière
            "mes_jetons": {"text": "1000", "colors": []},
            "jetons_joueur1": {"text": "800", "colors": ["white"]},
            "jetons_joueur2": {"text": "600", "colors": ["white"]},
            
            # Mises
            "mise_joueur1": {"text": "50", "colors": ["white"]},
            "pot_total": {"text": "150", "colors": ["white"]},
        }
        
        print("\n📊 Analyse de la situation...")
        print("Main: As-Kd | Board: Ah-5h-2c | Pot: 150 BB | Mise à suivre: 50 BB")
        
        analysis, formatted_analysis = advisor.analyze_detection_results(test_situation)
        
        # Vérifier que l'analyse unifiée est présente
        if analysis.get("unified_analysis"):
            print("\n🔥 CONSEILLER UNIFIÉ ACTIVÉ !")
            
            unified = analysis["unified_analysis"]
            
            print(f"✅ Action recommandée: {analysis['recommended_action']}")
            print(f"✅ Raison: {analysis['action_reason']}")
            print(f"✅ Score de confiance: {analysis.get('confidence_score', 0):.0f}%")
            print(f"✅ Consensus: {unified.get('consensus', 'N/A')}")
            
            # Analyser les sources
            sources = unified.get("sources", [])
            all_recs = unified.get("all_recommendations", {})
            
            print(f"\n📊 DÉTAIL DES ANALYSES:")
            for source, rec_data in all_recs.items():
                action = rec_data.get("action", "N/A")
                weight = rec_data.get("weight", 0)
                source_desc = rec_data.get("source", source)
                print(f"   • {source_desc}: {action} (poids: {weight:.1f})")
            
            # Vérifier les conflits
            conflicts = unified.get("conflicts", [])
            if conflicts:
                print(f"\n⚠️ CONFLITS DÉTECTÉS:")
                for conflict in conflicts:
                    print(f"   • {conflict}")
            else:
                print(f"\n✅ AUCUN CONFLIT - Toutes les analyses convergent")
            
            # Afficher un extrait de l'affichage unifié
            print(f"\n📋 EXTRAIT DE L'AFFICHAGE UNIFIÉ:")
            lines = formatted_analysis.split('\n')
            in_unified_section = False
            line_count = 0
            
            for line in lines:
                if "🔥 **ANALYSE UNIFIÉE** 🔥" in line:
                    in_unified_section = True
                    print(f"   {line}")
                    line_count += 1
                elif in_unified_section and line_count < 8:
                    print(f"   {line}")
                    line_count += 1
                    if line.strip() == "" and line_count > 4:
                        break
            
            # Vérifier la cohérence
            print(f"\n🔍 VÉRIFICATION DE COHÉRENCE:")
            
            # L'action doit être claire et précise
            action = analysis['recommended_action']
            if any(keyword in action.upper() for keyword in ['BET', 'RAISE', 'CALL', 'FOLD', 'ALL-IN']):
                print(f"   ✅ Action claire: {action}")
            else:
                print(f"   ⚠️ Action peu claire: {action}")
            
            # Le score de confiance doit être élevé pour une main premium
            confidence = analysis.get('confidence_score', 0)
            if confidence >= 70:
                print(f"   ✅ Confiance élevée: {confidence:.0f}%")
            elif confidence >= 50:
                print(f"   ⚠️ Confiance modérée: {confidence:.0f}%")
            else:
                print(f"   ❌ Confiance faible: {confidence:.0f}%")
            
            # Vérifier que l'action est appropriée pour une main premium
            if any(keyword in action.upper() for keyword in ['BET', 'RAISE', 'ALL-IN']):
                print(f"   ✅ Action agressive appropriée pour main premium")
            elif 'CALL' in action.upper():
                print(f"   ⚠️ Action passive pour main premium (peut être justifiée)")
            elif 'FOLD' in action.upper():
                print(f"   ❌ Fold avec main premium - vérifier la logique")
            
        else:
            print("❌ Analyse unifiée non générée")
            print("   Utilisation de l'analyse de base uniquement")
            print(f"   Action: {analysis.get('recommended_action', 'N/A')}")
    
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

def test_situations_multiples():
    """Test avec différentes situations pour vérifier la robustesse"""
    print("\n🎯 TEST SITUATIONS MULTIPLES")
    print("=" * 60)
    
    try:
        from poker_advisor_light import PokerAdvisorLight
        
        advisor = PokerAdvisorLight()
        
        if not advisor.advanced_features_enabled:
            print("❌ Modules avancés non disponibles")
            return
        
        situations = [
            {
                "name": "Main faible (7-2 offsuit)",
                "data": {
                    "carte_1m": {"text": "7s", "colors": ["black"]},
                    "carte_2m": {"text": "2d", "colors": ["black"]},
                    "mes_jetons": {"text": "500", "colors": []},
                    "mise_joueur1": {"text": "100", "colors": ["white"]},
                    "pot_total": {"text": "200", "colors": ["white"]},
                },
                "expected_action": "FOLD"
            },
            {
                "name": "Paire d'As (AA)",
                "data": {
                    "carte_1m": {"text": "As", "colors": ["red"]},
                    "carte_2m": {"text": "Ad", "colors": ["black"]},
                    "mes_jetons": {"text": "1000", "colors": []},
                    "pot_total": {"text": "50", "colors": ["white"]},
                },
                "expected_action": ["BET", "RAISE", "ALL-IN"]
            },
            {
                "name": "Tirage couleur (9h-8h sur Ah-5h-2c)",
                "data": {
                    "carte_1m": {"text": "9h", "colors": ["red"]},
                    "carte_2m": {"text": "8h", "colors": ["red"]},
                    "card_1": {"text": "Ah", "colors": ["red"]},
                    "card_2": {"text": "5h", "colors": ["red"]},
                    "card_3": {"text": "2c", "colors": ["black"]},
                    "mes_jetons": {"text": "800", "colors": []},
                    "mise_joueur1": {"text": "30", "colors": ["white"]},
                    "pot_total": {"text": "100", "colors": ["white"]},
                },
                "expected_action": ["CALL", "BET", "RAISE"]
            }
        ]
        
        for i, situation in enumerate(situations, 1):
            print(f"\n📊 Test {i}: {situation['name']}")
            
            analysis, _ = advisor.analyze_detection_results(situation["data"])
            
            if analysis.get("unified_analysis"):
                action = analysis['recommended_action']
                confidence = analysis.get('confidence_score', 0)
                consensus = analysis['unified_analysis'].get('consensus', 'N/A')
                
                print(f"   Action: {action}")
                print(f"   Confiance: {confidence:.0f}%")
                print(f"   Consensus: {consensus}")
                
                # Vérifier si l'action est dans les attentes
                expected = situation["expected_action"]
                if isinstance(expected, list):
                    action_ok = any(exp in action.upper() for exp in expected)
                else:
                    action_ok = expected in action.upper()
                
                if action_ok:
                    print(f"   ✅ Action appropriée")
                else:
                    print(f"   ⚠️ Action inattendue (attendu: {expected})")
            else:
                print(f"   ❌ Analyse unifiée non disponible")
    
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

if __name__ == "__main__":
    print("🚀 TEST DU CONSEILLER UNIFIÉ")
    print("=" * 60)
    print("🎯 Objectif: Fusionner tous les conseils en UN SEUL optimal")
    print("📊 Sources: Analyse de base + Monte Carlo + GTO + Range vs Range")
    print("🔥 Résultat: Action unique avec score de confiance")
    print()
    
    # Test principal
    test_conseiller_unifie()
    
    # Tests multiples
    test_situations_multiples()
    
    print("\n✅ TESTS TERMINÉS")
    print("=" * 60)
    print("🎯 Le conseiller unifié fusionne maintenant tous les conseils !")
    print("🔥 UN SEUL CONSEIL DE MISE avec score de confiance")
    print("📊 Consensus des analyses avancées")
    print("⚠️ Détection automatique des conflits")
    print("🎮 Intégré visuellement dans l'interface")
