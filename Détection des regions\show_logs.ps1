# Script PowerShell pour afficher les dernières entrées du log de surveillance
# Encodage UTF-8 pour gérer les caractères spéciaux

param(
    [int]$Lines = 10,
    [string]$LogFile = "monitor.log"
)

try {
    # Changer vers le répertoire du script
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    if ($ScriptDir) {
        Set-Location $ScriptDir
    }

    # Vérifier si le fichier existe
    if (Test-Path $LogFile) {
        Write-Host "=== DERNIÈRES $Lines ENTRÉES DU LOG DE SURVEILLANCE ===" -ForegroundColor Green
        Write-Host ""

        # Lire les dernières lignes du fichier
        $content = Get-Content $LogFile -Encoding UTF8 | Select-Object -Last $Lines

        if ($content) {
            foreach ($line in $content) {
                Write-Host $line
            }
        } else {
            Write-Host "Le fichier de log est vide." -ForegroundColor Yellow
        }
    } else {
        Write-Host "Fichier de log non trouvé: $LogFile" -ForegroundColor Red
    }
} catch {
    Write-Host "Erreur lors de la lecture du fichier de log: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
