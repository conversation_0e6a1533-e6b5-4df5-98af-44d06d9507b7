#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de performance avec traitement parallèle
============================================

Ce script teste les améliorations de performance apportées par :
1. Le mode rapide (1 seul appel OCR par région)
2. Le traitement parallèle des régions
3. La combinaison des deux optimisations

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import time
import cv2
import numpy as np
from detector import Detector

def create_test_image():
    """Crée une image de test avec plusieurs régions simulées"""
    # Créer une image de test de 1920x1080 (résolution courante)
    image = np.zeros((1080, 1920, 3), dtype=np.uint8)
    
    # Ajouter un fond coloré (simuler le tapis de poker)
    image[:] = (139, 69, 19)  # Couleur marron pour le tapis
    
    # Ajouter des rectangles blancs pour simuler des cartes
    card_positions = [
        (100, 100, 80, 120),   # Carte 1
        (200, 100, 80, 120),   # Carte 2
        (300, 100, 80, 120),   # Carte 3
        (400, 100, 80, 120),   # Carte 4
        (500, 100, 80, 120),   # Carte 5
        (100, 300, 80, 120),   # Carte main 1
        (200, 300, 80, 120),   # Carte main 2
    ]
    
    for x, y, w, h in card_positions:
        # Fond blanc de la carte
        cv2.rectangle(image, (x, y), (x+w, y+h), (255, 255, 255), -1)
        # Bordure noire
        cv2.rectangle(image, (x, y), (x+w, y+h), (0, 0, 0), 2)
        # Ajouter du texte simulé (A, K, Q, etc.)
        cv2.putText(image, 'A', (x+20, y+40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    return image

def benchmark_detection_mode(detector, image, mode_name, fast_mode=True, parallel=True, num_runs=3):
    """Teste les performances d'un mode de détection spécifique"""
    print(f"\n🔍 Test: {mode_name}")
    print("-" * 50)
    
    times = []
    results_list = []
    
    for i in range(num_runs):
        print(f"   Run {i+1}/{num_runs}...", end=" ")
        
        start_time = time.time()
        
        try:
            results = detector.process_image_direct(image, fast_mode=fast_mode, parallel=parallel)
            end_time = time.time()
            duration = end_time - start_time
            times.append(duration)
            results_list.append(results)
            
            print(f"{duration:.3f}s")
        except Exception as e:
            print(f"ERREUR: {e}")
            times.append(float('inf'))
            results_list.append({})
    
    # Calculer les statistiques
    valid_times = [t for t in times if t != float('inf')]
    if valid_times:
        avg_time = sum(valid_times) / len(valid_times)
        min_time = min(valid_times)
        max_time = max(valid_times)
        
        print(f"   Temps moyen: {avg_time:.3f}s")
        print(f"   Temps min/max: {min_time:.3f}s / {max_time:.3f}s")
        
        # Compter les détections réussies
        if results_list and results_list[0]:
            regions_detected = len(results_list[0])
            cards_detected = sum(1 for name, result in results_list[0].items() 
                               if result.get('text', '').strip())
            print(f"   Régions traitées: {regions_detected}")
            print(f"   Cartes détectées: {cards_detected}")
        
        return {
            'mode': mode_name,
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'success_rate': len(valid_times) / num_runs * 100,
            'regions_detected': regions_detected if 'regions_detected' in locals() else 0,
            'cards_detected': cards_detected if 'cards_detected' in locals() else 0
        }
    else:
        print("   ❌ Tous les tests ont échoué")
        return {
            'mode': mode_name,
            'avg_time': float('inf'),
            'min_time': float('inf'),
            'max_time': float('inf'),
            'success_rate': 0,
            'regions_detected': 0,
            'cards_detected': 0
        }

def main():
    """Fonction principale de test de performance"""
    print("🚀 TEST DE PERFORMANCE - TRAITEMENT PARALLÈLE")
    print("=" * 60)
    
    # Créer l'image de test
    test_image = create_test_image()
    print(f"✅ Image de test créée: {test_image.shape}")
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return
    
    print(f"✅ Configuration chargée: {config_path}")
    
    # Initialiser le détecteur
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé avec CUDA")
    except Exception as e:
        print(f"⚠️ Erreur CUDA, utilisation du CPU: {e}")
        try:
            detector = Detector(config_path, use_cuda=False)
            print("✅ Détecteur initialisé avec CPU")
        except Exception as e2:
            print(f"❌ Erreur lors de l'initialisation: {e2}")
            return
    
    # Tests de performance
    benchmarks = []
    
    # Test 1: Mode original (lent, séquentiel)
    benchmark1 = benchmark_detection_mode(
        detector, test_image, 
        "Mode original (lent + séquentiel)", 
        fast_mode=False, parallel=False
    )
    benchmarks.append(benchmark1)
    
    # Test 2: Mode rapide seulement
    benchmark2 = benchmark_detection_mode(
        detector, test_image, 
        "Mode rapide (1 OCR par région)", 
        fast_mode=True, parallel=False
    )
    benchmarks.append(benchmark2)
    
    # Test 3: Mode parallèle seulement
    benchmark3 = benchmark_detection_mode(
        detector, test_image, 
        "Mode parallèle (séquentiel mais parallèle)", 
        fast_mode=False, parallel=True
    )
    benchmarks.append(benchmark3)
    
    # Test 4: Mode optimisé (rapide + parallèle)
    benchmark4 = benchmark_detection_mode(
        detector, test_image, 
        "Mode optimisé (rapide + parallèle)", 
        fast_mode=True, parallel=True
    )
    benchmarks.append(benchmark4)
    
    # Résumé des performances
    print("\n" + "=" * 60)
    print("📈 RÉSUMÉ DES PERFORMANCES")
    print("=" * 60)
    
    # Trier par temps moyen
    valid_benchmarks = [b for b in benchmarks if b['avg_time'] != float('inf')]
    valid_benchmarks.sort(key=lambda x: x['avg_time'])
    
    if valid_benchmarks:
        baseline = valid_benchmarks[-1]  # Le plus lent
        fastest = valid_benchmarks[0]    # Le plus rapide
        
        print(f"🏆 Mode le plus rapide: {fastest['mode']}")
        print(f"   Temps moyen: {fastest['avg_time']:.3f}s")
        print(f"   Régions/Cartes: {fastest['regions_detected']}/{fastest['cards_detected']}")
        
        print(f"\n🐌 Mode le plus lent: {baseline['mode']}")
        print(f"   Temps moyen: {baseline['avg_time']:.3f}s")
        
        # Calculer l'amélioration
        if baseline['avg_time'] > 0:
            improvement = (baseline['avg_time'] - fastest['avg_time']) / baseline['avg_time'] * 100
            speedup = baseline['avg_time'] / fastest['avg_time']
            
            print(f"\n🚀 AMÉLIORATION GLOBALE:")
            print(f"   Gain de performance: +{improvement:.1f}%")
            print(f"   Accélération: {speedup:.1f}x plus rapide")
            print(f"   Temps économisé: {baseline['avg_time'] - fastest['avg_time']:.3f}s par détection")
        
        # Tableau comparatif
        print(f"\n📊 TABLEAU COMPARATIF:")
        print(f"{'Mode':<35} {'Temps (s)':<12} {'Amélioration':<15}")
        print("-" * 62)
        
        for benchmark in valid_benchmarks:
            if baseline['avg_time'] > 0:
                improvement_pct = (baseline['avg_time'] - benchmark['avg_time']) / baseline['avg_time'] * 100
                improvement_str = f"+{improvement_pct:.1f}%"
            else:
                improvement_str = "N/A"
            
            print(f"{benchmark['mode']:<35} {benchmark['avg_time']:<12.3f} {improvement_str:<15}")
    
    print(f"\n✅ Test de performance terminé!")

if __name__ == "__main__":
    main()
    input("\nAppuyez sur Entrée pour fermer...")
