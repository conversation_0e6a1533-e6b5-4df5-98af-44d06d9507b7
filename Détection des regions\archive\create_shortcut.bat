@echo off
echo ===================================================
echo Creation du raccourci de configuration
echo ===================================================
echo.

set CONFIG_DIR=C:\Users\<USER>\PokerAdvisor\Détection des regions\config
set SHORTCUT_NAME=poker_advisor_config - Raccourci
set TARGET_FILE=C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json

echo Dossier de configuration: %CONFIG_DIR%
echo Nom du raccourci: %SHORTCUT_NAME%
echo Fichier cible: %TARGET_FILE%
echo.

if not exist "%CONFIG_DIR%" (
    echo Creation du dossier de configuration...
    mkdir "%CONFIG_DIR%"
)

echo Creation du raccourci...

:: C<PERSON>er un script VBS temporaire pour créer le raccourci
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\create_shortcut.vbs"
echo sLinkFile = "%CONFIG_DIR%\%SHORTCUT_NAME%.lnk" >> "%TEMP%\create_shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\create_shortcut.vbs"
echo oLink.TargetPath = "%TARGET_FILE%" >> "%TEMP%\create_shortcut.vbs"
echo oLink.Save >> "%TEMP%\create_shortcut.vbs"

:: Exécuter le script VBS
cscript //nologo "%TEMP%\create_shortcut.vbs"

:: Supprimer le script VBS temporaire
del "%TEMP%\create_shortcut.vbs"

:: Vérifier si le raccourci a été créé
if exist "%CONFIG_DIR%\%SHORTCUT_NAME%.lnk" (
    echo Raccourci cree avec succes!
    echo Chemin du raccourci: %CONFIG_DIR%\%SHORTCUT_NAME%.lnk
) else (
    echo ERREUR: Le raccourci n'a pas pu etre cree.
)

:: Créer un fichier texte avec le même nom pour la compatibilité
echo %TARGET_FILE% > "%CONFIG_DIR%\%SHORTCUT_NAME%"
echo Fichier texte de secours cree: %CONFIG_DIR%\%SHORTCUT_NAME%

echo.
pause
