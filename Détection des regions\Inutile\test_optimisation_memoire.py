#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test des optimisations mémoire pour la logique avancée.
"""

import psutil
import os
import time

def get_memory_usage():
    """Obtient l'utilisation mémoire actuelle"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

def test_optimisation_memoire():
    """Test des optimisations mémoire"""
    print("🧪 TEST DES OPTIMISATIONS MÉMOIRE")
    print("=" * 50)
    
    # Mesure initiale
    initial_memory = get_memory_usage()
    print(f"📊 Mémoire initiale: {initial_memory:.1f} MB")
    
    try:
        # Import de la logique avancée
        from poker_advisor_integration import poker_integration
        
        after_import_memory = get_memory_usage()
        print(f"📊 Mémoire après import: {after_import_memory:.1f} MB")
        
        # Test de stress avec de nombreux appels
        print("\n🔥 TEST DE STRESS (100 appels)...")
        
        memory_samples = []
        
        for i in range(100):
            # Appel de la logique avancée
            result = poker_integration.evaluate_hand_advanced(
                ["As", "Roi"], ["Cœur", "Pique"],
                ["Dame", "Valet", "10"], ["Trèfle", "Carreau", "Cœur"]
            )
            
            # Mesurer la mémoire tous les 10 appels
            if i % 10 == 0:
                current_memory = get_memory_usage()
                memory_samples.append(current_memory)
                print(f"   Appel {i+1:3d}: {current_memory:.1f} MB")
        
        final_memory = get_memory_usage()
        print(f"\n📊 Mémoire finale: {final_memory:.1f} MB")
        
        # Analyse des résultats
        memory_increase = final_memory - after_import_memory
        max_memory = max(memory_samples)
        min_memory = min(memory_samples)
        
        print(f"\n📈 ANALYSE:")
        print(f"   Augmentation totale: {memory_increase:.1f} MB")
        print(f"   Pic mémoire: {max_memory:.1f} MB")
        print(f"   Minimum mémoire: {min_memory:.1f} MB")
        print(f"   Variation: {max_memory - min_memory:.1f} MB")
        
        # Évaluation
        if memory_increase < 50:  # Moins de 50 MB d'augmentation
            print("✅ EXCELLENT: Pas de fuite mémoire détectée")
        elif memory_increase < 100:  # Moins de 100 MB
            print("✅ BON: Augmentation mémoire acceptable")
        elif memory_increase < 200:  # Moins de 200 MB
            print("⚠️ MOYEN: Augmentation mémoire modérée")
        else:
            print("❌ PROBLÈME: Fuite mémoire importante détectée")
        
        return memory_increase < 200
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def test_performance():
    """Test de performance"""
    print("\n⚡ TEST DE PERFORMANCE")
    print("=" * 30)
    
    try:
        from poker_advisor_integration import poker_integration
        
        # Test de vitesse
        start_time = time.time()
        
        for i in range(50):
            result = poker_integration.evaluate_hand_advanced(
                ["As", "Roi"], ["Cœur", "Pique"],
                ["Dame", "Valet", "10"], ["Trèfle", "Carreau", "Cœur"]
            )
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / 50 * 1000  # ms
        
        print(f"⏱️ Temps total (50 appels): {total_time:.2f}s")
        print(f"⏱️ Temps moyen par appel: {avg_time:.1f}ms")
        
        if avg_time < 10:
            print("✅ EXCELLENT: Performance très rapide")
        elif avg_time < 50:
            print("✅ BON: Performance acceptable")
        elif avg_time < 100:
            print("⚠️ MOYEN: Performance modérée")
        else:
            print("❌ LENT: Performance à améliorer")
        
        return avg_time < 100
        
    except Exception as e:
        print(f"❌ Erreur lors du test de performance: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 TEST DES OPTIMISATIONS MÉMOIRE ET PERFORMANCE")
    print("=" * 70)
    
    # Test 1: Optimisation mémoire
    memory_ok = test_optimisation_memoire()
    
    # Test 2: Performance
    performance_ok = test_performance()
    
    print(f"\n🎯 RÉSULTAT FINAL:")
    print("=" * 30)
    
    if memory_ok and performance_ok:
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Optimisations mémoire efficaces")
        print("✅ Performance acceptable")
        print("✅ L'application devrait fonctionner sans fuite mémoire")
    elif memory_ok:
        print("✅ Optimisations mémoire OK")
        print("⚠️ Performance à améliorer")
    elif performance_ok:
        print("⚠️ Problème mémoire détecté")
        print("✅ Performance OK")
    else:
        print("❌ Problèmes détectés")
        print("❌ Optimisations supplémentaires nécessaires")
    
    print(f"\n📋 RECOMMANDATIONS:")
    if memory_ok:
        print("✅ Vous pouvez utiliser l'application normalement")
        print("✅ Les fuites mémoire ont été corrigées")
    else:
        print("⚠️ Surveillez l'utilisation mémoire")
        print("⚠️ Redémarrez l'application si elle devient lente")

if __name__ == "__main__":
    main()
