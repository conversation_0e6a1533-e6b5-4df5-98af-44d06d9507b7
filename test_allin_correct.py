#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la détection correcte des all-in :
- Rouge dans jetons = indicateur all-in
- Montant dans mise = valeur all-in
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisor<PERSON>ight

def test_allin_correct():
    """Test de la détection all-in correcte"""
    print("🧪 TEST DE LA DÉTECTION ALL-IN CORRECTE")
    print("=" * 60)

    advisor = PokerAdvisorLight()

    # Simuler la vraie situation :
    # - Rouge dans jetons = all-in
    # - Montant dans mise = valeur
    simulated_results = {
        # Cartes
        "carte_1m": {"text": "As", "colors": ["red"]},
        "carte_2m": {"text": "Roi", "colors": ["black"]},

        # Mes jetons
        "mes_jetons": {"text": "1000", "colors": []},

        # JOUEUR 1 : Jetons normaux + mise normale
        "jetons_joueur1": {"text": "800", "colors": ["white"]},  # Jetons normaux
        "mise_joueur1": {"text": "50", "colors": ["white"]},    # Mise normale

        # JOUEUR 2 : Jetons normaux + mise normale
        "jetons_joueur2": {"text": "1200", "colors": ["white"]}, # Jetons normaux
        "mise_joueur2": {"text": "100", "colors": ["white"]},    # Mise normale

        # JOUEUR 3 : ROUGE dans jetons (all-in) + montant dans mise
        "jetons_joueur3": {"text": "all in", "colors": ["red"]}, # ALL-IN (rouge)
        "mise_joueur3": {"text": "500", "colors": ["white"]},    # Montant all-in

        # JOUEUR 4 : ROUGE dans jetons (all-in) + montant dans mise
        "jetons_joueur4": {"text": "", "colors": ["red"]},       # ALL-IN (rouge, pas de texte)
        "mise_joueur4": {"text": "300", "colors": ["white"]},    # Montant all-in

        # Pot total
        "pot_total": {"text": "950", "colors": ["white"]},
    }

    print("📊 SITUATION SIMULÉE:")
    print("   💰 Mes jetons: 1000 BB")
    print("   👥 J1: 800 BB (normal) + mise 50 BB")
    print("   👥 J2: 1200 BB (normal) + mise 100 BB")
    print("   🔥 J3: ROUGE dans jetons + mise 500 BB (ALL-IN)")
    print("   🔥 J4: ROUGE dans jetons + mise 300 BB (ALL-IN)")
    print("   🎯 Pot total: 950 BB")

    # Traiter les résultats
    print(f"\n🔍 TRAITEMENT DES RÉSULTATS...")
    data = advisor.extract_poker_data(simulated_results)

    print(f"\n📈 RÉSULTATS DE L'EXTRACTION:")
    print(f"   💰 Mes jetons: {data['my_stack']} BB")
    print(f"   🎯 Pot total: {data['pot_total']} BB")

    print(f"\n👥 JETONS DES ADVERSAIRES:")
    for player, amount in data["player_stacks"].items():
        print(f"   - {player}: {amount} BB")

    print(f"\n🔥 INDICATEURS ALL-IN (rouge dans jetons):")
    if data["allin_indicators"]:
        for player, is_allin in data["allin_indicators"].items():
            if is_allin:
                print(f"   - {player}: ALL-IN détecté (rouge dans jetons)")
    else:
        print("   Aucun indicateur all-in détecté")

    print(f"\n💲 MISES DES ADVERSAIRES:")
    for player, amount in data["player_bets"].items():
        print(f"   - {player}: {amount} BB")

    print(f"\n🔥 ALL-IN CONFIRMÉS (avec montants):")
    if data["player_allins"]:
        for player, amount in data["player_allins"].items():
            print(f"   - {player}: {amount} BB")
    else:
        print("   Aucun all-in confirmé")

    # Analyser la situation complète
    print(f"\n🎯 ANALYSE COMPLÈTE:")
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)

    # Vérifications
    print(f"\n✅ VÉRIFICATIONS:")

    # Vérifier que J1 et J2 ne sont pas all-in
    j1_allin = "joueur1" in data["player_allins"]
    j2_allin = "joueur2" in data["player_allins"]
    print(f"   J1 all-in: {'❌ OUI' if j1_allin else '✅ NON'}")
    print(f"   J2 all-in: {'❌ OUI' if j2_allin else '✅ NON'}")

    # Vérifier que J3 et J4 sont all-in
    j3_allin = "joueur3" in data["player_allins"]
    j4_allin = "joueur4" in data["player_allins"]
    print(f"   J3 all-in: {'✅ OUI' if j3_allin else '❌ NON'}")
    print(f"   J4 all-in: {'✅ OUI' if j4_allin else '❌ NON'}")

    # Vérifier les montants all-in
    if j3_allin:
        j3_amount = data["player_allins"]["joueur3"]
        print(f"   J3 montant: {'✅ 500 BB' if j3_amount == 500 else f'❌ {j3_amount} BB'}")

    if j4_allin:
        j4_amount = data["player_allins"]["joueur4"]
        print(f"   J4 montant: {'✅ 300 BB' if j4_amount == 300 else f'❌ {j4_amount} BB'}")

    # Afficher le conseiller formaté
    print(f"\n📋 AFFICHAGE DU CONSEILLER:")
    print("=" * 60)
    print(formatted_analysis)

    return True

def test_cas_limites():
    """Test des cas limites"""
    print("\n🧪 TEST DES CAS LIMITES")
    print("=" * 60)

    advisor = PokerAdvisorLight()

    # Cas 1: Rouge dans mise mais pas dans jetons
    print("\n🔍 Cas 1: Rouge dans mise seulement")
    results1 = {
        "jetons_joueur1": {"text": "500", "colors": ["white"]},  # Jetons normaux
        "mise_joueur1": {"text": "100", "colors": ["red"]},      # Mise rouge
    }

    data1 = advisor.extract_poker_data(results1)
    j1_allin = "joueur1" in data1["player_allins"]
    result_text = "✅ ALL-IN détecté" if j1_allin else "❌ Pas d'all-in"
    print(f"   Résultat: {result_text}")

    # Cas 2: Rouge dans jetons mais pas de mise
    print("\n🔍 Cas 2: Rouge dans jetons sans mise")
    results2 = {
        "jetons_joueur2": {"text": "all in", "colors": ["red"]}, # ALL-IN
        # Pas de mise correspondante
    }

    data2 = advisor.extract_poker_data(results2)
    j2_indicator = data2["allin_indicators"].get("joueur2", False)
    j2_allin = "joueur2" in data2["player_allins"]
    print(f"   Indicateur: {'✅ Détecté' if j2_indicator else '❌ Non détecté'}")
    allin_text = "❌ OUI" if j2_allin else "✅ NON (normal, pas de montant)"
    print(f"   All-in confirmé: {allin_text}")

    # Cas 3: Rouge dans jetons ET dans mise
    print("\n🔍 Cas 3: Rouge dans jetons ET mise")
    results3 = {
        "jetons_joueur3": {"text": "", "colors": ["red"]},       # ALL-IN
        "mise_joueur3": {"text": "200", "colors": ["red"]},      # Mise rouge aussi
    }

    data3 = advisor.extract_poker_data(results3)
    j3_allin = "joueur3" in data3["player_allins"]
    result3_text = "✅ ALL-IN détecté" if j3_allin else "❌ Pas d'all-in"
    print(f"   Résultat: {result3_text}")
    if j3_allin:
        print(f"   Montant: {data3['player_allins']['joueur3']} BB")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS ALL-IN CORRECTS")
    print("=" * 60)

    # Test principal
    test_allin_correct()

    # Tests cas limites
    test_cas_limites()

    print("\n✅ TESTS TERMINÉS")
