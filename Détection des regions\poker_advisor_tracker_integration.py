#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 INTÉGRATION TRACKER INTELLIGENT AVEC CONSEILLER POKER
========================================================

Module d'intégration qui connecte le tracker intelligent avec votre
conseiller poker existant pour des recommandations personnalisées.
"""

import os
import json
from typing import Dict, List, Optional, Any
from poker_tracker_intelligent import IntelligentTracker, PlayerStats

class PokerAdvisorWithTracker:
    """Conseiller poker amélioré avec intelligence de tracking"""
    
    def __init__(self, history_path: str = None):
        self.tracker = IntelligentTracker(history_path)
        self.current_table_players = []
        self.table_analysis = None
        self.last_update = None
        
        print("🎯 Conseiller Poker avec Tracker Intelligent initialisé")
        
        # Scanner automatiquement au démarrage
        try:
            self.update_player_database()
        except Exception as e:
            print(f"⚠️ Erreur scan initial: {e}")
    
    def update_player_database(self):
        """Met à jour la base de données des joueurs"""
        print("🔄 Mise à jour de la base de données des joueurs...")
        players = self.tracker.scan_history_files()
        print(f"✅ {len(players)} joueurs mis à jour")
        return players
    
    def set_current_table(self, player_names: List[str]):
        """Définit les joueurs actuellement à la table"""
        self.current_table_players = [name.strip() for name in player_names if name.strip()]
        
        if self.current_table_players:
            print(f"🎲 Table actuelle: {', '.join(self.current_table_players)}")
            self.table_analysis = self.tracker.get_table_intelligence(self.current_table_players)
            print(f"🎯 Style de table détecté: {self.table_analysis['table_style']}")
        else:
            self.table_analysis = None
    
    def get_enhanced_recommendation(self, 
                                  hand_cards: List[str], 
                                  board_cards: List[str],
                                  pot_size: int = 0,
                                  my_stack: int = 0,
                                  opponent_bets: Dict[str, int] = None) -> Dict[str, Any]:
        """
        Génère une recommandation améliorée en tenant compte des profils adversaires
        """
        # Recommandation de base (votre logique existante)
        base_recommendation = self._get_base_recommendation(hand_cards, board_cards, pot_size)
        
        # Améliorations basées sur le tracking
        enhanced_recommendation = {
            "base_action": base_recommendation["action"],
            "base_reasoning": base_recommendation["reasoning"],
            "tracker_adjustments": [],
            "opponent_insights": {},
            "table_dynamics": {},
            "final_recommendation": base_recommendation["action"],
            "confidence": base_recommendation.get("confidence", 0.5)
        }
        
        # Analyser les adversaires présents
        if self.current_table_players:
            for player_name in self.current_table_players:
                profile = self.tracker.get_player_profile(player_name)
                if profile:
                    enhanced_recommendation["opponent_insights"][player_name] = {
                        "style": profile["style"]["type"],
                        "vpip": profile["stats"].vpip,
                        "aggression": profile["stats"].aggression_factor,
                        "recommendations": profile["recommendations"][:2]  # Top 2 recommandations
                    }
        
        # Ajustements basés sur l'analyse de table
        if self.table_analysis:
            enhanced_recommendation["table_dynamics"] = {
                "style": self.table_analysis["table_style"],
                "recommendations": self.table_analysis["recommendations"],
                "alerts": self.table_analysis["alerts"]
            }
            
            # Ajuster la recommandation selon le style de table
            adjusted_action = self._adjust_for_table_style(
                base_recommendation["action"],
                self.table_analysis["table_style"],
                hand_cards,
                board_cards
            )
            
            if adjusted_action != base_recommendation["action"]:
                enhanced_recommendation["tracker_adjustments"].append(
                    f"Ajustement pour table {self.table_analysis['table_style']}: "
                    f"{base_recommendation['action']} → {adjusted_action}"
                )
                enhanced_recommendation["final_recommendation"] = adjusted_action
        
        # Ajustements spécifiques aux adversaires
        if opponent_bets:
            for player_name, bet_amount in opponent_bets.items():
                if player_name in enhanced_recommendation["opponent_insights"]:
                    player_info = enhanced_recommendation["opponent_insights"][player_name]
                    adjustment = self._adjust_for_opponent(
                        enhanced_recommendation["final_recommendation"],
                        player_info,
                        bet_amount,
                        pot_size
                    )
                    
                    if adjustment:
                        enhanced_recommendation["tracker_adjustments"].append(adjustment)
        
        return enhanced_recommendation
    
    def _get_base_recommendation(self, hand_cards: List[str], board_cards: List[str], pot_size: int) -> Dict[str, Any]:
        """Recommandation de base (à remplacer par votre logique existante)"""
        # Ceci est un placeholder - remplacez par votre logique de conseiller existante
        
        # Évaluation simple pour l'exemple
        if not hand_cards:
            return {"action": "fold", "reasoning": "Pas de cartes", "confidence": 1.0}
        
        # Logique simplifiée basée sur les cartes
        high_cards = sum(1 for card in hand_cards if card[0] in ['A', 'K', 'Q', 'J'])
        
        if high_cards >= 2:
            return {"action": "raise", "reasoning": "Main forte", "confidence": 0.8}
        elif high_cards == 1:
            return {"action": "call", "reasoning": "Main moyenne", "confidence": 0.6}
        else:
            return {"action": "fold", "reasoning": "Main faible", "confidence": 0.7}
    
    def _adjust_for_table_style(self, base_action: str, table_style: str, 
                               hand_cards: List[str], board_cards: List[str]) -> str:
        """Ajuste l'action selon le style de table"""
        
        if table_style == "Tight-Passive":
            # Table serrée: être plus agressif
            if base_action == "call":
                return "raise"  # Voler plus souvent
            elif base_action == "fold" and len(hand_cards) >= 2:
                # Peut-être jouer plus de mains
                if hand_cards[0][0] in ['A', 'K', 'Q', 'J'] or hand_cards[1][0] in ['A', 'K', 'Q', 'J']:
                    return "call"
        
        elif table_style == "Loose-Aggressive":
            # Table agressive: être plus conservateur
            if base_action == "raise":
                return "call"  # Moins d'agressivité
            elif base_action == "call":
                # Être plus sélectif
                high_cards = sum(1 for card in hand_cards if card[0] in ['A', 'K', 'Q'])
                if high_cards < 1:
                    return "fold"
        
        return base_action
    
    def _adjust_for_opponent(self, current_action: str, opponent_info: Dict, 
                           bet_amount: int, pot_size: int) -> Optional[str]:
        """Ajuste l'action selon un adversaire spécifique"""
        
        style = opponent_info["style"]
        aggression = opponent_info["aggression"]
        
        # Si l'adversaire est très agressif et mise gros
        if aggression > 3 and bet_amount > pot_size * 0.7:
            if current_action == "call":
                return f"Contre {style} très agressif: considérer fold au lieu de call"
        
        # Si l'adversaire est très passif et mise
        elif aggression < 1 and bet_amount > pot_size * 0.5:
            if current_action == "call":
                return f"Contre {style} passif qui mise: attention, main forte probable"
        
        return None
    
    def get_hud_display(self) -> Dict[str, Any]:
        """Données pour affichage HUD en temps réel"""
        hud_data = {
            "players": {},
            "table_info": {},
            "alerts": []
        }
        
        # Données des joueurs
        for player_name in self.current_table_players:
            hud_data["players"][player_name] = self.tracker.get_hud_data(player_name)
        
        # Informations de table
        if self.table_analysis:
            hud_data["table_info"] = {
                "style": self.table_analysis["table_style"],
                "player_count": len(self.current_table_players),
                "known_players": len([p for p in hud_data["players"].values() if p["hands"] > 0])
            }
            hud_data["alerts"] = self.table_analysis["alerts"]
        
        return hud_data
    
    def export_session_report(self, output_file: str = "session_report.txt"):
        """Exporte un rapport de session"""
        if not self.current_table_players:
            return "Aucune table active"
        
        report = f"""
🎯 RAPPORT DE SESSION POKER
{'='*50}

📅 Date: {self.tracker.last_scan}
🎲 Joueurs à la table: {', '.join(self.current_table_players)}

"""
        
        if self.table_analysis:
            report += f"""
🎯 ANALYSE DE TABLE:
• Style général: {self.table_analysis['table_style']}

💡 Recommandations table:
"""
            for rec in self.table_analysis['recommendations']:
                report += f"• {rec}\n"
            
            if self.table_analysis['alerts']:
                report += f"\n⚠️ Alertes:\n"
                for alert in self.table_analysis['alerts']:
                    report += f"• {alert}\n"
        
        report += f"\n👥 PROFILS DES ADVERSAIRES:\n"
        for player_name in self.current_table_players:
            profile = self.tracker.get_player_profile(player_name)
            if profile:
                stats = profile['stats']
                style = profile['style']
                report += f"""
🎭 {player_name}:
  • Style: {style['type']} (confiance: {style['confidence']*100:.0f}%)
  • Stats: VPIP {stats.vpip:.1f}% | PFR {stats.pfr:.1f}% | AF {stats.aggression_factor:.1f}
  • Mains observées: {stats.hands_played}
  • Recommandations: {', '.join(profile['recommendations'][:2])}
"""
            else:
                report += f"\n🔍 {player_name}: Joueur inconnu - observez attentivement\n"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ Rapport sauvegardé: {output_file}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
        
        return report

# Fonctions d'intégration pour votre application existante
def integrate_tracker_with_existing_advisor(poker_advisor_instance, history_path: str = None):
    """Intègre le tracker avec votre conseiller existant"""
    
    # Créer l'instance du tracker
    tracker_advisor = PokerAdvisorWithTracker(history_path)
    
    # Ajouter les méthodes du tracker à votre conseiller existant
    poker_advisor_instance.tracker = tracker_advisor.tracker
    poker_advisor_instance.set_current_table = tracker_advisor.set_current_table
    poker_advisor_instance.get_enhanced_recommendation = tracker_advisor.get_enhanced_recommendation
    poker_advisor_instance.get_hud_display = tracker_advisor.get_hud_display
    
    print("✅ Tracker intégré avec le conseiller existant")
    return poker_advisor_instance

def create_enhanced_advisor(history_path: str = None) -> PokerAdvisorWithTracker:
    """Crée un nouveau conseiller avec tracker intégré"""
    return PokerAdvisorWithTracker(history_path)

if __name__ == "__main__":
    # Test de l'intégration
    print("🧪 Test de l'intégration Tracker + Conseiller")
    print("="*50)
    
    try:
        # Créer le conseiller amélioré
        advisor = create_enhanced_advisor()
        
        # Simuler une table
        test_players = ["Prelyna", "Badboy44700", "ludogrnx"]
        advisor.set_current_table(test_players)
        
        # Test d'une recommandation
        test_hand = ["As", "Kh"]
        test_board = ["Qd", "Jc", "Ts"]
        
        recommendation = advisor.get_enhanced_recommendation(
            hand_cards=test_hand,
            board_cards=test_board,
            pot_size=1000,
            my_stack=5000
        )
        
        print(f"\n🎯 Test recommandation:")
        print(f"Main: {test_hand}")
        print(f"Board: {test_board}")
        print(f"Action de base: {recommendation['base_action']}")
        print(f"Action finale: {recommendation['final_recommendation']}")
        
        if recommendation['tracker_adjustments']:
            print(f"Ajustements tracker:")
            for adj in recommendation['tracker_adjustments']:
                print(f"  • {adj}")
        
        # Test HUD
        hud_data = advisor.get_hud_display()
        print(f"\n📱 Données HUD:")
        print(f"Style de table: {hud_data['table_info'].get('style', 'Inconnu')}")
        print(f"Joueurs connus: {hud_data['table_info'].get('known_players', 0)}")
        
        print("\n✅ Test d'intégration réussi!")
        
    except Exception as e:
        print(f"❌ Erreur test: {e}")
        import traceback
        traceback.print_exc()
