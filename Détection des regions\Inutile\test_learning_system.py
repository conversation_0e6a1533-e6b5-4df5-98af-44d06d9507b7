#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test du système d'apprentissage en temps réel
============================================

Ce script teste le nouveau système d'apprentissage qui collecte
les corrections manuelles pour améliorer la détection future.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os
import cv2
import numpy as np

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from learning_system import LearningSystem

def create_test_image(text="A", size=(100, 150)):
    """Crée une image de test simulant une carte"""
    image = np.ones((size[1], size[0], 3), dtype=np.uint8) * 255  # Fond blanc
    
    # Ajouter du texte
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 2
    color = (0, 0, 0)  # Noir
    thickness = 3
    
    # Calculer la position du texte pour le centrer
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = (size[0] - text_size[0]) // 2
    text_y = (size[1] + text_size[1]) // 2
    
    cv2.putText(image, text, (text_x, text_y), font, font_scale, color, thickness)
    
    return image

def test_learning_system():
    """Test complet du système d'apprentissage"""
    
    print("🧪 Test du système d'apprentissage en temps réel")
    print("=" * 60)
    
    # Créer une instance du système d'apprentissage
    learning_system = LearningSystem("test_learning_data")
    
    print(f"✅ Système d'apprentissage initialisé")
    print(f"📂 Répertoire de données: test_learning_data")
    
    # Test 1: Enregistrer des corrections de base
    print("\n🔧 Test 1: Enregistrement de corrections de base")
    print("-" * 50)
    
    test_cases = [
        {
            "region": "card_1",
            "detected": "8",
            "corrected": "6",
            "confidence": 0.75,
            "text": "A"
        },
        {
            "region": "card_2", 
            "detected": "K",
            "corrected": "A",
            "confidence": 0.82,
            "text": "K"
        },
        {
            "region": "card_3",
            "detected": "Q",
            "corrected": "",  # Pas de cartes
            "confidence": 0.65,
            "text": "Q"
        },
        {
            "region": "hand_card_1",
            "detected": "9",
            "corrected": "5",
            "confidence": 0.70,
            "text": "9"
        }
    ]
    
    correction_ids = []
    for i, case in enumerate(test_cases):
        # Créer une image de test
        test_image = create_test_image(case["text"])
        
        # Contexte de test
        context = {
            "test_case": i + 1,
            "colors_detected": ["black", "white"],
            "region_type": "board" if case["region"].startswith("card_") else "hand"
        }
        
        # Enregistrer la correction
        correction_id = learning_system.record_correction(
            region_name=case["region"],
            image_crop=test_image,
            detected_value=case["detected"],
            corrected_value=case["corrected"],
            detected_confidence=case["confidence"],
            context_info=context
        )
        
        if correction_id:
            correction_ids.append(correction_id)
            print(f"  ✅ Correction {i+1}: {case['region']} '{case['detected']}' → '{case['corrected']}' (ID: {correction_id})")
        else:
            print(f"  ❌ Échec de l'enregistrement de la correction {i+1}")
    
    print(f"\n📊 Total des corrections enregistrées: {len(correction_ids)}")
    
    # Test 2: Vérifier les suggestions de correction
    print("\n🎯 Test 2: Suggestions de correction basées sur l'apprentissage")
    print("-" * 50)
    
    suggestion_tests = [
        {"detected": "8", "confidence": 0.70, "region": "card_1"},
        {"detected": "K", "confidence": 0.80, "region": "card_2"},
        {"detected": "9", "confidence": 0.65, "region": "hand_card_1"},
        {"detected": "J", "confidence": 0.90, "region": "card_4"},  # Pas de règle
    ]
    
    for test in suggestion_tests:
        suggestion = learning_system.get_suggested_correction(
            test["detected"], 
            test["confidence"], 
            test["region"]
        )
        
        if suggestion:
            print(f"  💡 Suggestion pour '{test['detected']}' (conf: {test['confidence']}): → '{suggestion}'")
        else:
            print(f"  ℹ️  Aucune suggestion pour '{test['detected']}' (conf: {test['confidence']})")
    
    # Test 3: Statistiques d'apprentissage
    print("\n📈 Test 3: Statistiques d'apprentissage")
    print("-" * 50)
    
    stats = learning_system.get_learning_statistics()
    
    print(f"  📊 Total des corrections: {stats['total_corrections']}")
    print(f"  🔄 Corrections de session: {stats['session_corrections']}")
    print(f"  ⏱️  Temps de session: {stats['session_time_minutes']:.1f} minutes")
    print(f"  🧠 Règles adaptatives: {stats['adaptive_rules_count']}")
    print(f"  🎯 Régions avec corrections: {len(stats['regions_with_corrections'])}")
    
    if stats['most_common_errors']:
        print(f"  ❌ Erreurs les plus communes:")
        for error, count in stats['most_common_errors'].items():
            print(f"     • {error}: {count} fois")
    
    # Test 4: Vérifier la persistance des données
    print("\n💾 Test 4: Persistance des données")
    print("-" * 50)
    
    # Créer une nouvelle instance pour tester le chargement
    learning_system2 = LearningSystem("test_learning_data")
    
    stats2 = learning_system2.get_learning_statistics()
    
    if stats2['total_corrections'] == stats['total_corrections']:
        print(f"  ✅ Données chargées correctement: {stats2['total_corrections']} corrections")
    else:
        print(f"  ❌ Problème de persistance: {stats2['total_corrections']} vs {stats['total_corrections']}")
    
    # Test 5: Vérifier les fichiers créés
    print("\n📁 Test 5: Fichiers créés")
    print("-" * 50)
    
    data_dir = "test_learning_data"
    expected_files = [
        "corrections.json",
        "statistics.json", 
        "adaptive_rules.json"
    ]
    
    for filename in expected_files:
        filepath = os.path.join(data_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"  ✅ {filename}: {size} bytes")
        else:
            print(f"  ❌ {filename}: fichier manquant")
    
    # Vérifier le dossier d'images
    images_dir = os.path.join(data_dir, "images")
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) if f.endswith('.jpg')]
        print(f"  ✅ Images sauvegardées: {len(image_files)} fichiers")
    else:
        print(f"  ❌ Dossier d'images manquant")
    
    print("\n🎉 Test terminé avec succès!")
    print("\n📝 Résumé des fonctionnalités testées:")
    print("  ✅ Enregistrement des corrections avec images")
    print("  ✅ Création de règles adaptatives")
    print("  ✅ Suggestions de correction intelligentes")
    print("  ✅ Statistiques d'apprentissage en temps réel")
    print("  ✅ Persistance des données")
    print("  ✅ Sauvegarde des images de régions")
    
    print(f"\n🚀 Le système d'apprentissage est prêt à améliorer la détection !")
    print(f"📂 Données stockées dans: {os.path.abspath(data_dir)}")

if __name__ == "__main__":
    test_learning_system()
