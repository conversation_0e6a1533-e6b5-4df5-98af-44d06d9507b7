#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la correction de l'évaluation des mains
Situation : 2♠ 7♦ en main, 8♥ 5♥ 2♥ Q♥ au board
Résultat attendu : Paire de 2 (pas "Quinte possible")
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight

def test_situation_utilisateur():
    """Test de la situation exacte de l'utilisateur"""
    print("🧪 TEST DE LA SITUATION UTILISATEUR")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Situation exacte de l'utilisateur
    hand_cards = ["2s", "7d"]  # 2♠ 7♦
    board_cards = ["8h", "5h", "2h", "Qh"]  # 8♥ 5♥ 2♥ Q♥
    
    print("📊 SITUATION:")
    print(f"   Main : {' '.join(hand_cards)} (2♠ 7♦)")
    print(f"   Board : {' '.join(board_cards)} (8♥ 5♥ 2♥ Q♥)")
    print(f"   Attendu : Paire de 2")
    
    # Évaluer la main
    hand_strength = advisor.evaluate_hand_strength(board_cards, hand_cards)
    
    print(f"\n🎯 RÉSULTAT:")
    print(f"   Force détectée : {hand_strength}")
    
    # Vérifications
    if "Paire" in hand_strength:
        print("   ✅ CORRECT : Paire détectée")
        if "2" in hand_strength:
            print("   ✅ PARFAIT : Paire de 2 détectée")
        else:
            print("   ⚠️ ATTENTION : Paire détectée mais pas de 2")
    elif "Quinte" in hand_strength:
        print("   ❌ ERREUR : Quinte détectée au lieu de paire")
    else:
        print(f"   ❌ ERREUR : {hand_strength} détecté au lieu de paire")
    
    # Test de l'équité
    equity = advisor.estimate_equity(board_cards, hand_cards)
    print(f"\n📊 ÉQUITÉ:")
    print(f"   Équité estimée : {equity[0]}-{equity[1]}%")
    
    return hand_strength

def test_autres_situations():
    """Test d'autres situations pour vérifier la logique"""
    print("\n🧪 TEST D'AUTRES SITUATIONS")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    test_cases = [
        {
            "name": "Paire d'As",
            "hand": ["As", "Kd"],
            "board": ["Ah", "5h", "2h", "Qh"],
            "expected": "Paire"
        },
        {
            "name": "Deux paires",
            "hand": ["As", "Kd"],
            "board": ["Ah", "Kh", "2h", "Qh"],
            "expected": "Deux paires"
        },
        {
            "name": "Brelan",
            "hand": ["As", "Ad"],
            "board": ["Ah", "5h", "2h", "Qh"],
            "expected": "Brelan"
        },
        {
            "name": "Couleur",
            "hand": ["Ah", "Kh"],
            "board": ["5h", "2h", "Qh", "7h"],
            "expected": "Couleur"
        },
        {
            "name": "Quinte réelle",
            "hand": ["6s", "9d"],
            "board": ["5h", "7h", "8h", "Th"],
            "expected": "Quinte"
        },
        {
            "name": "Tirage quinte",
            "hand": ["6s", "9d"],
            "board": ["5h", "7h", "8h", "2h"],
            "expected": "Tirage quinte"
        },
        {
            "name": "Hauteur seulement",
            "hand": ["As", "Kd"],
            "board": ["5h", "7h", "9h", "2c"],
            "expected": "Hauteur"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 {test_case['name']}:")
        print(f"   Main : {' '.join(test_case['hand'])}")
        print(f"   Board : {' '.join(test_case['board'])}")
        
        result = advisor.evaluate_hand_strength(test_case['board'], test_case['hand'])
        expected = test_case['expected']
        
        if expected in result:
            print(f"   ✅ CORRECT : {result}")
        else:
            print(f"   ❌ ERREUR : {result} (attendu: {expected})")

def test_check_straight_possibility():
    """Test spécifique de la fonction check_straight_possibility"""
    print("\n🧪 TEST DE LA FONCTION CHECK_STRAIGHT_POSSIBILITY")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    test_cases = [
        {
            "name": "Quinte faite (9-K)",
            "cards": ["9s", "Td", "Jh", "Qh", "Kh"],
            "expected": "Quinte"
        },
        {
            "name": "Wheel faite (A-5)",
            "cards": ["As", "2d", "3h", "4h", "5h"],
            "expected": "Quinte"
        },
        {
            "name": "Tirage quinte (manque 1)",
            "cards": ["9s", "Td", "Jh", "Qh", "2h"],
            "expected": "Tirage quinte"
        },
        {
            "name": "Pas de quinte",
            "cards": ["2s", "7d", "8h", "5h", "Qh"],
            "expected": "Hauteur"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 {test_case['name']}:")
        print(f"   Cartes : {' '.join(test_case['cards'])}")
        
        result = advisor.check_straight_possibility(test_case['cards'])
        expected = test_case['expected']
        
        if result == expected:
            print(f"   ✅ CORRECT : {result}")
        else:
            print(f"   ❌ ERREUR : {result} (attendu: {expected})")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS D'ÉVALUATION CORRECTE")
    print("=" * 60)
    
    # Test principal : situation utilisateur
    result = test_situation_utilisateur()
    
    # Tests supplémentaires
    test_autres_situations()
    
    # Test fonction spécifique
    test_check_straight_possibility()
    
    print("\n✅ TESTS TERMINÉS")
    
    # Résumé
    print(f"\n📋 RÉSUMÉ:")
    print(f"   Situation utilisateur : {result}")
    if "Paire" in result:
        print("   ✅ PROBLÈME RÉSOLU : Paire correctement détectée")
    else:
        print("   ❌ PROBLÈME PERSISTANT : Paire non détectée")
