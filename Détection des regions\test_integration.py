#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'intégration de la logique avancée dans le conseiller poker.
"""

import sys
import os

def test_integration():
    """Test de l'intégration complète"""
    print("🧪 TEST D'INTÉGRATION DE LA LOGIQUE AVANCÉE")
    print("=" * 60)
    
    try:
        # Test 1: Import de la logique avancée
        print("\n🔍 Test 1: Import de la logique avancée")
        try:
            from poker_advisor_integration import poker_integration
            print("   ✅ Import réussi")
        except ImportError as e:
            print(f"   ❌ Erreur d'import: {e}")
            return False
        
        # Test 2: Test de l'évaluation d'une main simple
        print("\n🔍 Test 2: Évaluation d'une main simple")
        hand_values = ["As", "Roi"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["Dame", "Valet", "10"]
        board_suits = ["Trèfle", "Carreau", "Cœur"]
        
        try:
            result = poker_integration.evaluate_hand_advanced(
                hand_values, hand_suits, board_values, board_suits
            )
            
            print(f"   Main: {result['hand_description']}")
            print(f"   Équité: {result['equity']:.1f}%")
            print(f"   Action: {result['recommendations']['action']}")
            print(f"   Raison: {result['recommendations']['reason']}")
            print("   ✅ Évaluation réussie")
            
        except Exception as e:
            print(f"   ❌ Erreur d'évaluation: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 3: Test avec tirages
        print("\n🔍 Test 3: Test avec tirages de couleur")
        hand_values = ["As", "Roi"]
        hand_suits = ["Cœur", "Cœur"]
        board_values = ["7", "3", "9"]
        board_suits = ["Cœur", "Cœur", "Pique"]
        
        try:
            result = poker_integration.evaluate_hand_advanced(
                hand_values, hand_suits, board_values, board_suits
            )
            
            print(f"   Main: {result['hand_description']}")
            print(f"   Équité: {result['equity']:.1f}%")
            print(f"   Tirages détectés:")
            for draw_type, draw_info in result['draws'].items():
                if draw_type not in ['total_outs', 'clean_outs'] and draw_info.get('possible', False):
                    print(f"     - {draw_info['description']}")
            print(f"   Action: {result['recommendations']['action']}")
            print("   ✅ Test des tirages réussi")
            
        except Exception as e:
            print(f"   ❌ Erreur test tirages: {e}")
            return False
        
        # Test 4: Test preflop
        print("\n🔍 Test 4: Test preflop")
        hand_values = ["As", "As"]
        hand_suits = ["Cœur", "Pique"]
        board_values = []
        board_suits = []
        
        try:
            result = poker_integration.evaluate_hand_advanced(
                hand_values, hand_suits, board_values, board_suits
            )
            
            print(f"   Main preflop: AA")
            print(f"   Équité: {result['equity']:.1f}%")
            print(f"   Action: {result['recommendations']['action']}")
            
            if result['equity'] > 80:
                print("   ✅ Équité preflop correcte pour AA")
            else:
                print(f"   ❌ Équité trop faible pour AA: {result['equity']:.1f}%")
                return False
            
        except Exception as e:
            print(f"   ❌ Erreur test preflop: {e}")
            return False
        
        # Test 5: Test du formatage
        print("\n🔍 Test 5: Test du formatage d'analyse")
        try:
            formatted_text = poker_integration.format_analysis_text(result)
            print(f"   Texte formaté:")
            print(f"   {formatted_text}")
            print("   ✅ Formatage réussi")
            
        except Exception as e:
            print(f"   ❌ Erreur formatage: {e}")
            return False
        
        print("\n🎉 TOUS LES TESTS D'INTÉGRATION SONT RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur générale lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison():
    """Compare l'ancienne et la nouvelle logique"""
    print("\n\n🔄 COMPARAISON ANCIENNE VS NOUVELLE LOGIQUE")
    print("=" * 60)
    
    # Cas de test problématiques identifiés
    test_cases = [
        {
            "name": "Fausse quinte détectée",
            "hand": ["5", "3"],
            "hand_suits": ["Cœur", "Pique"],
            "board": ["Roi", "Dame", "Valet", "10", "9"],
            "board_suits": ["Trèfle", "Carreau", "Cœur", "Pique", "Trèfle"]
        },
        {
            "name": "Tirage de couleur",
            "hand": ["As", "Roi"],
            "hand_suits": ["Cœur", "Cœur"],
            "board": ["7", "3", "9"],
            "board_suits": ["Cœur", "Cœur", "Pique"]
        },
        {
            "name": "Main faible (7-2)",
            "hand": ["7", "2"],
            "hand_suits": ["Cœur", "Pique"],
            "board": [],
            "board_suits": []
        }
    ]
    
    try:
        from poker_advisor_integration import poker_integration
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}: {test_case['name']}")
            print(f"   Main: {test_case['hand']} de {test_case['hand_suits']}")
            print(f"   Board: {test_case['board']} de {test_case['board_suits']}")
            
            try:
                result = poker_integration.evaluate_hand_advanced(
                    test_case['hand'], test_case['hand_suits'],
                    test_case['board'], test_case['board_suits']
                )
                
                print(f"   ✅ Nouvelle logique:")
                print(f"      Main: {result['hand_description']}")
                print(f"      Équité: {result['equity']:.1f}%")
                print(f"      Action: {result['recommendations']['action']}")
                
                # Vérifier les tirages
                draws_found = []
                for draw_type, draw_info in result['draws'].items():
                    if draw_type not in ['total_outs', 'clean_outs'] and draw_info.get('possible', False):
                        draws_found.append(draw_info['description'])
                
                if draws_found:
                    print(f"      Tirages: {', '.join(draws_found)}")
                else:
                    print(f"      Tirages: Aucun")
                
            except Exception as e:
                print(f"   ❌ Erreur nouvelle logique: {e}")
        
        print("\n🎯 COMPARAISON TERMINÉE")
        
    except ImportError:
        print("   ⚠️ Logique avancée non disponible pour la comparaison")

def main():
    """Fonction principale de test"""
    print("🚀 TESTS D'INTÉGRATION DE LA LOGIQUE AVANCÉE DE POKER")
    print("=" * 70)
    
    success = test_integration()
    
    if success:
        test_comparison()
        print("\n✅ INTÉGRATION RÉUSSIE!")
        print("\nLa logique avancée est maintenant intégrée et fonctionnelle.")
        print("Vous pouvez maintenant lancer l'application principale avec:")
        print("   python detector_gui.py")
        print("\nLes améliorations incluent:")
        print("   ✅ Détection correcte des combinaisons")
        print("   ✅ Calcul précis des outs et tirages")
        print("   ✅ Équité réaliste")
        print("   ✅ Recommandations intelligentes")
        print("   ✅ Gestion des cartes déjà sorties")
    else:
        print("\n❌ INTÉGRATION ÉCHOUÉE")
        print("Veuillez vérifier les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
