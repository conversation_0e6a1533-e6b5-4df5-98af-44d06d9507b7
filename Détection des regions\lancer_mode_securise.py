#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur en mode sécurisé pour identifier la source exacte du crash.
"""

import os
import sys
import subprocess
import time

def test_logique_avancee_seule():
    """Test de la logique avancée seule pour confirmer qu'elle fonctionne"""
    print("🧪 TEST DE LA LOGIQUE AVANCÉE SEULE")
    print("=" * 50)
    
    try:
        # Test simple de la logique avancée
        from poker_advisor_integration import poker_integration
        
        print("✅ Import réussi")
        
        # Test avec des cartes simples
        result = poker_integration.evaluate_hand_advanced(
            ["As", "Roi"], ["Cœur", "Pique"],
            ["Dame", "Valet", "10"], ["Trèfle", "Carreau", "Cœur"]
        )
        
        print(f"✅ Résultat: {result['hand_description']}")
        print(f"✅ Équité: {result['equity']:.1f}%")
        print(f"✅ Action: {result['recommendations']['action']}")
        
        # Test de stress (50 appels)
        print("\n🔥 Test de stress (50 appels)...")
        for i in range(50):
            result = poker_integration.evaluate_hand_advanced(
                ["As", "Roi"], ["Cœur", "Pique"],
                ["Dame", "Valet", "10"], ["Trèfle", "Carreau", "Cœur"]
            )
            if i % 10 == 0:
                print(f"   Appel {i+1}/50 OK")
        
        print("✅ Logique avancée fonctionne parfaitement !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur logique avancée: {e}")
        return False

def test_interface_minimale():
    """Test de l'interface PyQt5 minimale"""
    print("\n🖥️ TEST DE L'INTERFACE MINIMALE")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt5.QtCore import QTimer
        import sys
        
        app = QApplication(sys.argv)
        
        # Fenêtre minimale
        window = QMainWindow()
        window.setWindowTitle("Test Interface Minimale")
        window.setGeometry(100, 100, 400, 200)
        
        label = QLabel("Interface PyQt5 fonctionne !", window)
        label.move(50, 50)
        
        # Timer pour fermer automatiquement
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3 secondes
        
        window.show()
        
        print("✅ Interface PyQt5 lancée (fermeture auto dans 3s)")
        app.exec_()
        
        print("✅ Interface PyQt5 fonctionne !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur interface PyQt5: {e}")
        return False

def test_capture_ecran():
    """Test de la capture d'écran seule"""
    print("\n📸 TEST DE LA CAPTURE D'ÉCRAN")
    print("=" * 50)
    
    try:
        import mss
        import numpy as np
        
        # Test de capture simple
        with mss.mss() as sct:
            monitor = sct.monitors[1]  # Écran principal
            screenshot = sct.grab(monitor)
            
            # Convertir en numpy array
            img = np.array(screenshot)
            
            print(f"✅ Capture réussie: {img.shape}")
            
            # Test de stress (10 captures)
            print("🔥 Test de stress (10 captures)...")
            for i in range(10):
                screenshot = sct.grab(monitor)
                img = np.array(screenshot)
                if i % 5 == 0:
                    print(f"   Capture {i+1}/10 OK")
            
            print("✅ Capture d'écran fonctionne !")
            return True
            
    except Exception as e:
        print(f"❌ Erreur capture d'écran: {e}")
        return False

def test_ocr_simple():
    """Test de l'OCR simple"""
    print("\n🔤 TEST DE L'OCR SIMPLE")
    print("=" * 50)
    
    try:
        import cv2
        import numpy as np
        
        # Créer une image de test simple
        img = np.ones((100, 200, 3), dtype=np.uint8) * 255  # Image blanche
        cv2.putText(img, "TEST", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Test PaddleOCR
        try:
            from paddleocr import PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)
            result = ocr.ocr(img, cls=True)
            print("✅ PaddleOCR fonctionne")
        except Exception as e:
            print(f"⚠️ PaddleOCR problématique: {e}")
        
        # Test EasyOCR
        try:
            import easyocr
            reader = easyocr.Reader(['en'], gpu=False)
            result = reader.readtext(img)
            print("✅ EasyOCR fonctionne")
        except Exception as e:
            print(f"⚠️ EasyOCR problématique: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur OCR: {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🔍 DIAGNOSTIC COMPLET DU CRASH")
    print("=" * 70)
    
    # Test 1: Logique avancée seule
    logique_ok = test_logique_avancee_seule()
    
    # Test 2: Interface PyQt5 minimale
    interface_ok = test_interface_minimale()
    
    # Test 3: Capture d'écran
    capture_ok = test_capture_ecran()
    
    # Test 4: OCR
    ocr_ok = test_ocr_simple()
    
    print("\n📋 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 50)
    print(f"✅ Logique avancée: {'OK' if logique_ok else 'PROBLÈME'}")
    print(f"✅ Interface PyQt5: {'OK' if interface_ok else 'PROBLÈME'}")
    print(f"✅ Capture d'écran: {'OK' if capture_ok else 'PROBLÈME'}")
    print(f"✅ OCR: {'OK' if ocr_ok else 'PROBLÈME'}")
    
    print("\n🎯 CONCLUSION:")
    if logique_ok and interface_ok and capture_ok and ocr_ok:
        print("✅ Tous les composants fonctionnent individuellement")
        print("❓ Le problème vient de l'interaction entre les composants")
        print("💡 Solution: Réduire la fréquence de capture ou optimiser les threads")
    else:
        problemes = []
        if not logique_ok:
            problemes.append("Logique avancée")
        if not interface_ok:
            problemes.append("Interface PyQt5")
        if not capture_ok:
            problemes.append("Capture d'écran")
        if not ocr_ok:
            problemes.append("OCR")
        
        print(f"❌ Composants problématiques: {', '.join(problemes)}")
        print("💡 Solution: Corriger ces composants avant de relancer l'app complète")
    
    print("\n🔧 RECOMMANDATIONS:")
    print("1. Si tous les tests passent: Réduire l'intervalle de capture à 2-3 secondes")
    print("2. Si OCR pose problème: Désactiver temporairement EasyOCR")
    print("3. Si interface pose problème: Vérifier les drivers graphiques")
    print("4. Si capture pose problème: Utiliser une résolution plus faible")
    
    input("\nAppuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    main()
