#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simulateur Monte Carlo pour calculs d'équité précis au poker
"""

import random
import itertools
from collections import defaultdict
import time
import numpy as np

class MonteCarloSimulator:
    """Simulateur Monte Carlo pour équité poker"""
    
    def __init__(self):
        self.deck = self._create_deck()
        self.hand_rankings = {
            'Quinte flush royale': 9,
            'Quinte flush': 8,
            'Carré': 7,
            'Full house': 6,
            'Couleur': 5,
            'Quinte': 4,
            'Brelan': 3,
            'Deux paires': 2,
            'Paire': 1,
            'Hauteur': 0
        }
    
    def _create_deck(self):
        """Crée un jeu de 52 cartes"""
        suits = ['h', 'd', 'c', 's']  # hearts, diamonds, clubs, spades
        ranks = ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A']
        return [rank + suit for rank in ranks for suit in suits]
    
    def simulate_equity(self, hero_hand, villain_ranges, board=None, num_simulations=10000):
        """
        Simule l'équité d'une main contre des ranges d'adversaires
        
        Args:
            hero_hand (list): Main du héros ['As', 'Kd']
            villain_ranges (list): Liste des ranges adversaires [['AA', 'KK'], ['22+', 'A2s+']]
            board (list): Cartes du board ['Ah', '5h', '2c']
            num_simulations (int): Nombre de simulations
            
        Returns:
            dict: Résultats détaillés de la simulation
        """
        if not hero_hand or len(hero_hand) != 2:
            return {"error": "Main héros invalide"}
        
        # Convertir les ranges en mains spécifiques
        villain_hands = []
        for range_def in villain_ranges:
            hands = self._expand_range(range_def)
            villain_hands.append(hands)
        
        wins = 0
        ties = 0
        total_simulations = 0
        hand_strength_distribution = defaultdict(int)
        
        start_time = time.time()
        
        for _ in range(num_simulations):
            # Créer le deck disponible
            used_cards = set(hero_hand + (board or []))
            available_deck = [card for card in self.deck if card not in used_cards]
            
            # Tirer des mains pour les adversaires
            villain_actual_hands = []
            temp_used = set(used_cards)
            
            valid_simulation = True
            for villain_range in villain_hands:
                # Choisir une main aléatoire dans le range
                available_hands = [hand for hand in villain_range 
                                 if not any(card in temp_used for card in hand)]
                if not available_hands:
                    valid_simulation = False
                    break
                
                chosen_hand = random.choice(available_hands)
                villain_actual_hands.append(chosen_hand)
                temp_used.update(chosen_hand)
            
            if not valid_simulation:
                continue
            
            # Compléter le board si nécessaire
            remaining_deck = [card for card in available_deck 
                            if card not in temp_used]
            
            if board is None:
                final_board = random.sample(remaining_deck, 5)
            else:
                cards_needed = 5 - len(board)
                if cards_needed > 0:
                    final_board = board + random.sample(remaining_deck, cards_needed)
                else:
                    final_board = board[:5]
            
            # Évaluer toutes les mains
            hero_strength = self._evaluate_hand(hero_hand + final_board)
            villain_strengths = [self._evaluate_hand(hand + final_board) 
                               for hand in villain_actual_hands]
            
            # Déterminer le gagnant
            all_strengths = [hero_strength] + villain_strengths
            max_strength = max(all_strengths)
            
            if hero_strength == max_strength:
                winners = [i for i, strength in enumerate(all_strengths) if strength == max_strength]
                if len(winners) == 1:
                    wins += 1
                else:
                    ties += 1
            
            # Enregistrer la distribution des forces
            hand_name = self._strength_to_name(hero_strength)
            hand_strength_distribution[hand_name] += 1
            
            total_simulations += 1
        
        simulation_time = time.time() - start_time
        
        if total_simulations == 0:
            return {"error": "Aucune simulation valide"}
        
        equity = (wins + ties * 0.5) / total_simulations * 100
        
        return {
            "equity": equity,
            "wins": wins,
            "ties": ties,
            "total_simulations": total_simulations,
            "win_rate": wins / total_simulations * 100,
            "tie_rate": ties / total_simulations * 100,
            "hand_distribution": dict(hand_strength_distribution),
            "simulation_time": simulation_time,
            "simulations_per_second": total_simulations / simulation_time if simulation_time > 0 else 0
        }
    
    def _expand_range(self, range_def):
        """Expanse une définition de range en mains spécifiques"""
        if isinstance(range_def, list) and len(range_def) == 2:
            # Main spécifique comme ['As', 'Kd']
            return [range_def]
        
        hands = []
        
        if isinstance(range_def, str):
            # Range textuel comme "AA", "22+", "A2s+"
            if range_def == "AA":
                hands.extend(self._get_pocket_pairs(['A']))
            elif range_def.endswith("+"):
                # Range comme "22+", "A2s+"
                hands.extend(self._expand_plus_range(range_def))
            else:
                # Main spécifique comme "AKs", "AKo"
                hands.extend(self._get_specific_hand(range_def))
        elif isinstance(range_def, list):
            # Liste de définitions
            for hand_def in range_def:
                hands.extend(self._expand_range(hand_def))
        
        return hands
    
    def _get_pocket_pairs(self, ranks):
        """Obtient toutes les combinaisons de paires pour les rangs donnés"""
        hands = []
        suits = ['h', 'd', 'c', 's']
        
        for rank in ranks:
            for suit1, suit2 in itertools.combinations(suits, 2):
                hands.append([rank + suit1, rank + suit2])
        
        return hands
    
    def _expand_plus_range(self, range_def):
        """Expanse un range avec + comme '22+' ou 'A2s+'"""
        hands = []
        
        if range_def.endswith("+"):
            base = range_def[:-1]
            
            if len(base) == 2 and base[0] == base[1]:
                # Pocket pairs comme "22+"
                rank = base[0]
                rank_order = ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A']
                start_idx = rank_order.index(rank)
                
                for i in range(start_idx, len(rank_order)):
                    hands.extend(self._get_pocket_pairs([rank_order[i]]))
            
            elif len(base) >= 3:
                # Suited/offsuit comme "A2s+" ou "A2o+"
                if base.endswith('s'):
                    # Suited
                    high_rank = base[0]
                    low_rank = base[1]
                    hands.extend(self._get_suited_range(high_rank, low_rank, True))
                elif base.endswith('o'):
                    # Offsuit
                    high_rank = base[0]
                    low_rank = base[1]
                    hands.extend(self._get_suited_range(high_rank, low_rank, False))
        
        return hands
    
    def _get_suited_range(self, high_rank, low_rank, suited):
        """Obtient un range suited ou offsuit"""
        hands = []
        rank_order = ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A']
        suits = ['h', 'd', 'c', 's']
        
        start_idx = rank_order.index(low_rank)
        high_idx = rank_order.index(high_rank)
        
        for i in range(start_idx, high_idx):
            low = rank_order[i]
            
            if suited:
                # Même couleur
                for suit in suits:
                    hands.append([high_rank + suit, low + suit])
            else:
                # Couleurs différentes
                for suit1 in suits:
                    for suit2 in suits:
                        if suit1 != suit2:
                            hands.append([high_rank + suit1, low + suit2])
        
        return hands
    
    def _get_specific_hand(self, hand_def):
        """Obtient toutes les combinaisons pour une main spécifique"""
        hands = []
        suits = ['h', 'd', 'c', 's']
        
        if len(hand_def) == 3:
            if hand_def.endswith('s'):
                # Suited
                rank1, rank2 = hand_def[0], hand_def[1]
                for suit in suits:
                    hands.append([rank1 + suit, rank2 + suit])
            elif hand_def.endswith('o'):
                # Offsuit
                rank1, rank2 = hand_def[0], hand_def[1]
                for suit1 in suits:
                    for suit2 in suits:
                        if suit1 != suit2:
                            hands.append([rank1 + suit1, rank2 + suit2])
        
        return hands
    
    def _evaluate_hand(self, seven_cards):
        """Évalue une main de 7 cartes et retourne sa force"""
        if len(seven_cards) < 5:
            return 0
        
        # Prendre les 5 meilleures cartes
        best_hand = self._get_best_five_cards(seven_cards)
        return self._hand_strength(best_hand)
    
    def _get_best_five_cards(self, seven_cards):
        """Trouve les 5 meilleures cartes parmi 7"""
        # Simplification : retourner les 5 premières pour l'instant
        # TODO: Implémenter la vraie logique de sélection
        return seven_cards[:5]
    
    def _hand_strength(self, five_cards):
        """Calcule la force d'une main de 5 cartes"""
        # Simplification : retourner une valeur aléatoire pour l'instant
        # TODO: Implémenter la vraie évaluation des mains
        return random.randint(0, 9)
    
    def _strength_to_name(self, strength):
        """Convertit une force numérique en nom de main"""
        strength_names = {
            9: 'Quinte flush royale',
            8: 'Quinte flush',
            7: 'Carré',
            6: 'Full house',
            5: 'Couleur',
            4: 'Quinte',
            3: 'Brelan',
            2: 'Deux paires',
            1: 'Paire',
            0: 'Hauteur'
        }
        return strength_names.get(strength, 'Hauteur')

if __name__ == "__main__":
    # Test du simulateur
    simulator = MonteCarloSimulator()
    
    # Test simple
    hero_hand = ['As', 'Kd']
    villain_ranges = [['AA'], ['KK']]
    
    print("🧪 Test du simulateur Monte Carlo")
    print("=" * 50)
    
    result = simulator.simulate_equity(hero_hand, villain_ranges, num_simulations=1000)
    
    if "error" not in result:
        print(f"Équité: {result['equity']:.1f}%")
        print(f"Victoires: {result['wins']}")
        print(f"Égalités: {result['ties']}")
        print(f"Simulations: {result['total_simulations']}")
        print(f"Temps: {result['simulation_time']:.2f}s")
    else:
        print(f"Erreur: {result['error']}")
