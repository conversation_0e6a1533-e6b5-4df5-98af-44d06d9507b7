#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Module de détection multi-OCR
=============================================

Module combinant plusieurs moteurs OCR pour améliorer la détection des cartes.
Utilise PaddleOCR, EasyOCR et Tesseract OCR pour une meilleure précision, notamment pour les cartes problématiques (J, Q).

Ce module permet de :
1. Détecter le texte avec plusieurs moteurs OCR
2. Analyser la forme des caractères pour confirmer les détections
3. Combiner les résultats pour une meilleure précision

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import cv2
import numpy as np
import time
import logging
import sys

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("multi_ocr_log.txt"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("MultiOCR")

# Constantes
CARD_VALUES = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
J_CONFUSIONS = ['1', 'I', 'T', 'L', 'J.', '.J', 'j', 'i', 'l', '!', '|', '7', 'F', 'f']
Q_CONFUSIONS = ['O', '0', 'D', 'o', 'd', 'q', '9', 'g', 'G', 'C', 'c']

class MultiOCRDetector:
    """Classe pour la détection de texte avec plusieurs moteurs OCR

    Cette classe combine PaddleOCR et EasyOCR pour une meilleure détection des cartes,
    en particulier pour les cartes problématiques comme J et Q.
    """

    def __init__(self, use_cuda=None):
        """Initialise le détecteur multi-OCR

        Args:
            use_cuda (bool, optional): Indique si CUDA doit être utilisé pour l'accélération GPU.
                Si None, la disponibilité de CUDA est détectée automatiquement.
        """
        self.use_cuda = self._check_cuda() if use_cuda is None else use_cuda
        self.paddle_ocr = None
        self.easy_ocr = None
        self.tesseract_ocr = None

        # Initialiser PaddleOCR
        self._init_paddle_ocr()

        # Initialiser EasyOCR (installation à la demande)
        self._init_easy_ocr()

        # Initialiser Tesseract OCR (installation à la demande)
        self._init_tesseract_ocr()

        logger.info("MultiOCRDetector initialisé avec CUDA: %s", self.use_cuda)

    def _check_cuda(self):
        """Vérifie si CUDA est disponible

        Returns:
            bool: True si CUDA est disponible, False sinon
        """
        try:
            import torch
            cuda_available = torch.cuda.is_available()
            if cuda_available:
                logger.info("CUDA détecté: %s", torch.cuda.get_device_name(0))
                logger.info("Mémoire CUDA disponible: %.2f Go",
                           torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024)
                return True
            else:
                logger.warning("CUDA non disponible, utilisation du CPU")
                return False
        except ImportError:
            logger.warning("PyTorch non installé, impossible de détecter CUDA")
            return False
        except Exception as e:
            logger.error("Erreur lors de la détection de CUDA: %s", e)
            return False

    def _init_paddle_ocr(self):
        """Initialise PaddleOCR"""
        try:
            from paddleocr import PaddleOCR

            # Vérifier si PaddlePaddle est compilé avec CUDA
            try:
                import paddle
                paddle_cuda = paddle.device.is_compiled_with_cuda()
                if paddle_cuda and self.use_cuda:
                    paddle.device.set_device('gpu:0')
                    logger.info("PaddlePaddle configuré pour utiliser le GPU")
                else:
                    logger.warning("PaddlePaddle non compilé avec CUDA ou CUDA désactivé")
            except ImportError:
                logger.warning("PaddlePaddle non importable")

            # Initialiser PaddleOCR avec les paramètres optimisés
            if self.use_cuda:
                self.paddle_ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='fr',
                    use_gpu=True,
                    show_log=False,
                    enable_mkldnn=False,
                    use_mp=False,
                    use_tensorrt=False,
                    gpu_mem=2000
                )
                logger.info("PaddleOCR initialisé avec GPU")
            else:
                self.paddle_ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='fr',
                    use_gpu=False,
                    show_log=False,
                    enable_mkldnn=True,
                    use_mp=True
                )
                logger.info("PaddleOCR initialisé avec CPU optimisé")
        except Exception as e:
            logger.error("Erreur lors de l'initialisation de PaddleOCR: %s", e)
            self.paddle_ocr = None

    def _init_easy_ocr(self):
        """Initialise EasyOCR (installation à la demande)"""
        try:
            import easyocr
            self.easy_ocr = easyocr.Reader(['en'], gpu=self.use_cuda, quantize=False)
            logger.info("EasyOCR initialisé avec GPU: %s", self.use_cuda)
        except ImportError:
            logger.warning("EasyOCR non installé. Installation recommandée: pip install easyocr")
            self.easy_ocr = None
        except Exception as e:
            logger.error("Erreur lors de l'initialisation d'EasyOCR: %s", e)
            self.easy_ocr = None

    def _init_tesseract_ocr(self):
        """Initialise Tesseract OCR (installation à la demande)"""
        try:
            import pytesseract

            # Vérifier si Tesseract est installé
            try:
                version = pytesseract.get_tesseract_version()
                logger.info("Tesseract OCR version: %s", version)

                # Configurer Tesseract OCR
                # Paramètres optimisés pour la détection des cartes
                custom_config = r'--oem 1 --psm 10 -c tessedit_char_whitelist=AKQJ1098765432'

                # Stocker la configuration dans l'objet
                self.tesseract_ocr = {
                    'engine': pytesseract,
                    'config': custom_config
                }

                logger.info("Tesseract OCR initialisé avec succès")
            except Exception as e:
                logger.error("Erreur lors de la vérification de Tesseract OCR: %s", e)
                logger.warning("Tesseract OCR est installé mais ne fonctionne pas correctement")
                self.tesseract_ocr = None
        except ImportError:
            logger.warning("pytesseract non installé. Installation recommandée: python install_tesseract.py")
            self.tesseract_ocr = None
        except Exception as e:
            logger.error("Erreur lors de l'initialisation de Tesseract OCR: %s", e)
            self.tesseract_ocr = None

    def preprocess_image(self, image, is_hand_card=False):
        """Prétraite l'image pour améliorer la détection OCR

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à prétraiter
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            dict: Dictionnaire contenant différentes versions prétraitées de l'image
        """
        try:
            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                logger.warning("Image invalide fournie au prétraitement")
                return {"original": image}

            # Convertir en niveaux de gris
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Appliquer un flou gaussien pour réduire le bruit
            blur_size = 3
            blurred = cv2.GaussianBlur(gray, (blur_size, blur_size), 0)

            # Améliorer le contraste
            clip_limit = 3.5 if is_hand_card else 2.5
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(8, 8))
            enhanced = clahe.apply(blurred)

            # Redimensionner si nécessaire
            h, w = enhanced.shape
            min_size = 120 if is_hand_card else 60
            if h < min_size or w < min_size:
                scale_factor = max(min_size / h, min_size / w)
                enhanced = cv2.resize(enhanced, (0, 0), fx=scale_factor, fy=scale_factor,
                                     interpolation=cv2.INTER_CUBIC)

            # Version standard (binarisation adaptative)
            block_size = 9 if is_hand_card else 11
            c_value = 1 if is_hand_card else 2
            binary = cv2.adaptiveThreshold(
                enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, block_size, c_value
            )

            # Version inversée (meilleure pour les fonds sombres)
            _, binary_inv = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # Version spéciale pour Q
            threshold_q = 110 if is_hand_card else 127
            _, binary_q = cv2.threshold(enhanced, threshold_q, 255, cv2.THRESH_BINARY)
            kernel_circle = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            closed_q = cv2.morphologyEx(binary_q, cv2.MORPH_CLOSE, kernel_circle,
                                       iterations=2 if is_hand_card else 1)

            # Version spéciale pour J
            # Version 1: Binarisation standard avec dilatation verticale et horizontale
            _, binary_j1 = cv2.threshold(enhanced, 100, 255, cv2.THRESH_BINARY)
            kernel_j_vertical = np.ones((3, 1), np.uint8)
            dilated_j_vertical = cv2.dilate(binary_j1, kernel_j_vertical, iterations=2)
            kernel_j_horizontal = np.ones((1, 3), np.uint8)
            dilated_j1 = cv2.dilate(dilated_j_vertical, kernel_j_horizontal, iterations=1)

            # Version 2: Binarisation inversée pour capturer les contours
            _, binary_j2 = cv2.threshold(enhanced, 120, 255, cv2.THRESH_BINARY_INV)
            kernel_j_thin = np.ones((2, 2), np.uint8)
            eroded_j = cv2.erode(binary_j2, kernel_j_thin, iterations=1)
            dilated_j2 = cv2.dilate(eroded_j, kernel_j_thin, iterations=1)

            # Combiner les deux versions pour J
            combined_j = cv2.bitwise_or(dilated_j1, dilated_j2)
            filtered_j = cv2.medianBlur(combined_j, 3)

            # Convertir toutes les versions en BGR pour les OCR
            processed = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
            processed_inv = cv2.cvtColor(binary_inv, cv2.COLOR_GRAY2BGR)
            processed_q = cv2.cvtColor(closed_q, cv2.COLOR_GRAY2BGR)
            processed_j = cv2.cvtColor(filtered_j, cv2.COLOR_GRAY2BGR)

            # Retourner toutes les versions prétraitées
            return {
                "original": image,
                "gray": gray,
                "enhanced": enhanced,
                "standard": processed,
                "inverted": processed_inv,
                "q_special": processed_q,
                "j_special": processed_j
            }
        except Exception as e:
            logger.error("Erreur lors du prétraitement de l'image: %s", e)
            return {"original": image}

    def detect_with_paddle(self, images):
        """Détecte le texte avec PaddleOCR sur plusieurs versions de l'image

        Args:
            images (dict): Dictionnaire contenant différentes versions prétraitées de l'image

        Returns:
            dict: Résultats de la détection pour chaque version de l'image
        """
        if self.paddle_ocr is None:
            logger.warning("PaddleOCR n'est pas disponible")
            return {}

        results = {}
        for name, img in images.items():
            try:
                paddle_result = self.paddle_ocr.ocr(img, cls=True)

                # Extraire le texte des résultats
                text = ""
                if paddle_result and len(paddle_result) > 0 and paddle_result[0]:
                    for line in paddle_result[0]:
                        text += line[1][0] + " "
                    text = text.strip()

                results[name] = text
            except Exception as e:
                logger.error("Erreur lors de la détection avec PaddleOCR (%s): %s", name, e)
                results[name] = ""

        return results

    def detect_with_easyocr(self, images):
        """Détecte le texte avec EasyOCR sur plusieurs versions de l'image

        Args:
            images (dict): Dictionnaire contenant différentes versions prétraitées de l'image

        Returns:
            dict: Résultats de la détection pour chaque version de l'image
        """
        if self.easy_ocr is None:
            logger.warning("EasyOCR n'est pas disponible")
            return {}

        results = {}
        for name, img in images.items():
            try:
                # EasyOCR peut être lent, donc on limite aux versions les plus importantes
                if name not in ["original", "standard", "j_special", "q_special"]:
                    continue

                easy_result = self.easy_ocr.readtext(img, detail=0)

                # Extraire le texte des résultats
                text = " ".join(easy_result) if easy_result else ""

                results[name] = text
            except Exception as e:
                logger.error("Erreur lors de la détection avec EasyOCR (%s): %s", name, e)
                results[name] = ""

        return results

    def detect_with_tesseract(self, images):
        """Détecte le texte avec Tesseract OCR sur plusieurs versions de l'image

        Args:
            images (dict): Dictionnaire contenant différentes versions prétraitées de l'image

        Returns:
            dict: Résultats de la détection pour chaque version de l'image
        """
        if self.tesseract_ocr is None:
            logger.warning("Tesseract OCR n'est pas disponible")
            return {}

        results = {}
        for name, img in images.items():
            try:
                # Tesseract est rapide, donc on peut traiter toutes les versions
                # Mais on se concentre sur les versions les plus susceptibles de donner de bons résultats
                if name not in ["original", "standard", "enhanced", "j_special", "q_special"]:
                    continue

                # Convertir en niveaux de gris si nécessaire
                if len(img.shape) == 3:
                    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                else:
                    gray = img

                # Appliquer un redimensionnement pour améliorer la détection
                # Tesseract fonctionne mieux avec des images plus grandes
                h, w = gray.shape
                scale_factor = max(1.0, 200 / min(h, w))
                if scale_factor > 1.0:
                    resized = cv2.resize(gray, (0, 0), fx=scale_factor, fy=scale_factor,
                                        interpolation=cv2.INTER_CUBIC)
                else:
                    resized = gray

                # Détecter avec Tesseract OCR
                pytesseract = self.tesseract_ocr['engine']
                config = self.tesseract_ocr['config']

                text = pytesseract.image_to_string(resized, config=config).strip()

                # Nettoyer le texte
                text = ''.join(c for c in text if c.isalnum())

                results[name] = text
            except Exception as e:
                logger.error("Erreur lors de la détection avec Tesseract OCR (%s): %s", name, e)
                results[name] = ""

        return results

    def analyze_j_shape(self, image):
        """Analyse si l'image contient un J en se basant sur sa forme

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            bool: True si l'image contient probablement un J, False sinon
        """
        try:
            # Convertir en niveaux de gris et binariser
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

            # Trouver les contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return False

            # Trouver le plus grand contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Calculer le rectangle englobant
            x, y, w, h = cv2.boundingRect(largest_contour)

            # Calculer le ratio hauteur/largeur (le J est généralement plus haut que large)
            aspect_ratio = h / w if w > 0 else 0

            # Diviser l'image en régions
            top_region = binary[y:y+h//4, x:x+w]
            middle_region = binary[y+h//4:y+3*h//4, x:x+w]
            bottom_region = binary[y+3*h//4:y+h, x:x+w]

            # Diviser horizontalement
            left_region = binary[y:y+h, x:x+w//2]
            right_region = binary[y:y+h, x+w//2:x+w]

            # Calculer les densités
            top_density = cv2.countNonZero(top_region) / (top_region.size) if top_region.size > 0 else 0
            middle_density = cv2.countNonZero(middle_region) / (middle_region.size) if middle_region.size > 0 else 0
            bottom_density = cv2.countNonZero(bottom_region) / (bottom_region.size) if bottom_region.size > 0 else 0

            left_density = cv2.countNonZero(left_region) / (left_region.size) if left_region.size > 0 else 0
            right_density = cv2.countNonZero(right_region) / (right_region.size) if right_region.size > 0 else 0

            # Caractéristiques d'un J:
            # 1. Ratio hauteur/largeur élevé (plus haut que large)
            # 2. Densité élevée en haut (barre horizontale)
            # 3. Densité plus élevée à droite qu'à gauche (tige verticale à droite)
            # 4. Densité plus faible en bas qu'au milieu (courbure)
            is_j_shape = (
                aspect_ratio > 1.3 and
                top_density > 0.15 and
                right_density > left_density * 1.2 and
                middle_density > bottom_density
            )

            logger.info("Analyse de forme pour J: ratio=%.2f, top=%.2f, middle=%.2f, bottom=%.2f, left=%.2f, right=%.2f, is_j=%s",
                       aspect_ratio, top_density, middle_density, bottom_density, left_density, right_density, is_j_shape)

            return is_j_shape
        except Exception as e:
            logger.error("Erreur lors de l'analyse de forme du J: %s", e)
            return False

    def detect_card(self, image, is_hand_card=False):
        """Détecte la valeur d'une carte en utilisant plusieurs moteurs OCR

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            str: Valeur de la carte détectée
        """
        start_time = time.time()

        # Prétraiter l'image
        preprocessed_images = self.preprocess_image(image, is_hand_card)

        # Détecter avec PaddleOCR
        paddle_results = self.detect_with_paddle(preprocessed_images)

        # Détecter avec EasyOCR (si disponible)
        easy_results = self.detect_with_easyocr(preprocessed_images)

        # Détecter avec Tesseract OCR (si disponible)
        tesseract_results = self.detect_with_tesseract(preprocessed_images)

        # Analyser la forme pour détecter un J
        j_shape_detected = self.analyze_j_shape(preprocessed_images["j_special"])

        # Combiner les résultats
        result = self.combine_results(paddle_results, easy_results, tesseract_results, j_shape_detected)

        end_time = time.time()
        logger.info("Détection multi-OCR terminée en %.2f secondes. Résultat: %s",
                   end_time - start_time, result)

        return result

    def combine_results(self, paddle_results, easy_results, tesseract_results, j_shape_detected):
        """Combine les résultats des différents OCR pour déterminer la valeur de la carte

        Args:
            paddle_results (dict): Résultats de PaddleOCR
            easy_results (dict): Résultats d'EasyOCR
            tesseract_results (dict): Résultats de Tesseract OCR
            j_shape_detected (bool): Indique si l'analyse de forme a détecté un J

        Returns:
            str: Valeur de la carte détectée
        """
        # Si l'analyse de forme a détecté un J, c'est probablement un J
        if j_shape_detected:
            logger.info("J détecté par analyse de forme")
            return 'J'

        # Vérifier si un J est détecté dans les résultats OCR
        j_detected_paddle = any('J' in result.upper() for result in paddle_results.values())
        j_detected_easy = any('J' in result.upper() for result in easy_results.values())
        j_detected_tesseract = any('J' in result.upper() for result in tesseract_results.values())

        # Si au moins deux OCR détectent un J, c'est probablement un J
        j_detections = sum([j_detected_paddle, j_detected_easy, j_detected_tesseract])
        if j_detections >= 2:
            logger.info("J détecté par %d OCR", j_detections)
            return 'J'

        # Vérifier si un Q est détecté dans les résultats OCR
        q_detected_paddle = any('Q' in result.upper() for result in paddle_results.values())
        q_detected_easy = any('Q' in result.upper() for result in easy_results.values())
        q_detected_tesseract = any('Q' in result.upper() for result in tesseract_results.values())

        # Si au moins deux OCR détectent un Q, c'est probablement un Q
        q_detections = sum([q_detected_paddle, q_detected_easy, q_detected_tesseract])
        if q_detections >= 2:
            logger.info("Q détecté par %d OCR", q_detections)
            return 'Q'

        # Créer une liste de tous les résultats valides
        valid_results = []

        # Ajouter les résultats de PaddleOCR
        for version, text in paddle_results.items():
            text = text.strip().upper()
            for value in CARD_VALUES:
                if value in text:
                    valid_results.append((value, "paddle", version))
                    break

        # Ajouter les résultats d'EasyOCR
        for version, text in easy_results.items():
            text = text.strip().upper()
            for value in CARD_VALUES:
                if value in text:
                    valid_results.append((value, "easy", version))
                    break

        # Ajouter les résultats de Tesseract OCR
        for version, text in tesseract_results.items():
            text = text.strip().upper()
            for value in CARD_VALUES:
                if value in text:
                    valid_results.append((value, "tesseract", version))
                    break

        # Si aucun résultat valide n'est trouvé, essayer de corriger les confusions courantes
        if not valid_results:
            # Essayer de corriger les confusions pour J
            j_confusions_detected = []

            # Vérifier dans PaddleOCR
            for version, text in paddle_results.items():
                text = text.strip().upper()
                for confusion in J_CONFUSIONS:
                    if confusion.upper() in text:
                        j_confusions_detected.append(("paddle", confusion))

            # Vérifier dans EasyOCR
            for version, text in easy_results.items():
                text = text.strip().upper()
                for confusion in J_CONFUSIONS:
                    if confusion.upper() in text:
                        j_confusions_detected.append(("easy", confusion))

            # Vérifier dans Tesseract OCR
            for version, text in tesseract_results.items():
                text = text.strip().upper()
                for confusion in J_CONFUSIONS:
                    if confusion.upper() in text:
                        j_confusions_detected.append(("tesseract", confusion))

            # Si au moins deux OCR détectent une confusion pour J, c'est probablement un J
            if len(j_confusions_detected) >= 2:
                logger.info("Possible J détecté (confusions: %s)", j_confusions_detected)
                return 'J'

            # Essayer de corriger les confusions pour Q
            q_confusions_detected = []

            # Vérifier dans PaddleOCR
            for version, text in paddle_results.items():
                text = text.strip().upper()
                for confusion in Q_CONFUSIONS:
                    if confusion.upper() in text:
                        q_confusions_detected.append(("paddle", confusion))

            # Vérifier dans EasyOCR
            for version, text in easy_results.items():
                text = text.strip().upper()
                for confusion in Q_CONFUSIONS:
                    if confusion.upper() in text:
                        q_confusions_detected.append(("easy", confusion))

            # Vérifier dans Tesseract OCR
            for version, text in tesseract_results.items():
                text = text.strip().upper()
                for confusion in Q_CONFUSIONS:
                    if confusion.upper() in text:
                        q_confusions_detected.append(("tesseract", confusion))

            # Si au moins deux OCR détectent une confusion pour Q, c'est probablement un Q
            if len(q_confusions_detected) >= 2:
                logger.info("Possible Q détecté (confusions: %s)", q_confusions_detected)
                return 'Q'

            # Si un seul OCR détecte une confusion pour J, c'est peut-être un J
            if len(j_confusions_detected) == 1:
                logger.info("Possible J détecté (confusion: %s)", j_confusions_detected[0])
                return 'J'

            # Si un seul OCR détecte une confusion pour Q, c'est peut-être un Q
            if len(q_confusions_detected) == 1:
                logger.info("Possible Q détecté (confusion: %s)", q_confusions_detected[0])
                return 'Q'

            # Si toujours rien, essayer de trouver un résultat dans l'un des OCR
            all_results = []

            # Ajouter les résultats de PaddleOCR
            for text in paddle_results.values():
                if text.strip():
                    all_results.append(("paddle", text.strip().upper()))

            # Ajouter les résultats d'EasyOCR
            for text in easy_results.values():
                if text.strip():
                    all_results.append(("easy", text.strip().upper()))

            # Ajouter les résultats de Tesseract OCR
            for text in tesseract_results.values():
                if text.strip():
                    all_results.append(("tesseract", text.strip().upper()))

            # Si au moins un résultat est trouvé, retourner le plus long
            if all_results:
                longest_result = max(all_results, key=lambda x: len(x[1]))
                logger.info("Résultat le plus long: %s (%s)", longest_result[1], longest_result[0])
                return longest_result[1]

            # Si toujours rien, retourner une chaîne vide
            return ""

        # Compter les occurrences de chaque valeur
        value_counts = {}
        for value, _, _ in valid_results:
            value_counts[value] = value_counts.get(value, 0) + 1

        # Retourner la valeur la plus fréquente
        most_common_value = max(value_counts.items(), key=lambda x: x[1])[0]
        logger.info("Valeur la plus fréquente: %s (occurrences: %d)",
                   most_common_value, value_counts[most_common_value])

        return most_common_value
