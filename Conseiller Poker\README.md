# Poker-Coach Pro v5

## Description
Poker-Coach Pro v5 est une application d'intelligence artificielle spécialisée en poker qui analyse en temps réel les situations de jeu et fournit des recommandations stratégiques optimales. L'application surveille le fichier "realtime_results" situé dans le dossier "C:\Users\<USER>\PokerAdvisor\Détection des regions\export" et génère des analyses détaillées.

## Fonctionnalités
- Analyse en temps réel des situations de poker
- Affichage des cartes avec leurs couleurs respectives :
  - ♥ <PERSON><PERSON><PERSON> (Rouge)
  - ♠ <PERSON><PERSON> (Noir)
  - ♣ Trèfle (Vert)
  - ♦ <PERSON><PERSON> (Bleu)
  - Lettres et chiffres des cartes (1,2,3,4,5) en Blanc
- Évaluation de la force de la main et de l'équité contre une range adverse
- Calcul des cotes du pot (pot odds) et des cotes implicites
- Recommandation d'actions optimales avec justification
- Historique des analyses précédentes
- Interface sombre pour un meilleur confort visuel

## Format d'analyse
L'application génère des analyses structurées comprenant :
1. **Situation** : Board, main, taille du pot et tapis effectif
2. **Analyse rapide** : Force de la main, équité estimée, pot odds et cote implicite
3. **Recommandation** : Action optimale avec justification concise
4. **Notes supplémentaires** : Hypothèses ou informations complémentaires

## Prérequis
- Python 3.8 ou supérieur
- PyQt5

## Installation
1. Assurez-vous que Python est installé sur votre système
2. Installez PyQt5 si ce n'est pas déjà fait :
   ```
   pip install PyQt5
   ```

## Utilisation
1. Double-cliquez sur le fichier "run_advisor.bat" pour lancer l'application
2. Ou exécutez directement le script Python :
   ```
   python poker_advisor_app.py
   ```
3. L'application se connecte automatiquement au fichier de données et commence à analyser les situations de poker
4. Utilisez le bouton "Rafraîchir l'analyse" pour forcer une mise à jour
5. Utilisez le bouton "Effacer l'historique" pour réinitialiser l'historique des analyses


## Structure des fichiers
- `poker_advisor_app.py` : Script principal de l'application
- `run_advisor.bat` : Script batch pour lancer facilement l'application
- `README.md` : Ce fichier d'instructions

## Dépannage
- Si l'application affiche "Le fichier n'existe pas", vérifiez que le chemin vers le fichier "realtime_results" est correct
- Si vous rencontrez des erreurs lors du lancement, vérifiez que PyQt5 est bien installé
- Si les cartes ne s'affichent pas correctement, vérifiez que le format des données dans le fichier "realtime_results" est correct
