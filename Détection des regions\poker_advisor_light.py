#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Poker Advisor Light - Module de conseiller poker léger et rapide
================================================================

Version simplifiée du conseiller poker pour une intégration directe
dans l'interface de détection des régions.

Ce module permet d'analyser rapidement les cartes détectées et de
fournir des recommandations stratégiques optimales.

Auteur: Augment Agent
Date: 2023-2025
"""

import re
import time
from collections import OrderedDict

# Importer les nouveaux modules avancés
try:
    from monte_carlo_simulator import MonteCarloSimulator
    from range_analyzer import RangeAnalyzer
    from gto_solver import GTOSolver
    from variance_calculator import VarianceCalculator
    from session_analyzer import SessionAnalyzer
    ADVANCED_MODULES_AVAILABLE = True
    print("🚀 Modules avancés chargés avec succès!")
except ImportError as e:
    ADVANCED_MODULES_AVAILABLE = False
    print(f"⚠️ Modules avancés non disponibles: {e}")
    print("📝 Fonctionnalités de base uniquement")

# Constantes pour les cartes
CARD_VALUES = ["A", "K", "Q", "J", "10", "9", "8", "7", "6", "5", "4", "3", "2"]
CARD_SUITS = ["Cœur", "Pique", "Trèfle", "Carreau"]

# Constantes pour les couleurs d'affichage
CARD_COLORS = {
    "Cœur": "rouge",
    "Pique": "noir",
    "Trèfle": "vert",
    "Carreau": "bleu",
    "Valeur": "blanc"
}

# Mappings pour corriger les erreurs de détection courantes
CARD_VALUE_CORRECTIONS = {
    # Confusion 9/6 - priorité au 9 pour g/G, priorité au 6 pour b/B
    "g": "9", "G": "9", "q": "9", "Q": "9",
    "b": "6", "B": "6", "p": "6", "P": "6",
    # Confusion 8/B
    "B": "8", "b": "8",
    # Confusion 7/T/L/I
    "T": "7", "l": "7", "L": "7", "I": "7", "i": "7",
    # Confusion 5/S/G
    "S": "5", "s": "5",
    # Confusion A/4
    "A": "4", "a": "4",
    # Confusion 3/E
    "E": "3", "e": "3",
    # Confusion 2/Z
    "Z": "2", "z": "2"
}

class PokerAdvisorLight:
    """Classe principale du conseiller poker léger"""

    def __init__(self, cache_size=20):
        """
        Initialise le conseiller poker

        Args:
            cache_size (int): Taille du cache pour les analyses
        """
        self.cache = PokerDataCache(max_size=cache_size)
        self.last_analysis = None
        self.last_data = None
        self.manual_corrections = {}  # Dictionnaire pour stocker les corrections manuelles

        # Initialiser les modules avancés si disponibles
        if ADVANCED_MODULES_AVAILABLE:
            try:
                self.monte_carlo = MonteCarloSimulator()
                self.range_analyzer = RangeAnalyzer()
                self.gto_solver = GTOSolver()
                self.variance_calculator = VarianceCalculator()
                self.session_analyzer = SessionAnalyzer()
                self.advanced_features_enabled = True
                print("🎯 Modules avancés initialisés: Monte Carlo, Range Analyzer, GTO Solver, Variance Calculator, Session Analyzer")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'initialisation des modules avancés: {e}")
                self.advanced_features_enabled = False
        else:
            self.advanced_features_enabled = False
            print("📝 Mode de base activé - fonctionnalités avancées désactivées")

    def set_manual_correction(self, region_name, corrected_value, corrected_suit=None):
        """
        Définit une correction manuelle pour une carte

        Args:
            region_name (str): Nom de la région (ex: "card_1", "hand_card_1")
            corrected_value (str): Valeur corrigée de la carte (ex: "A", "K", "Q", etc.) ou "" pour "pas de cartes"
            corrected_suit (str, optional): Couleur corrigée de la carte (ex: "Cœur", "Pique", etc.)

        Returns:
            bool: True si la correction a été appliquée avec succès
        """
        # Valider la valeur de la carte (peut être vide pour "pas de cartes")
        if corrected_value not in CARD_VALUES and corrected_value != "":
            return False

        # Valider la couleur de la carte (peut être vide)
        if corrected_suit is not None and corrected_suit not in CARD_SUITS and corrected_suit != "":
            return False

        # Stocker la correction (même si la valeur est vide pour "pas de cartes")
        self.manual_corrections[region_name] = {
            "value": corrected_value,
            "suit": corrected_suit if corrected_suit else ""
        }

        # Vider le cache pour forcer une nouvelle analyse
        self.cache.clear()

        return True

    def remove_manual_correction(self, region_name):
        """
        Supprime une correction manuelle pour une région

        Args:
            region_name (str): Nom de la région

        Returns:
            bool: True si la correction a été supprimée
        """
        if region_name in self.manual_corrections:
            del self.manual_corrections[region_name]
            self.cache.clear()
            return True
        return False

    def get_manual_corrections(self):
        """
        Récupère toutes les corrections manuelles

        Returns:
            dict: Dictionnaire des corrections manuelles
        """
        return self.manual_corrections

    def clear_manual_corrections(self):
        """
        Efface toutes les corrections manuelles

        Returns:
            int: Nombre de corrections effacées
        """
        count = len(self.manual_corrections)
        self.manual_corrections.clear()
        self.cache.clear()
        return count

    def analyze_detection_results(self, results):
        """
        Analyse les résultats de détection et fournit des recommandations

        Args:
            results (dict): Résultats de la détection des cartes

        Returns:
            dict: Analyse complète avec recommandations
        """
        # Extraire les données de poker des résultats
        data = self.extract_poker_data(results)

        # Vérifier si de nouvelles cartes ont été détectées et supprimer les anciennes corrections si nécessaire
        self._check_and_update_cards(data, results)

        # Vérifier si les données sont dans le cache
        analysis, formatted_analysis, from_cache = self.cache.get(data)
        if from_cache:
            self.last_analysis = analysis
            self.last_data = data
            return analysis, formatted_analysis

        # Analyser la situation de poker
        analysis = self.analyze_poker_situation(data)

        # Formater l'analyse pour l'affichage
        formatted_analysis = self.format_analysis(data, analysis)

        # Mettre en cache les résultats
        self.cache.put(data, analysis, formatted_analysis)

        # Stocker les dernières données et analyse
        self.last_analysis = analysis
        self.last_data = data

        return analysis, formatted_analysis

    def _check_and_update_cards(self, current_data, results):
        """
        Vérifie si de nouvelles cartes ont été détectées et supprime les anciennes corrections si nécessaire

        Args:
            current_data (dict): Données de poker actuelles
            results (dict): Résultats de la détection
        """
        # Si c'est la première analyse, ne rien faire
        if self.last_data is None:
            return

        # Extraire les cartes actuelles et précédentes
        current_board_cards = current_data.get("board_cards_text", "").split(", ") if current_data.get("board_cards_text") else []
        current_hand_cards = current_data.get("hand_cards_text", "").split(", ") if current_data.get("hand_cards_text") else []

        previous_board_cards = self.last_data.get("board_cards_text", "").split(", ") if self.last_data.get("board_cards_text") else []
        previous_hand_cards = self.last_data.get("hand_cards_text", "").split(", ") if self.last_data.get("hand_cards_text") else []

        # Si le nombre de cartes a changé, vérifier les corrections à supprimer
        if len(current_board_cards) != len(previous_board_cards) or len(current_hand_cards) != len(previous_hand_cards):
            # Vérifier les régions qui ont changé
            regions_to_check = []

            # Vérifier les cartes du board
            for i in range(1, 6):
                region_name = f"card_{i}"
                if region_name in results:
                    regions_to_check.append(region_name)

            # Vérifier les cartes en main
            for i in range(1, 3):
                for region_format in [f"carte_{i}m", f"hand_card_{i}"]:
                    if region_format in results:
                        regions_to_check.append(region_format)

            # Supprimer les corrections pour les régions qui ont changé
            corrections_to_remove = []
            for region in self.manual_corrections:
                if region in regions_to_check:
                    # Vérifier si la région a une nouvelle valeur détectée
                    if region in results and results[region].get("text", "").strip():
                        corrections_to_remove.append(region)

            # Supprimer les corrections
            for region in corrections_to_remove:
                del self.manual_corrections[region]

            # Si des corrections ont été supprimées, vider le cache
            if corrections_to_remove:
                self.cache.clear()

    def convert_to_float(self, text):
        """
        Convertit un texte en nombre flottant en gérant les virgules et les points

        Args:
            text (str): Texte à convertir en nombre

        Returns:
            float: Valeur numérique convertie
        """
        if not text:
            return 0.0

        # Remplacer les virgules par des points pour la conversion
        text = text.replace(',', '.')

        # Supprimer les espaces et autres caractères non numériques
        # Mais garder les points décimaux
        cleaned_text = re.sub(r'[^\d.]', '', text)

        try:
            return float(cleaned_text)
        except ValueError:
            return 0.0

    def extract_poker_data(self, results):
        """
        Extrait les données de poker des résultats de détection

        Args:
            results (dict): Résultats de la détection

        Returns:
            dict: Données de poker structurées
        """
        data = {
            "hand_cards_text": "",
            "board_cards_text": "",
            "probability": 0,
            "action": "",
            "pot": 10,  # Valeurs par défaut
            "pot_total": 10,
            "effective_stack": 100,
            "my_stack": 100,
            "my_bet": 0,  # Ma mise actuelle
            "player_stacks": {},
            "player_bets": {},
            "player_allins": {},  # All-ins des joueurs
            "allin_indicators": {},  # Indicateurs all-in (rouge dans jetons)
            "call_amount": 0,  # Montant à suivre
            "raise_amount": 0,  # Montant de relance
            "my_allin": 0,  # Mon all-in
            "missing_cards": [],  # Liste pour suivre les cartes manquantes
            "detected_regions": [],  # Liste des régions détectées
            "selected_regions": [],  # Liste des régions sélectionnées
            "analysis_scope": "complete"  # Portée de l'analyse
        }

        # Récupérer les métadonnées si disponibles
        metadata = results.get("_metadata", {})
        if metadata:
            data["selected_regions"] = metadata.get("selected_regions", [])
            data["total_selected"] = metadata.get("total_selected", 0)
            data["total_detected"] = metadata.get("total_detected", 0)
            print(f"🎯 Analyse complète: {data['total_detected']}/{data['total_selected']} régions détectées")

            # Analyser les types de régions sélectionnées
            selected_types = {
                "cards": 0,
                "chips": 0,
                "bets": 0,
                "players": 0,
                "other": 0
            }

            for region in data["selected_regions"]:
                if region.startswith(("card_", "carte_", "hand_card_")):
                    selected_types["cards"] += 1
                elif region.startswith(("chips_", "stack_")):
                    selected_types["chips"] += 1
                elif region.startswith(("bet_", "mise_")):
                    selected_types["bets"] += 1
                elif region.startswith("player"):
                    selected_types["players"] += 1
                else:
                    selected_types["other"] += 1

            print(f"📊 Types de régions sélectionnées: Cartes={selected_types['cards']}, Jetons={selected_types['chips']}, Mises={selected_types['bets']}, Joueurs={selected_types['players']}, Autres={selected_types['other']}")

            # Ajuster la portée de l'analyse selon les régions sélectionnées
            if selected_types["cards"] > 0 and (selected_types["chips"] > 0 or selected_types["bets"] > 0):
                data["analysis_scope"] = "complete"
            elif selected_types["cards"] > 0:
                data["analysis_scope"] = "cards_only"
            elif selected_types["chips"] > 0 or selected_types["bets"] > 0:
                data["analysis_scope"] = "money_only"
            else:
                data["analysis_scope"] = "limited"

        # Extraire les cartes du board (card_1 à card_5)
        board_cards = []
        for i in range(1, 6):
            region_name = f"card_{i}"

            # Vérifier s'il y a une correction manuelle pour cette région
            if region_name in self.manual_corrections:
                correction = self.manual_corrections[region_name]
                corrected_value = correction.get("value", "")
                corrected_suit = correction.get("suit", "")

                # Marquer cette région comme ayant une correction manuelle
                data["manual_corrections"] = data.get("manual_corrections", [])
                data["manual_corrections"].append(region_name)

                # Si la valeur est vide, c'est une correction "Pas de cartes"
                if corrected_value == "":
                    # Ne pas ajouter cette carte, passer à la suivante
                    continue

                # Si la valeur est spécifiée, traiter la correction
                if corrected_value:
                    # Si la couleur n'est pas spécifiée, essayer de la récupérer des résultats de détection
                    if not corrected_suit and region_name in results:
                        region_data = results[region_name]
                        colors = region_data.get("colors", [])
                        corrected_suit = self.determine_card_suit(colors)

                    # Si nous avons maintenant une valeur et une couleur, ajouter la carte corrigée
                    if corrected_value and corrected_suit:
                        board_cards.append(f"{corrected_value} de {corrected_suit}")
                        data["detected_regions"].append(region_name)
                        continue

            # Si pas de correction manuelle ou correction incomplète, utiliser la détection automatique
            if region_name in results:
                region_data = results[region_name]
                card_text = region_data.get("text", "").strip()
                colors = region_data.get("colors", [])

                if card_text:
                    # Corriger les erreurs de détection courantes
                    corrected_card_text = self.correct_card_value(card_text)
                    # Déterminer la couleur de la carte
                    suit = self.determine_card_suit(colors)
                    if suit and corrected_card_text in CARD_VALUES:
                        board_cards.append(f"{corrected_card_text} de {suit}")
                    data["detected_regions"].append(region_name)

        # Extraire les cartes en main (carte_1m et carte_2m ou hand_card_1 et hand_card_2)
        hand_cards = []
        hand_regions_checked = []

        # Vérifier d'abord les corrections manuelles pour les cartes en main
        for i in range(1, 3):
            # Vérifier les deux formats possibles
            region_names = [f"carte_{i}m", f"hand_card_{i}"]

            for region_name in region_names:
                if region_name in self.manual_corrections:
                    correction = self.manual_corrections[region_name]
                    corrected_value = correction.get("value", "")
                    corrected_suit = correction.get("suit", "")

                    # Marquer cette région comme ayant une correction manuelle
                    data["manual_corrections"] = data.get("manual_corrections", [])
                    data["manual_corrections"].append(region_name)
                    hand_regions_checked.append(region_name)

                    # Si la valeur est vide, c'est une correction "Pas de cartes"
                    if corrected_value == "":
                        # Ne pas ajouter cette carte, passer à la suivante
                        continue

                    # Si la valeur est spécifiée, traiter la correction
                    if corrected_value:
                        # Si la couleur n'est pas spécifiée, essayer de la récupérer des résultats de détection
                        if not corrected_suit and region_name in results:
                            region_data = results[region_name]
                            colors = region_data.get("colors", [])
                            corrected_suit = self.determine_card_suit(colors)

                        # Si nous avons maintenant une valeur et une couleur, ajouter la carte corrigée
                        if corrected_value and corrected_suit:
                            hand_cards.append(f"{corrected_value} de {corrected_suit}")
                            data["detected_regions"].append(region_name)

        # Si nous n'avons pas encore 2 cartes en main, essayer la détection automatique
        if len(hand_cards) < 2:
            # Essayer d'abord avec le format carte_Xm
            for i in range(1, 3):
                region_name = f"carte_{i}m"
                if region_name not in hand_regions_checked and region_name in results:
                    region_data = results[region_name]
                    card_text = region_data.get("text", "").strip()
                    colors = region_data.get("colors", [])

                    if card_text:
                        # Corriger les erreurs de détection courantes
                        corrected_card_text = self.correct_card_value(card_text)
                        # Déterminer la couleur de la carte
                        suit = self.determine_card_suit(colors)
                        if suit and corrected_card_text in CARD_VALUES:
                            hand_cards.append(f"{corrected_card_text} de {suit}")
                        data["detected_regions"].append(region_name)

            # Si nous n'avons toujours pas 2 cartes en main, essayer avec le format hand_card_X
            if len(hand_cards) < 2:
                for i in range(1, 3):
                    region_name = f"hand_card_{i}"
                    if region_name not in hand_regions_checked and region_name in results:
                        region_data = results[region_name]
                        card_text = region_data.get("text", "").strip()
                        colors = region_data.get("colors", [])

                        if card_text:
                            # Corriger les erreurs de détection courantes
                            corrected_card_text = self.correct_card_value(card_text)
                            # Déterminer la couleur de la carte
                            suit = self.determine_card_suit(colors)
                            if suit and corrected_card_text in CARD_VALUES:
                                hand_cards.append(f"{corrected_card_text} de {suit}")
                            data["detected_regions"].append(region_name)

        # Extraire les informations sur les jetons et les mises
        # Rechercher les régions de jetons (mes_jetons, jetons_joueurX, etc.)
        for region_name, region_data in results.items():
            # Extraire les jetons du joueur principal
            if region_name == "mes_jetons":
                # Récupérer le texte brut
                chips_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💰 Texte brut pour {region_name}: '{chips_text}'")

                if chips_text:
                    # Convertir en nombre en gérant les virgules
                    chips_value = self.convert_to_float(chips_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💰 Valeur convertie pour {region_name}: {chips_value}")

                    if chips_value > 0:  # Vérifier que la valeur est positive
                        data["my_stack"] = chips_value
                        data["effective_stack"] = chips_value  # Par défaut, le tapis effectif est celui du joueur
                        print(f"✅ Stack du joueur mis à jour: {chips_value}")
                        data["detected_regions"].append(region_name)

            # Extraire les jetons des autres joueurs
            elif region_name.startswith("jetons_joueur"):
                # Récupérer le texte brut et les couleurs
                chips_text = region_data.get("text", "").strip()
                colors = region_data.get("colors", [])

                # Afficher le texte brut pour le débogage
                print(f"💰 Texte brut pour {region_name}: '{chips_text}' - Couleurs: {colors}")

                # Vérifier si c'est un all-in (couleur rouge dans les jetons)
                if colors and 'red' in colors:
                    # Extraire le numéro du joueur
                    player_match = re.search(r'joueur(\d+)', region_name)
                    if player_match:
                        player_num = player_match.group(1)
                        # Marquer ce joueur comme all-in (montant sera récupéré dans la mise)
                        data["allin_indicators"][f"joueur{player_num}"] = True
                        print(f"🔥 ALL-IN INDICATEUR détecté pour joueur {player_num}: Rouge dans jetons (montant dans mise)")
                        data["detected_regions"].append(region_name)
                elif chips_text:
                    # Traitement normal des jetons (pas d'all-in)
                    chips_value = self.convert_to_float(chips_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💰 Valeur convertie pour {region_name}: {chips_value}")

                    if chips_value > 0:  # Vérifier que la valeur est positive
                        # Extraire le numéro du joueur
                        player_match = re.search(r'joueur(\d+)', region_name)
                        if player_match:
                            player_num = player_match.group(1)
                            data["player_stacks"][f"joueur{player_num}"] = chips_value
                            # Mettre à jour le tapis effectif (le plus petit tapis à la table)
                            data["effective_stack"] = min(data["effective_stack"], chips_value)
                            print(f"✅ Stack du joueur {player_num} mis à jour: {chips_value}")

                            # Log spécial pour les gros montants
                            if chips_value >= 1000:
                                print(f"💰 GROS MONTANT détecté pour joueur {player_num}: {chips_value} BB")
                            elif chips_value < 10:
                                print(f"⚠️ PETIT MONTANT pour joueur {player_num}: {chips_value} BB - Vérifier si complet")

                            data["detected_regions"].append(region_name)

            # Extraire les jetons avec l'ancien format pour compatibilité
            elif region_name.startswith("chips_") or region_name.startswith("stack_"):
                # Récupérer le texte brut
                chips_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💰 Texte brut pour {region_name}: '{chips_text}'")

                if chips_text:
                    # Convertir en nombre en gérant les virgules
                    chips_value = self.convert_to_float(chips_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💰 Valeur convertie pour {region_name}: {chips_value}")

                    if chips_value > 0:  # Vérifier que la valeur est positive
                        # Si c'est le stack du joueur principal
                        if region_name == "chips_player" or region_name == "stack_player" or region_name == "my_stack":
                            data["my_stack"] = chips_value
                            data["effective_stack"] = chips_value  # Par défaut, le tapis effectif est celui du joueur
                            print(f"✅ Stack du joueur mis à jour: {chips_value}")
                        else:
                            # Extraire le numéro du joueur si possible
                            player_match = re.search(r'player(\d+)', region_name)
                            if player_match:
                                player_num = player_match.group(1)
                                data["player_stacks"][f"player{player_num}"] = chips_value
                                # Mettre à jour le tapis effectif (le plus petit tapis à la table)
                                data["effective_stack"] = min(data["effective_stack"], chips_value)
                                print(f"✅ Stack du joueur {player_num} mis à jour: {chips_value}")

                        data["detected_regions"].append(region_name)

            # Extraire ma mise
            elif region_name == "ma_mise":
                # Récupérer le texte brut
                bet_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💲 Texte brut pour {region_name}: '{bet_text}'")

                if bet_text:
                    # Convertir en nombre en gérant les virgules
                    bet_value = self.convert_to_float(bet_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💲 Valeur convertie pour {region_name}: {bet_value}")

                    if bet_value > 0:  # Vérifier que la valeur est positive
                        data["my_bet"] = bet_value
                        print(f"✅ Ma mise mise à jour: {bet_value}")
                        # Ajouter au pot total
                        data["pot_total"] += bet_value
                        data["detected_regions"].append(region_name)

            # Extraire les mises des autres joueurs
            elif region_name.startswith("mise_joueur"):
                # Récupérer le texte brut et les couleurs
                bet_text = region_data.get("text", "").strip()
                colors = region_data.get("colors", [])

                # Afficher le texte brut pour le débogage
                print(f"💲 Texte brut pour {region_name}: '{bet_text}' - Couleurs: {colors}")

                if bet_text:
                    # Convertir en nombre en gérant les virgules
                    bet_value = self.convert_to_float(bet_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💲 Valeur convertie pour {region_name}: {bet_value}")

                    if bet_value > 0:  # Vérifier que la valeur est positive
                        # Extraire le numéro du joueur
                        player_match = re.search(r'joueur(\d+)', region_name)
                        if player_match:
                            player_num = player_match.group(1)

                            # Vérifier si c'est un all-in (indicateur rouge dans jetons)
                            is_allin = data["allin_indicators"].get(f"joueur{player_num}", False)

                            # Toujours ajouter comme mise (que ce soit all-in ou mise normale)
                            data["player_bets"][f"joueur{player_num}"] = bet_value

                            if is_allin:
                                # C'est un all-in : le montant de la mise EST le montant all-in
                                data["player_allins"][f"joueur{player_num}"] = bet_value
                                print(f"🔥 ALL-IN du joueur {player_num}: {bet_value} BB (rouge dans jetons, montant = mise)")
                            else:
                                # Mise normale
                                print(f"✅ Mise du joueur {player_num} mise à jour: {bet_value}")

                            # Log spécial pour les gros montants
                            if bet_value >= 1000:
                                print(f"💰 GROSSE MISE détectée pour joueur {player_num}: {bet_value} BB")

                            # Ajouter au pot total
                            data["pot_total"] += bet_value
                            data["detected_regions"].append(region_name)

            # Extraire les mises avec l'ancien format pour compatibilité
            elif region_name.startswith("bet_") or region_name.startswith("mise_"):
                # Récupérer le texte brut
                bet_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💲 Texte brut pour {region_name}: '{bet_text}'")

                if bet_text:
                    # Convertir en nombre en gérant les virgules
                    bet_value = self.convert_to_float(bet_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💲 Valeur convertie pour {region_name}: {bet_value}")

                    if bet_value > 0:  # Vérifier que la valeur est positive
                        # Si c'est la mise du joueur principal
                        if region_name == "bet_player" or region_name == "mise_player":
                            data["my_bet"] = bet_value
                            print(f"✅ Mise du joueur mise à jour: {bet_value}")
                        else:
                            # Extraire le numéro du joueur si possible
                            player_match = re.search(r'player(\d+)', region_name)
                            if player_match:
                                player_num = player_match.group(1)
                                data["player_bets"][f"player{player_num}"] = bet_value
                                print(f"✅ Mise du joueur {player_num} mise à jour: {bet_value}")

                        # Ajouter au pot total
                        data["pot_total"] += bet_value
                        data["detected_regions"].append(region_name)

            # Extraire le pot total (chiffres blancs uniquement) - SEULE SOURCE DE POT
            elif region_name == "pot_total":
                # Récupérer le texte brut
                pot_total_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"🎯 Texte brut POT TOTAL (blanc uniquement): '{pot_total_text}'")

                if pot_total_text:
                    # Convertir en nombre en gérant les virgules
                    pot_total_value = self.convert_to_float(pot_total_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"🎯 Valeur convertie POT TOTAL: {pot_total_value}")

                    if pot_total_value > 0:  # Vérifier que la valeur est positive
                        data["pot_total"] = pot_total_value
                        data["pot"] = pot_total_value  # Le pot total devient le pot principal
                        print(f"✅ Pot total mis à jour: {pot_total_value}")
                        print(f"✅ Pot principal mis à jour avec pot total: {pot_total_value}")
                        data["detected_regions"].append(region_name)

            # Extraire les montants de call et relance
            elif region_name in ["montant_call", "montant_relance"]:
                # Récupérer le texte brut
                amount_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💵 Texte brut pour {region_name}: '{amount_text}'")

                if amount_text:
                    # Convertir en nombre en gérant les virgules
                    amount_value = self.convert_to_float(amount_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💵 Valeur convertie pour {region_name}: {amount_value}")

                    if amount_value > 0:  # Vérifier que la valeur est positive
                        if region_name == "montant_call":
                            data["call_amount"] = amount_value
                            print(f"✅ Montant call mis à jour: {amount_value}")
                        elif region_name == "montant_relance":
                            data["raise_amount"] = amount_value
                            print(f"✅ Montant relance mis à jour: {amount_value}")
                        data["detected_regions"].append(region_name)

            # Extraire les all-in
            elif region_name.startswith("allin_joueur") or region_name == "mon_allin":
                # Récupérer le texte brut
                allin_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"🔥 Texte brut pour {region_name}: '{allin_text}'")

                if allin_text:
                    # Convertir en nombre en gérant les virgules
                    allin_value = self.convert_to_float(allin_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"🔥 Valeur convertie pour {region_name}: {allin_value}")

                    if allin_value > 0:  # Vérifier que la valeur est positive
                        if region_name == "mon_allin":
                            data["my_allin"] = allin_value
                            print(f"✅ Mon all-in mis à jour: {allin_value}")
                        else:
                            # Extraire le numéro du joueur
                            player_match = re.search(r'joueur(\d+)', region_name)
                            if player_match:
                                player_num = player_match.group(1)
                                data["player_allins"][f"joueur{player_num}"] = allin_value
                                print(f"✅ All-in du joueur {player_num} mis à jour: {allin_value}")
                        data["detected_regions"].append(region_name)

        # Mettre à jour les données
        data["board_cards_text"] = ", ".join(board_cards) if board_cards else "Non détectées"
        data["hand_cards_text"] = ", ".join(hand_cards) if hand_cards else "Non détectées"

        # Vérifier les cartes en double
        all_cards = board_cards + hand_cards
        duplicates = [card for card in all_cards if all_cards.count(card) > 1]
        if duplicates:
            unique_duplicates = list(set(duplicates))
            data["missing_cards"].append(f"duplicate_cards:{','.join(unique_duplicates)}")

        # DÉTECTION DIRECTE DU BOUTON VIA LES RÉGIONS BOUTON_JOUEUR (NOIR ET BLANC)
        button_position = self.detect_button_from_regions(results)
        if button_position:
            data['button_position'] = button_position
            print(f"🔘 Bouton détecté directement: {button_position}")

        # AJOUTER LES INFORMATIONS DE JEU AVANCÉES (TABLE DE 6 JOUEURS)
        game_analysis = results.get('game_analysis', {})
        if game_analysis:
            print("🎯 Récupération des informations de jeu avancées...")

            # Récupérer la profondeur des tapis
            stack_depth = game_analysis.get('stack_depth', 'unknown')
            if stack_depth != 'unknown':
                data['stack_depth'] = stack_depth
                print(f"💰 Profondeur des tapis: {stack_depth}")

            # Récupérer les pot odds
            pot_odds = game_analysis.get('pot_odds', 0)
            if pot_odds > 0:
                data['pot_odds_calculated'] = pot_odds
                print(f"📊 Pot odds calculés: {pot_odds:.1f}%")

            # Récupérer le nombre de régions détectées
            detected_regions_count = len(game_analysis.get('detected_regions', []))
            if detected_regions_count > 0:
                data['game_regions_detected'] = detected_regions_count
                print(f"📈 Régions de jeu détectées: {detected_regions_count}")

            # Récupérer la moyenne des tapis
            avg_stack = game_analysis.get('average_stack', 0)
            if avg_stack > 0:
                data['average_stack'] = avg_stack
                print(f"💰 Moyenne des tapis: {avg_stack:.1f} BB")

        return data

    def correct_card_value(self, card_text):
        """
        Corrige les erreurs de détection courantes dans les valeurs de cartes

        Args:
            card_text (str): Texte de la carte détectée

        Returns:
            str: Texte corrigé de la carte
        """
        # Nettoyer le texte (supprimer les espaces et caractères spéciaux)
        cleaned_text = card_text.strip()

        # Si le texte est déjà une valeur valide, le retourner tel quel
        if cleaned_text in CARD_VALUES:
            return cleaned_text

        # Cas spécial pour distinguer 9 et 6 (problème fréquent)
        if cleaned_text.lower() in ["g", "q"]:
            return "9"  # Si c'est un g ou q, c'est probablement un 9
        if cleaned_text.lower() in ["b", "p"]:
            return "6"  # Si c'est un b ou p, c'est probablement un 6

        # Vérifier si le texte est dans notre dictionnaire de corrections
        if cleaned_text in CARD_VALUE_CORRECTIONS:
            return CARD_VALUE_CORRECTIONS[cleaned_text]

        # Si le texte est un seul caractère, vérifier les corrections
        if len(cleaned_text) == 1 and cleaned_text in CARD_VALUE_CORRECTIONS:
            return CARD_VALUE_CORRECTIONS[cleaned_text]

        # Cas spécial pour le 10 qui peut être détecté comme IO, l0, etc.
        if cleaned_text.lower() in ["io", "l0", "lo", "to", "t0"]:
            return "10"

        # Cas spécial pour les figures
        if cleaned_text.lower() in ["as", "a", "ace"]:
            return "A"
        if cleaned_text.lower() in ["roi", "r", "k", "king"]:
            return "K"
        if cleaned_text.lower() in ["dame", "d", "q", "queen"]:
            return "Q"
        if cleaned_text.lower() in ["valet", "v", "j", "jack"]:
            return "J"

        # Cas spécial pour les chiffres écrits en toutes lettres
        number_words = {
            "one": "1", "un": "1", "une": "1",
            "two": "2", "deux": "2",
            "three": "3", "trois": "3",
            "four": "4", "quatre": "4",
            "five": "5", "cinq": "5",
            "six": "6", "six": "6",
            "seven": "7", "sept": "7",
            "eight": "8", "huit": "8",
            "nine": "9", "neuf": "9",
            "ten": "10", "dix": "10"
        }
        if cleaned_text.lower() in number_words:
            return number_words[cleaned_text.lower()]

        # Analyse contextuelle pour 9 vs 6 (si le texte contient ces caractères)
        if "g" in cleaned_text.lower() or "q" in cleaned_text.lower():
            return "9"
        if "b" in cleaned_text.lower() or "p" in cleaned_text.lower():
            return "6"

        # Si aucune correction n'est trouvée, retourner le texte original
        return cleaned_text

    def determine_card_suit(self, colors):
        """
        Détermine la couleur d'une carte à partir des couleurs détectées

        Args:
            colors (list): Liste des couleurs détectées

        Returns:
            str: Couleur de la carte (Cœur, Pique, Trèfle, Carreau) ou None
        """
        if not colors:
            return None

        # Correspondance entre les couleurs détectées et les couleurs de cartes
        if "red" in colors:
            return "Cœur"
        elif "black" in colors and "white" not in colors:
            return "Pique"
        elif "green" in colors:
            return "Trèfle"
        elif "blue" in colors:
            return "Carreau"

        # Par défaut, retourner Pique si on ne peut pas déterminer
        return "Pique"

    def detect_button_from_regions(self, results):
        """
        Détecte la position du bouton en utilisant les nouvelles régions bouton_joueur

        Args:
            results (dict): Résultats de détection contenant les régions bouton_joueur

        Returns:
            str: Position du bouton (ex: "joueur3") ou None si non détecté
        """
        print("🔍 Détection du bouton via les régions bouton_joueur...")

        # Vérifier toutes les régions bouton_joueur (1 à 7) et bouton_moi
        button_regions = []

        # Régions des joueurs adverses
        for i in range(1, 8):
            region_name = f"bouton_joueur{i}"
            if region_name in results:
                region_data = results[region_name]
                text = region_data.get("text", "").strip()
                colors = region_data.get("colors", [])

                # Vérifier si le bouton est détecté (présence de texte ou couleurs spécifiques)
                if text or colors:
                    # Analyser les couleurs pour détecter le bouton dealer
                    if self._is_dealer_button_detected(text, colors):
                        button_regions.append(f"joueur{i}")
                        print(f"🔘 Bouton détecté pour joueur{i}: texte='{text}', couleurs={colors}")

        # Vérifier ma région bouton
        if "bouton_moi" in results:
            region_data = results["bouton_moi"]
            text = region_data.get("text", "").strip()
            colors = region_data.get("colors", [])

            if text or colors:
                if self._is_dealer_button_detected(text, colors):
                    button_regions.append("moi")
                    print(f"🔘 Bouton détecté pour moi: texte='{text}', couleurs={colors}")

        # Retourner la première position de bouton détectée
        if button_regions:
            button_position = button_regions[0]  # Prendre le premier détecté
            print(f"✅ Position du bouton confirmée: {button_position}")
            return button_position

        print("❌ Aucun bouton détecté dans les régions bouton_joueur")
        return None

    def _is_dealer_button_detected(self, text, colors):
        """
        Détermine si un bouton dealer est présent basé sur l'ABSENCE d'orange

        NOUVELLE LOGIQUE SIMPLIFIÉE:
        - Si orange détecté = PAS de bouton (tapis visible)
        - Si PAS d'orange = Bouton présent

        Args:
            text (str): Texte détecté dans la région (non utilisé)
            colors (list): Couleurs détectées dans la région

        Returns:
            bool: True si un bouton dealer est détecté (PAS d'orange)
        """
        if colors and isinstance(colors, list):
            # Vérifier s'il y a de l'orange (tapis)
            has_orange = any(color.lower() in ['orange'] for color in colors)

            if has_orange:
                print(f"❌ Pas de bouton: orange détecté (tapis visible) dans {colors}")
                return False
            else:
                # Pas d'orange = bouton présent
                print(f"🔘 Bouton dealer détecté: PAS d'orange dans {colors}")
                return True

        # Si aucune couleur détectée, considérer comme pas de bouton
        print(f"❌ Pas de bouton: aucune couleur détectée")
        return False

    def analyze_poker_situation(self, data):
        """
        Analyse la situation de poker

        Args:
            data (dict): Données de poker extraites

        Returns:
            dict: Résultats de l'analyse
        """
        results = {
            "hand_cards": [],
            "board_cards": [],
            "hand_strength": "",
            "equity": (0, 0),
            "pot_odds": 0,
            "bet_to_call": 0,  # Montant à suivre
            "implied_odds": "N/A",
            "recommended_action": "",
            "action_reason": "",
            "notes": [],
            "detected_regions": data.get("detected_regions", []),
            "detected_chips": False,  # Indicateur pour les jetons détectés
            "detected_bets": False,   # Indicateur pour les mises détectées
            "detected_pot": False     # Indicateur pour le pot détecté
        }

        # Vérifier si les jetons, mises et pot ont été détectés
        for region in data.get("detected_regions", []):
            # Vérifier les jetons (nouveaux et anciens formats)
            if (region == "mes_jetons" or region.startswith("jetons_joueur") or
                region.startswith("chips_") or region.startswith("stack_")):
                results["detected_chips"] = True
            # Vérifier les mises (nouveaux et anciens formats)
            elif (region == "ma_mise" or region.startswith("mise_joueur") or
                  region.startswith("bet_") or region.startswith("mise_")):
                results["detected_bets"] = True
            # Vérifier le pot (seul pot_total est utilisé maintenant)
            elif region in ["pot_total"]:
                results["detected_pot"] = True

        # Analyser les cartes
        hand_cards = self.parse_cards(data["hand_cards_text"])
        board_cards = self.parse_cards(data["board_cards_text"])

        # Convertir au format Texas Hold'em
        results["hand_cards"] = self.cards_to_texas_format(hand_cards)
        results["board_cards"] = self.cards_to_texas_format(board_cards)

        # Ajouter des notes pour les cartes manquantes
        for issue in data["missing_cards"]:
            if issue.startswith("duplicate_cards:"):
                cards = issue.split(":")[1].split(",")
                results["notes"].append(f"⚠️ Cartes en double détectées: {', '.join(cards)}")

        # Évaluer la force de la main seulement si nous avons des cartes valides
        if results["hand_cards"] or results["board_cards"]:
            results["hand_strength"] = self.evaluate_hand_strength(results["board_cards"], results["hand_cards"])

            # Estimer l'équité
            results["equity"] = self.estimate_equity(results["board_cards"], results["hand_cards"])

            # NOUVEAU : Analyser les tirages et améliorations possibles
            draws_analysis = self.analyze_draws_and_improvements(results["board_cards"], results["hand_cards"])
            results["draws_analysis"] = draws_analysis

            # Ajouter les informations de tirages aux notes
            if draws_analysis["improvements"]:
                if draws_analysis["cards_to_come"] > 0:
                    if draws_analysis['total_outs'] > 0:
                        results["notes"].append(f"🎯 {draws_analysis['total_outs']} outs pour s'améliorer ({draws_analysis.get('improvement_probability', 'N/A')})")
                        for improvement in draws_analysis["improvements"]:
                            if isinstance(improvement, dict) and improvement.get("description"):
                                results["notes"].append(f"   • {improvement['description']}")
                    else:
                        results["notes"].append("🎯 Aucune amélioration possible")
                else:
                    results["notes"].append("🏁 Main finale - plus d'améliorations possibles")

            # NOUVEAU : Fonctionnalités avancées si disponibles
            if self.advanced_features_enabled:
                results["advanced_analysis"] = self.perform_advanced_analysis(results, data)

                # FUSIONNER TOUS LES CONSEILS EN UN SEUL OPTIMAL
                unified_recommendation = self._create_unified_recommendation(results, data)
                if unified_recommendation:
                    results["recommended_action"] = unified_recommendation["action"]
                    results["action_reason"] = unified_recommendation["reason"]
                    results["confidence_score"] = unified_recommendation["confidence"]
                    results["unified_analysis"] = unified_recommendation["analysis"]

                # Ajouter les insights avancés aux notes
                if results["advanced_analysis"].get("insights"):
                    results["notes"].extend(results["advanced_analysis"]["insights"])

            # Ajouter une note spécifique pour les mains très faibles
            if not results["board_cards"] and len(results["hand_cards"]) == 2:
                # Vérifier si c'est une main extrêmement faible
                category, description, _ = self.evaluate_preflop_hand(results["hand_cards"])
                if category == 9 and "À COUCHER" in description:
                    results["notes"].append("⛔ MAIN TRÈS FAIBLE: VOUS DEVRIEZ VOUS COUCHER ⛔")
        else:
            results["hand_strength"] = "Non détecté"
            results["equity"] = (0, 0)
            results["notes"].append("⚠️ Impossible d'évaluer la main: aucune carte valide détectée")

        # Calculer les pot odds en fonction des données réelles si disponibles
        bet_to_call = 0
        for player_bet in data["player_bets"].values():
            bet_to_call = max(bet_to_call, player_bet)

        # Stocker le montant à suivre dans les résultats
        results["bet_to_call"] = bet_to_call

        # Si nous avons une mise à suivre et un pot
        if bet_to_call > 0 and data["pot"] > 0:
            pot_odds = (bet_to_call / (data["pot"] + bet_to_call)) * 100
            results["pot_odds"] = round(pot_odds, 1)
        else:
            # Valeur par défaut si nous n'avons pas d'informations sur les mises
            results["pot_odds"] = 20

        # Calculer le rapport stack-to-pot
        if data["pot"] > 0 and data["effective_stack"] > 0:
            stack_to_pot_ratio = data["effective_stack"] / data["pot"]
        else:
            stack_to_pot_ratio = 10  # Valeur par défaut

        # Calculer les cotes implicites
        if data["pot"] > 0 and data["my_stack"] > 0:
            implied_odds = (data["pot"] + sum(data["player_stacks"].values())) / data["my_stack"]
            results["implied_odds"] = f"{implied_odds:.1f}:1"

        # Ajouter des informations sur les jetons détectés
        if data["my_stack"] != 100:  # Si ce n'est pas la valeur par défaut
            results["notes"].append(f"💰 Vos jetons: {data['my_stack']}")

        if data["player_stacks"]:
            stack_info = ", ".join([f"Joueur {k[-1:]}: {v}" for k, v in data["player_stacks"].items()])
            results["notes"].append(f"💰 Jetons adverses: {stack_info}")

        if data["pot"] != 10:  # Si ce n'est pas la valeur par défaut
            results["notes"].append(f"🏆 Pot: {data['pot']}")

        # Ajouter une note sur le montant à suivre si nécessaire
        if results["bet_to_call"] > 0:
            results["notes"].append(f"💲 Mise à suivre: {results['bet_to_call']:.0f} BB")

        # Recommander une action en fonction des informations disponibles
        if results["hand_cards"]:
            # Si nous avons des cartes en main, nous pouvons donner une recommandation
            action, reason = self.recommend_action(
                results["equity"],
                results["pot_odds"],
                stack_to_pot_ratio,
                data["my_stack"],
                data["pot"],
                results["bet_to_call"]  # Passer le montant à suivre
            )
            results["recommended_action"] = action
            results["action_reason"] = reason

            # Extraire le montant recommandé de l'action
            results["recommended_amount"] = self.extract_recommended_amount(action, results["bet_to_call"])
        else:
            # Si nous n'avons pas de cartes en main, recommander d'attendre
            results["recommended_action"] = "attendre"
            results["action_reason"] = "Cartes en main non détectées"
            results["notes"].append("⚠️ Recommandation impossible: cartes en main non détectées")

        # Ajouter une note sur les régions détectées
        if results["detected_regions"]:
            results["notes"].append(f"🔍 Régions détectées: {len(results['detected_regions'])}")

        # Ajouter une note sur les corrections manuelles
        if "manual_corrections" in data and data["manual_corrections"]:
            corrections_list = ", ".join(data["manual_corrections"])
            results["notes"].append(f"✏️ Corrections manuelles: {len(data['manual_corrections'])} ({corrections_list})")

        return results

    def parse_cards(self, card_text):
        """
        Analyse le texte des cartes et retourne une liste de tuples (valeur, couleur)

        Args:
            card_text (str): Texte contenant les cartes (ex: "As de Cœur, Roi de Pique")

        Returns:
            list: Liste de tuples (valeur, couleur)
        """
        if not card_text or card_text == "Non détectées":
            return []

        cards = []
        # Diviser le texte en cartes individuelles
        card_parts = card_text.split(", ")

        for part in card_parts:
            # Rechercher la valeur et la couleur
            match = re.search(r"([A-Z0-9]+) de (Cœur|Pique|Trèfle|Carreau)", part)
            if match:
                value, suit = match.groups()
                cards.append((value, suit))

        return cards

    def cards_to_texas_format(self, cards):
        """
        Convertit les cartes au format Texas Hold'em (ex: As♥)

        Args:
            cards (list): Liste de tuples (valeur, couleur)

        Returns:
            list: Liste de cartes au format Texas Hold'em
        """
        if not cards:
            return []

        # Symboles des couleurs
        suit_symbols = {
            "Cœur": "♥",
            "Pique": "♠",
            "Trèfle": "♣",
            "Carreau": "♦"
        }

        # Conversion des valeurs
        value_conversion = {
            "A": "A", "K": "K", "Q": "Q", "J": "J", "10": "T",
            "9": "9", "8": "8", "7": "7", "6": "6", "5": "5",
            "4": "4", "3": "3", "2": "2"
        }

        texas_cards = []
        for value, suit in cards:
            if value in value_conversion and suit in suit_symbols:
                texas_cards.append(f"{value_conversion[value]}{suit_symbols[suit]}")

        return texas_cards

    def evaluate_preflop_hand(self, hand_cards):
        """
        Évalue la force d'une main pre-flop

        Args:
            hand_cards (list): Liste des cartes en main au format Texas Hold'em

        Returns:
            tuple: (catégorie, description, rang)
                catégorie: 1-9 (1=premium, 9=très faible)
                description: Description textuelle de la main
                rang: Valeur numérique pour comparer les mains (plus élevé = meilleur)
        """
        if len(hand_cards) != 2:
            return (9, "Main incomplète", 0)

        # Extraire les valeurs et les couleurs
        values = [card[0] for card in hand_cards]
        suits = [card[1] for card in hand_cards]

        # Convertir les valeurs en valeurs numériques pour la comparaison
        value_map = {"A": 14, "K": 13, "Q": 12, "J": 11, "T": 10,
                     "9": 9, "8": 8, "7": 7, "6": 6, "5": 5, "4": 4, "3": 3, "2": 2}
        numeric_values = [value_map.get(v, 0) for v in values]
        numeric_values.sort(reverse=True)  # Trier par ordre décroissant

        # Vérifier si les cartes sont de la même couleur (suited)
        suited = suits[0] == suits[1]

        # Vérifier si c'est une paire
        is_pair = numeric_values[0] == numeric_values[1]

        # Vérifier si les cartes sont connectées (consécutives)
        connected = abs(numeric_values[0] - numeric_values[1]) == 1

        # Vérifier si les cartes sont presque connectées (écart de 2)
        almost_connected = abs(numeric_values[0] - numeric_values[1]) == 2

        # Calculer l'écart entre les cartes
        gap = abs(numeric_values[0] - numeric_values[1])

        # Calculer le rang de la main (plus élevé = meilleur)
        hand_rank = 0

        # Classification des mains pre-flop
        if is_pair:
            # Paires
            if numeric_values[0] >= 10:  # AA, KK, QQ, JJ, TT
                category = 1
                description = f"Paire premium ({values[0]}{values[1]})"
                hand_rank = 90 + numeric_values[0]
            elif numeric_values[0] >= 7:  # 99, 88, 77
                category = 2
                description = f"Paire moyenne ({values[0]}{values[1]})"
                hand_rank = 80 + numeric_values[0]
            else:  # 66, 55, 44, 33, 22
                category = 4
                description = f"Petite paire ({values[0]}{values[1]})"
                hand_rank = 70 + numeric_values[0]
        elif numeric_values[0] >= 13 and numeric_values[1] >= 11:  # AK, AQ, KQ
            # Cartes hautes premium
            if suited:
                category = 1
                description = f"Main premium assortie ({values[0]}{values[1]}s)"
                hand_rank = 85 + numeric_values[0] + numeric_values[1] * 0.1
            else:
                category = 2
                description = f"Main premium non assortie ({values[0]}{values[1]}o)"
                hand_rank = 75 + numeric_values[0] + numeric_values[1] * 0.1
        elif numeric_values[0] >= 13 and numeric_values[1] >= 9:  # AJ, AT, A9, KJ, KT
            # Cartes hautes fortes
            if suited:
                category = 2
                description = f"Main forte assortie ({values[0]}{values[1]}s)"
                hand_rank = 65 + numeric_values[0] + numeric_values[1] * 0.1
            else:
                category = 3
                description = f"Main forte non assortie ({values[0]}{values[1]}o)"
                hand_rank = 55 + numeric_values[0] + numeric_values[1] * 0.1
        elif numeric_values[0] >= 13:  # Ax
            # As avec petite carte
            if suited:
                category = 4
                description = f"As avec petite carte assortie ({values[0]}{values[1]}s)"
                hand_rank = 45 + numeric_values[1] * 0.5
            else:
                category = 6
                description = f"As avec petite carte non assortie ({values[0]}{values[1]}o)"
                hand_rank = 35 + numeric_values[1] * 0.5
        elif connected and numeric_values[0] >= 10:  # Connecteurs hauts: KQ, QJ, JT
            # Connecteurs hauts
            if suited:
                category = 3
                description = f"Connecteurs hauts assortis ({values[0]}{values[1]}s)"
                hand_rank = 60 + numeric_values[0] * 0.5
            else:
                category = 4
                description = f"Connecteurs hauts non assortis ({values[0]}{values[1]}o)"
                hand_rank = 50 + numeric_values[0] * 0.5
        elif connected:  # Connecteurs moyens et bas
            # Connecteurs moyens et bas
            if suited:
                category = 4
                description = f"Connecteurs assortis ({values[0]}{values[1]}s)"
                hand_rank = 40 + numeric_values[0] * 0.5
            else:
                category = 6
                description = f"Connecteurs non assortis ({values[0]}{values[1]}o)"
                hand_rank = 30 + numeric_values[0] * 0.5
        elif almost_connected and numeric_values[0] >= 10:  # Presque connecteurs hauts: K9, Q9, J9
            # Presque connecteurs hauts
            if suited:
                category = 5
                description = f"Presque connecteurs hauts assortis ({values[0]}{values[1]}s)"
                hand_rank = 35 + numeric_values[0] * 0.5
            else:
                category = 7
                description = f"Presque connecteurs hauts non assortis ({values[0]}{values[1]}o)"
                hand_rank = 25 + numeric_values[0] * 0.5
        elif suited and numeric_values[0] >= 10:  # Cartes hautes assorties
            category = 5
            description = f"Cartes hautes assorties ({values[0]}{values[1]}s)"
            hand_rank = 30 + numeric_values[0] * 0.5 + numeric_values[1] * 0.1
        elif numeric_values[0] >= 10 and numeric_values[1] >= 8:  # Cartes hautes non assorties
            category = 6
            description = f"Cartes hautes non assorties ({values[0]}{values[1]}o)"
            hand_rank = 20 + numeric_values[0] * 0.5 + numeric_values[1] * 0.1
        elif suited and gap <= 4:  # Cartes moyennes assorties avec petit écart
            category = 7
            description = f"Cartes moyennes assorties ({values[0]}{values[1]}s)"
            hand_rank = 15 + numeric_values[0] * 0.3 + numeric_values[1] * 0.1
        # Cas spécifique pour 5-3 et autres mains faibles similaires
        elif numeric_values[0] <= 7 and numeric_values[1] <= 5 and gap >= 2:
            # Mains très faibles avec un grand écart
            if suited:
                category = 9
                description = f"Main très faible assortie ({values[0]}{values[1]}s)"
                hand_rank = 5 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
            else:
                category = 9
                description = f"Main très faible non assortie ({values[0]}{values[1]}o)"
                hand_rank = 3 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
        # Cas spécifique pour les mains extrêmement faibles (comme 5-3, 7-2, etc.)
        elif numeric_values[0] <= 7 and numeric_values[1] <= 3:
            # Mains extrêmement faibles
            if suited:
                category = 9
                description = f"Main extrêmement faible assortie ({values[0]}{values[1]}s) - À COUCHER"
                hand_rank = 2 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
            else:
                category = 9
                description = f"Main extrêmement faible non assortie ({values[0]}{values[1]}o) - À COUCHER"
                hand_rank = 1 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
        else:
            # Autres mains faibles
            if suited:
                category = 8
                description = f"Main faible assortie ({values[0]}{values[1]}s)"
                hand_rank = 10 + numeric_values[0] * 0.2 + numeric_values[1] * 0.1
            else:
                category = 9
                description = f"Main faible non assortie ({values[0]}{values[1]}o)"
                hand_rank = 5 + numeric_values[0] * 0.2 + numeric_values[1] * 0.1

        return (category, description, hand_rank)

    def evaluate_hand_strength(self, board_cards, hand_cards):
        """
        Évalue la force de la main

        Args:
            board_cards (list): Liste des cartes du board au format Texas Hold'em
            hand_cards (list): Liste des cartes en main au format Texas Hold'em

        Returns:
            str: Description de la force de la main
        """
        # Si nous sommes au pre-flop (pas de cartes sur le board), utiliser l'évaluation pre-flop
        if not board_cards and len(hand_cards) == 2:
            _, description, _ = self.evaluate_preflop_hand(hand_cards)
            return description

        # Sinon, évaluer la main avec toutes les cartes
        all_cards = board_cards + hand_cards

        # Vérifier les paires
        values = [card[0] for card in all_cards]
        value_counts = {value: values.count(value) for value in set(values)}

        # Vérifier les couleurs
        suits = [card[1] for card in all_cards]
        suit_counts = {suit: suits.count(suit) for suit in set(suits)}

        # Déterminer la force de la main (ORDRE CORRECT : mains faites d'abord)
        if any(count >= 4 for count in value_counts.values()):
            return "Carré"
        elif any(count >= 3 for count in value_counts.values()) and any(count >= 2 for count in value_counts.values()):
            return "Full House"
        elif any(count >= 5 for count in suit_counts.values()):
            return "Couleur"
        elif any(count >= 3 for count in value_counts.values()):
            return "Brelan"
        elif list(value_counts.values()).count(2) >= 2:
            return "Deux paires"
        elif any(count >= 2 for count in value_counts.values()):
            return "Paire"
        elif len(all_cards) >= 5:
            # Vérifier s'il y a vraiment une quinte possible
            return self.check_straight_possibility(all_cards)
        else:
            return "Hauteur"

    def check_straight_possibility(self, all_cards):
        """
        Vérifie s'il y a vraiment une possibilité de quinte

        Args:
            all_cards (list): Toutes les cartes disponibles

        Returns:
            str: "Quinte" si quinte faite, "Tirage quinte" si tirage, "Hauteur" sinon
        """
        if len(all_cards) < 5:
            return "Hauteur"

        # Convertir les valeurs en nombres
        value_map = {"A": 14, "K": 13, "Q": 12, "J": 11, "T": 10,
                     "9": 9, "8": 8, "7": 7, "6": 6, "5": 5, "4": 4, "3": 3, "2": 2}

        values = [card[0] for card in all_cards]
        numeric_values = sorted(set([value_map.get(v, 0) for v in values if v in value_map]))

        # Vérifier les quintes faites
        for i in range(len(numeric_values) - 4):
            if numeric_values[i+4] - numeric_values[i] == 4:
                return "Quinte"

        # Vérifier la wheel spéciale (A-2-3-4-5)
        wheel = [14, 5, 4, 3, 2]
        if all(val in numeric_values for val in wheel):
            return "Quinte"

        # Vérifier les tirages de quinte (4 cartes consécutives)
        for i in range(len(numeric_values) - 3):
            if numeric_values[i+3] - numeric_values[i] == 3:
                return "Tirage quinte"

        # Vérifier tirage wheel (4 cartes sur 5 pour A-2-3-4-5)
        wheel_present = sum(1 for val in wheel if val in numeric_values)
        if wheel_present >= 4:
            return "Tirage quinte"

        return "Hauteur"

    def analyze_draws_and_improvements(self, board_cards, hand_cards):
        """
        Analyse les tirages et améliorations possibles jusqu'à la river

        Args:
            board_cards (list): Cartes du board
            hand_cards (list): Cartes en main

        Returns:
            dict: Analyse complète des tirages et améliorations
        """
        if not hand_cards or len(hand_cards) < 2:
            return {"current_hand": "Inconnue", "improvements": [], "outs": 0}

        all_cards = board_cards + hand_cards
        current_hand = self.evaluate_hand_strength(board_cards, hand_cards)

        # Déterminer le nombre de cartes restantes à venir
        cards_to_come = 5 - len(board_cards) if len(board_cards) < 5 else 0

        analysis = {
            "current_hand": current_hand,
            "cards_to_come": cards_to_come,
            "improvements": [],
            "outs": 0,
            "total_outs": 0
        }

        if cards_to_come == 0:
            analysis["improvements"].append({"type": "Final", "description": "🏁 Main finale - plus d'améliorations possibles", "outs": 0})
            return analysis

        # Analyser les améliorations possibles
        improvements = []
        total_outs = 0

        # 1. Analyser les tirages de couleur
        color_analysis = self._analyze_flush_draws(all_cards)
        if color_analysis["outs"] > 0:
            improvements.append(color_analysis)
            total_outs += color_analysis["outs"]

        # 2. Analyser les tirages de quinte
        straight_analysis = self._analyze_straight_draws(all_cards)
        if straight_analysis["outs"] > 0:
            improvements.append(straight_analysis)
            total_outs += straight_analysis["outs"]

        # 3. Analyser les améliorations de paires/brelans
        pair_analysis = self._analyze_pair_improvements(all_cards, hand_cards)
        if pair_analysis["outs"] > 0:
            improvements.append(pair_analysis)
            total_outs += pair_analysis["outs"]

        # 4. Analyser les overcards (cartes supérieures)
        overcard_analysis = self._analyze_overcards(board_cards, hand_cards)
        if overcard_analysis["outs"] > 0:
            improvements.append(overcard_analysis)
            total_outs += overcard_analysis["outs"]

        analysis["improvements"] = improvements
        analysis["total_outs"] = min(total_outs, 47)  # Maximum théorique

        # Calculer les probabilités
        if cards_to_come > 0 and total_outs > 0:
            # Probabilité d'amélioration
            if cards_to_come == 1:  # River seulement
                prob = (total_outs / 47) * 100
                analysis["improvement_probability"] = f"{prob:.1f}%"
            elif cards_to_come == 2:  # Turn + River
                prob = 1 - ((47 - total_outs) / 47) * ((46 - total_outs) / 46)
                analysis["improvement_probability"] = f"{prob * 100:.1f}%"
            else:  # Plusieurs cartes
                prob = min(total_outs * cards_to_come / 47, 0.95)
                analysis["improvement_probability"] = f"{prob * 100:.1f}%"

        return analysis

    def extract_recommended_amount(self, action, bet_to_call):
        """
        Extrait le montant recommandé de l'action

        Args:
            action (str): Action recommandée (ex: "raise 5 BB", "call 10 BB", "all-in")
            bet_to_call (float): Montant à suivre

        Returns:
            float: Montant de l'action recommandée
        """
        if not action:
            return 0

        action_lower = action.lower()

        # Extraire le montant des actions de relance
        if "raise" in action_lower:
            # Chercher un nombre suivi de "BB"
            import re
            match = re.search(r'(\d+(?:\.\d+)?)\s*bb', action_lower)
            if match:
                return float(match.group(1))
            # Chercher juste un nombre
            match = re.search(r'(\d+(?:\.\d+)?)', action_lower)
            if match:
                return float(match.group(1))

        # Pour les calls, retourner le montant à suivre
        elif "call" in action_lower:
            # Chercher un nombre dans l'action
            import re
            match = re.search(r'(\d+(?:\.\d+)?)', action_lower)
            if match:
                return float(match.group(1))
            else:
                return bet_to_call

        # Pour les all-in, retourner 0 (sera géré différemment dans l'affichage)
        elif "all-in" in action_lower:
            return 0  # All-in sera affiché spécialement

        # Pour les autres actions (fold, check, etc.)
        else:
            return 0

    def format_recommended_amount(self, recommended_amount, action, bet_to_call):
        """
        Formate l'affichage du montant recommandé

        Args:
            recommended_amount (float): Montant extrait de l'action
            action (str): Action recommandée
            bet_to_call (float): Montant à suivre

        Returns:
            str: Montant formaté pour l'affichage
        """
        if not action:
            return "0 BB"

        action_lower = action.lower()

        if "raise" in action_lower and recommended_amount > 0:
            return f"{recommended_amount:.1f} BB (relance)".replace(".0", "")
        elif "call" in action_lower:
            if recommended_amount > 0:
                return f"{recommended_amount:.0f} BB (suivre)"
            else:
                return f"{bet_to_call:.0f} BB (suivre)"
        elif "all-in" in action_lower:
            return "All-in (tout le stack)"
        elif "fold" in action_lower:
            return "0 BB (se coucher)"
        elif "check" in action_lower:
            return "0 BB (checker)"
        else:
            return f"{recommended_amount:.0f} BB"

    def perform_advanced_analysis(self, results, data):
        """
        Effectue une analyse avancée avec tous les modules

        Args:
            results (dict): Résultats de l'analyse de base
            data (dict): Données de poker extraites

        Returns:
            dict: Analyse avancée complète
        """
        if not self.advanced_features_enabled:
            return {"error": "Modules avancés non disponibles"}

        advanced_analysis = {
            "monte_carlo": {},
            "range_analysis": {},
            "gto_solution": {},
            "variance_metrics": {},
            "insights": [],
            "recommendations": []
        }

        try:
            # 1. ANALYSE MONTE CARLO
            if results.get("hand_cards") and len(results["hand_cards"]) == 2:
                hero_hand = self._convert_cards_for_simulation(results["hand_cards"])
                board = self._convert_cards_for_simulation(results.get("board_cards", []))

                if hero_hand:
                    # Définir des ranges d'adversaires selon la situation
                    villain_ranges = self._estimate_villain_ranges(data)

                    mc_result = self.monte_carlo.simulate_equity(
                        hero_hand, villain_ranges, board, num_simulations=5000
                    )

                    if "error" not in mc_result:
                        advanced_analysis["monte_carlo"] = mc_result
                        advanced_analysis["insights"].append(
                            f"🎲 Monte Carlo: {mc_result['equity']:.1f}% équité ({mc_result['total_simulations']} simulations)"
                        )

            # 2. ANALYSE RANGE VS RANGE
            if data.get("player_stacks"):
                # Estimer notre range et celui des adversaires
                hero_range = self._estimate_hero_range(data)
                villain_range = self._estimate_primary_villain_range(data)

                if hero_range and villain_range:
                    range_result = self.range_analyzer.analyze_range_vs_range(
                        hero_range, villain_range, results.get("board_cards", [])
                    )

                    if "error" not in range_result:
                        advanced_analysis["range_analysis"] = range_result
                        advanced_analysis["insights"].append(
                            f"📊 Range vs Range: {range_result['average_equity']:.1f}% équité moyenne"
                        )

            # 3. SOLUTION GTO
            situation = self._build_gto_situation(data, results)
            if situation:
                gto_result = self.gto_solver.solve_situation(situation)

                if "error" not in gto_result:
                    advanced_analysis["gto_solution"] = gto_result

                    # Extraire la recommandation principale
                    if gto_result.get("recommendations"):
                        main_rec = gto_result["recommendations"][0]
                        advanced_analysis["insights"].append(
                            f"🎯 GTO: {main_rec['description']}"
                        )

            # 4. CALCULS DE VARIANCE
            if data.get("my_stack"):
                variance_result = self.variance_calculator.calculate_variance_metrics(
                    game_type="cash_6max",
                    winrate=5.0,  # Estimation
                    hands_played=100,  # Session courte
                    style="tight_aggressive"
                )

                if "error" not in variance_result:
                    advanced_analysis["variance_metrics"] = variance_result
                    advanced_analysis["insights"].append(
                        f"📈 Variance: {variance_result['confidence_intervals']['95%']['lower']:.0f} à {variance_result['confidence_intervals']['95%']['upper']:.0f} BB (95%)"
                    )

            # 5. RECOMMANDATIONS AVANCÉES
            advanced_analysis["recommendations"] = self._generate_advanced_recommendations(
                advanced_analysis, results, data
            )

        except Exception as e:
            advanced_analysis["error"] = f"Erreur dans l'analyse avancée: {e}"
            print(f"⚠️ Erreur analyse avancée: {e}")

        return advanced_analysis

    def _convert_cards_for_simulation(self, cards):
        """Convertit les cartes du format français vers le format simulation"""
        if not cards:
            return []

        converted = []
        suit_map = {
            "Cœur": "h", "Pique": "s", "Trèfle": "c", "Carreau": "d"
        }
        value_map = {
            "A": "A", "K": "K", "Q": "Q", "J": "J", "10": "T",
            "9": "9", "8": "8", "7": "7", "6": "6", "5": "5", "4": "4", "3": "3", "2": "2"
        }

        for card in cards:
            if card and isinstance(card, str) and " de " in card:
                parts = card.split(" de ")
                if len(parts) == 2:
                    value, suit = parts
                    if value in value_map and suit in suit_map:
                        converted.append(value_map[value] + suit_map[suit])

        return converted

    def _estimate_villain_ranges(self, data):
        """Estime les ranges des adversaires selon la situation"""
        # Ranges par défaut selon le nombre d'adversaires
        num_opponents = len(data.get("player_stacks", {}))

        if num_opponents == 1:
            return [["AA", "KK", "QQ", "JJ", "AKs", "AKo"]]  # Range serré heads-up
        elif num_opponents <= 3:
            return [["AA", "KK", "QQ", "JJ", "TT", "99", "AKs", "AQs", "AKo", "AQo"]] * num_opponents
        else:
            return [["AA", "KK", "QQ", "JJ", "TT", "AKs", "AKo"]] * min(num_opponents, 5)

    def _estimate_hero_range(self, data):
        """Estime notre range selon la situation"""
        # Range par défaut - peut être amélioré avec plus de contexte
        return "BTN_tight"  # Range prédéfini

    def _estimate_primary_villain_range(self, data):
        """Estime le range de l'adversaire principal"""
        # Range par défaut - peut être amélioré avec plus de contexte
        return "UTG_tight"  # Range prédéfini

    def _build_gto_situation(self, data, results):
        """Construit une situation pour le solver GTO"""
        return {
            "position": "BTN",  # Position par défaut
            "stack_size": data.get("my_stack", 100),
            "pot_size": data.get("pot_total", 10),
            "action_to_call": data.get("call_amount", 0),
            "opponents": list(data.get("player_stacks", {}).keys()),
            "board": self._convert_cards_for_simulation(results.get("board_cards", []))
        }

    def _generate_advanced_recommendations(self, advanced_analysis, results, data):
        """Génère des recommandations basées sur l'analyse avancée"""
        recommendations = []

        # Recommandations Monte Carlo
        if advanced_analysis["monte_carlo"].get("equity"):
            equity = advanced_analysis["monte_carlo"]["equity"]
            if equity > 60:
                recommendations.append("💪 Équité forte - Jouer agressivement")
            elif equity < 40:
                recommendations.append("⚠️ Équité faible - Jouer prudemment")

        # Recommandations GTO
        if advanced_analysis["gto_solution"].get("recommendations"):
            gto_recs = advanced_analysis["gto_solution"]["recommendations"]
            for rec in gto_recs[:2]:  # Prendre les 2 premières
                recommendations.append(f"🎯 GTO: {rec['description']}")

        # Recommandations de variance
        if advanced_analysis["variance_metrics"].get("bankroll_requirements"):
            br_req = advanced_analysis["variance_metrics"]["bankroll_requirements"]
            if "recommended" in br_req:
                recommendations.append(f"💰 Bankroll recommandée: {br_req['recommended']['bankroll_bb']:.0f} BB")

        return recommendations

    def _create_unified_recommendation(self, results, data):
        """
        Crée une recommandation unifiée en fusionnant tous les conseils avancés

        Args:
            results (dict): Résultats de l'analyse de base
            data (dict): Données de poker extraites

        Returns:
            dict: Recommandation unifiée avec action, raison et score de confiance
        """
        if not self.advanced_features_enabled or not results.get("advanced_analysis"):
            return None

        advanced_analysis = results["advanced_analysis"]

        # Collecter tous les conseils
        recommendations = {
            "basic": {
                "action": results.get("recommended_action", ""),
                "weight": 1.0,
                "source": "Analyse de base"
            }
        }

        # Ajouter les recommandations Monte Carlo
        if advanced_analysis.get("monte_carlo") and "error" not in advanced_analysis["monte_carlo"]:
            mc_equity = advanced_analysis["monte_carlo"].get("equity", 0)
            # Seulement si l'équité est valide (pas 0 par défaut)
            if mc_equity > 0:
                mc_action = self._equity_to_action(mc_equity, data)
                recommendations["monte_carlo"] = {
                    "action": mc_action,
                    "weight": 2.0,  # Poids élevé car très précis
                    "source": f"Monte Carlo ({mc_equity:.1f}% équité)",
                    "equity": mc_equity
                }

        # Ajouter les recommandations GTO
        if advanced_analysis.get("gto_solution") and "error" not in advanced_analysis["gto_solution"]:
            gto_strategy = advanced_analysis["gto_solution"].get("mixed_strategy", {})
            if gto_strategy:
                gto_action = max(gto_strategy.items(), key=lambda x: x[1])[0]
                gto_freq = gto_strategy[gto_action]
                recommendations["gto"] = {
                    "action": self._normalize_action(gto_action),
                    "weight": 1.8,  # Poids élevé car théoriquement optimal
                    "source": f"GTO ({gto_freq*100:.0f}% fréquence)",
                    "frequency": gto_freq
                }

        # Ajouter les recommandations Range vs Range
        if advanced_analysis.get("range_analysis") and "error" not in advanced_analysis["range_analysis"]:
            range_equity = advanced_analysis["range_analysis"].get("average_equity", 0)
            range_action = self._equity_to_action(range_equity, data)
            recommendations["range"] = {
                "action": range_action,
                "weight": 1.5,
                "source": f"Range vs Range ({range_equity:.1f}% équité)",
                "equity": range_equity
            }

        # Analyser la cohérence des recommandations
        action_votes = {}
        total_weight = 0

        for source, rec in recommendations.items():
            action = rec["action"]
            weight = rec["weight"]

            if action not in action_votes:
                action_votes[action] = {"weight": 0, "sources": [], "details": []}

            action_votes[action]["weight"] += weight
            action_votes[action]["sources"].append(source)
            action_votes[action]["details"].append(rec["source"])
            total_weight += weight

        # Déterminer l'action gagnante
        if not action_votes:
            # Si aucune recommandation avancée, utiliser au moins l'analyse de base
            if recommendations.get("basic", {}).get("action"):
                return {
                    "action": recommendations["basic"]["action"],
                    "reason": "Analyse de base uniquement",
                    "confidence": 50,  # Confiance modérée
                    "analysis": {
                        "consensus": "1/1 analyses",
                        "winning_weight": 1.0,
                        "total_weight": 1.0,
                        "sources": ["basic"],
                        "conflicts": [],
                        "all_recommendations": recommendations
                    }
                }
            return None

        winning_action = max(action_votes.items(), key=lambda x: x[1]["weight"])
        action_name = winning_action[0]
        action_data = winning_action[1]

        # Calculer le score de confiance
        confidence = (action_data["weight"] / total_weight) * 100

        # Ajuster la confiance selon la cohérence
        num_sources = len(action_data["sources"])
        total_sources = len(recommendations)
        consensus_bonus = (num_sources / total_sources) * 20  # Bonus jusqu'à 20%
        confidence = min(100, confidence + consensus_bonus)

        # Créer la raison unifiée
        reason_parts = []

        # Raison principale basée sur l'action
        if "fold" in action_name.lower():
            reason_parts.append("🛑 Situation défavorable détectée par l'analyse avancée")
        elif "call" in action_name.lower():
            reason_parts.append("📞 Équité suffisante pour suivre selon l'analyse multi-critères")
        elif "raise" in action_name.lower() or "bet" in action_name.lower():
            reason_parts.append("🚀 Situation favorable pour une mise agressive")
        elif "all-in" in action_name.lower():
            reason_parts.append("💥 Situation optimale pour un all-in")
        else:
            reason_parts.append("🎯 Action optimale selon l'analyse combinée")

        # Ajouter les détails des sources
        if len(action_data["sources"]) > 1:
            reason_parts.append(f"Consensus de {len(action_data['sources'])}/{total_sources} analyses")

        # Ajouter les détails spécifiques
        key_details = action_data["details"][:2]  # Prendre les 2 plus importantes
        if key_details:
            reason_parts.extend([f"• {detail}" for detail in key_details])

        # Analyser les conflits
        conflict_analysis = self._analyze_recommendation_conflicts(action_votes, recommendations)

        return {
            "action": self._format_unified_action(action_name, data),
            "reason": " | ".join(reason_parts),
            "confidence": confidence,
            "analysis": {
                "consensus": f"{len(action_data['sources'])}/{total_sources} analyses",
                "winning_weight": action_data["weight"],
                "total_weight": total_weight,
                "sources": action_data["sources"],
                "conflicts": conflict_analysis,
                "all_recommendations": recommendations
            }
        }

    def _equity_to_action(self, equity, data):
        """Convertit une équité en action recommandée"""
        bet_to_call = data.get("call_amount", 0)
        my_stack = data.get("my_stack", 100)
        pot_size = data.get("pot_total", 10)

        # Ajuster pour les équités négatives (erreur de calcul)
        if equity < 0:
            equity = abs(equity)  # Prendre la valeur absolue

        if equity < 25:
            return "fold"
        elif equity < 40:
            if bet_to_call == 0:
                return "check"
            else:
                return "fold" if bet_to_call > pot_size * 0.3 else "call"
        elif equity < 60:
            if bet_to_call == 0:
                return "bet_small"
            else:
                return "call"
        elif equity < 75:
            if bet_to_call == 0:
                return "bet_medium"
            else:
                return "raise_small"
        else:
            # Équité élevée - action agressive
            if my_stack < 20:
                return "all-in"
            elif bet_to_call == 0:
                return "bet_large"
            elif bet_to_call > pot_size * 0.5:
                return "call"  # Grosse mise à suivre, être prudent
            else:
                return "raise_large"

    def _normalize_action(self, gto_action):
        """Normalise les actions GTO vers des actions standard"""
        action_map = {
            "fold": "fold",
            "call": "call",
            "raise_small": "raise_small",
            "raise_medium": "raise_medium",
            "raise_large": "raise_large",
            "all_in": "all-in",
            "bet_small": "bet_small",
            "bet_medium": "bet_medium",
            "bet_large": "bet_large",
            "check": "check"
        }
        return action_map.get(gto_action, gto_action)

    def _analyze_recommendation_conflicts(self, action_votes, recommendations):
        """Analyse les conflits entre recommandations"""
        conflicts = []

        # Si plus de 2 actions différentes recommandées
        if len(action_votes) > 2:
            conflicts.append("Divergence significative entre les analyses")

        # Analyser les conflits spécifiques
        actions = list(action_votes.keys())

        # Conflit fold vs aggressive
        if any("fold" in action.lower() for action in actions) and \
           any(action.lower() in ["raise_large", "all-in", "bet_large"] for action in actions):
            conflicts.append("Conflit majeur: fold vs action agressive")

        # Conflit call vs raise
        if any("call" in action.lower() for action in actions) and \
           any("raise" in action.lower() or "bet" in action.lower() for action in actions):
            conflicts.append("Conflit mineur: passif vs agressif")

        return conflicts

    def _format_unified_action(self, action_name, data):
        """Formate l'action unifiée avec des montants appropriés"""
        my_stack = data.get("my_stack", 100)
        pot_size = data.get("pot_total", 10)
        bet_to_call = data.get("call_amount", 0)

        if action_name == "fold":
            return "FOLD"
        elif action_name == "check":
            return "CHECK"
        elif action_name == "call":
            return f"CALL {bet_to_call:.0f} BB" if bet_to_call > 0 else "CALL"
        elif action_name == "all-in":
            return f"ALL-IN ({my_stack:.0f} BB)"
        elif "bet_small" in action_name:
            bet_size = min(pot_size * 0.5, my_stack * 0.1)
            return f"BET {bet_size:.0f} BB"
        elif "bet_medium" in action_name:
            bet_size = min(pot_size * 0.75, my_stack * 0.15)
            return f"BET {bet_size:.0f} BB"
        elif "bet_large" in action_name:
            bet_size = min(pot_size * 1.0, my_stack * 0.25)
            return f"BET {bet_size:.0f} BB"
        elif "raise_small" in action_name:
            raise_size = min(bet_to_call * 2.5, my_stack * 0.15)
            return f"RAISE {raise_size:.0f} BB"
        elif "raise_medium" in action_name:
            raise_size = min(bet_to_call * 3.5, my_stack * 0.25)
            return f"RAISE {raise_size:.0f} BB"
        elif "raise_large" in action_name:
            raise_size = min(bet_to_call * 5.0, my_stack * 0.4)
            return f"RAISE {raise_size:.0f} BB"
        else:
            return action_name.upper()

    def _format_advanced_analysis(self, advanced_analysis):
        """
        Formate l'analyse avancée pour l'affichage

        Args:
            advanced_analysis (dict): Résultats de l'analyse avancée

        Returns:
            str: Analyse avancée formatée
        """
        if not advanced_analysis or "error" in advanced_analysis:
            return ""

        formatted_sections = []

        # Section Monte Carlo
        if advanced_analysis.get("monte_carlo") and "error" not in advanced_analysis["monte_carlo"]:
            mc = advanced_analysis["monte_carlo"]
            formatted_sections.append(
                f"🎲 **Monte Carlo** ({mc.get('total_simulations', 0)} simulations):\n"
                f"   • Équité précise: {mc.get('equity', 0):.1f}%\n"
                f"   • Victoires: {mc.get('win_rate', 0):.1f}% | Égalités: {mc.get('tie_rate', 0):.1f}%"
            )

        # Section Range vs Range
        if advanced_analysis.get("range_analysis") and "error" not in advanced_analysis["range_analysis"]:
            ra = advanced_analysis["range_analysis"]
            formatted_sections.append(
                f"📊 **Range vs Range**:\n"
                f"   • Équité moyenne: {ra.get('average_equity', 0):.1f}%\n"
                f"   • Combinaisons: {ra.get('total_combinations', 0)}\n"
                f"   • Distribution: {ra.get('equity_distribution', {}).get('min', 0):.1f}% - {ra.get('equity_distribution', {}).get('max', 0):.1f}%"
            )

        # Section GTO
        if advanced_analysis.get("gto_solution") and "error" not in advanced_analysis["gto_solution"]:
            gto = advanced_analysis["gto_solution"]
            if gto.get("mixed_strategy"):
                strategy_items = []
                for action, freq in sorted(gto["mixed_strategy"].items(), key=lambda x: x[1], reverse=True)[:3]:
                    if freq > 0.05:  # Seulement si fréquence > 5%
                        strategy_items.append(f"{action}: {freq*100:.0f}%")

                formatted_sections.append(
                    f"🎯 **Solution GTO**:\n"
                    f"   • Stratégie optimale: {', '.join(strategy_items)}\n"
                    f"   • Exploitabilité: {gto.get('exploitability', {}).get('exploitability_score', 0):.1f}%"
                )

        # Section Variance
        if advanced_analysis.get("variance_metrics") and "error" not in advanced_analysis["variance_metrics"]:
            vm = advanced_analysis["variance_metrics"]
            ci_95 = vm.get("confidence_intervals", {}).get("95%", {})
            formatted_sections.append(
                f"📈 **Analyse de Variance**:\n"
                f"   • Résultat attendu: {vm.get('expected_result', 0):.0f} BB\n"
                f"   • Intervalle 95%: {ci_95.get('lower', 0):.0f} à {ci_95.get('upper', 0):.0f} BB\n"
                f"   • Écart-type: {vm.get('standard_deviation', 0):.0f} BB"
            )

        # Section Recommandations Avancées
        if advanced_analysis.get("recommendations"):
            recs = advanced_analysis["recommendations"][:3]  # Prendre les 3 premières
            if recs:
                formatted_sections.append(
                    f"💡 **Recommandations Avancées**:\n" +
                    "\n".join([f"   • {rec}" for rec in recs])
                )

        # Section Insights
        if advanced_analysis.get("insights"):
            insights = advanced_analysis["insights"][:3]  # Prendre les 3 premiers
            if insights:
                formatted_sections.append(
                    f"🔍 **Insights Avancés**:\n" +
                    "\n".join([f"   • {insight}" for insight in insights])
                )

        if formatted_sections:
            return "\n🚀 **ANALYSE AVANCÉE** 🚀\n" + "\n\n".join(formatted_sections) + "\n"
        else:
            return ""

    def _format_unified_analysis(self, unified_analysis):
        """
        Formate l'analyse unifiée pour l'affichage

        Args:
            unified_analysis (dict): Résultats de l'analyse unifiée

        Returns:
            str: Analyse unifiée formatée
        """
        if not unified_analysis:
            return ""

        formatted_sections = []

        # Section principale avec consensus
        consensus = unified_analysis.get("consensus", "")
        winning_weight = unified_analysis.get("winning_weight", 0)
        total_weight = unified_analysis.get("total_weight", 1)
        confidence_pct = (winning_weight / total_weight) * 100

        if consensus:
            formatted_sections.append(
                f"🎯 **CONSENSUS DES ANALYSES** ({consensus}):\n"
                f"   • Force du consensus: {confidence_pct:.0f}%\n"
                f"   • Sources convergentes: {', '.join(unified_analysis.get('sources', []))}"
            )

        # Afficher les conflits s'il y en a
        conflicts = unified_analysis.get("conflicts", [])
        if conflicts:
            formatted_sections.append(
                f"⚠️ **Conflits détectés**:\n" +
                "\n".join([f"   • {conflict}" for conflict in conflicts])
            )

        # Détail des recommandations par source
        all_recs = unified_analysis.get("all_recommendations", {})
        if len(all_recs) > 1:
            rec_details = []
            for source, rec_data in all_recs.items():
                if source != "basic":  # Exclure l'analyse de base déjà affichée
                    action = rec_data.get("action", "N/A")
                    source_desc = rec_data.get("source", source)
                    weight = rec_data.get("weight", 0)
                    rec_details.append(f"   • {source_desc}: {action} (poids: {weight:.1f})")

            if rec_details:
                formatted_sections.append(
                    f"📊 **Détail des recommandations**:\n" +
                    "\n".join(rec_details)
                )

        if formatted_sections:
            return "\n🔥 **ANALYSE UNIFIÉE** 🔥\n" + "\n\n".join(formatted_sections) + "\n"
        else:
            return ""

    def _analyze_flush_draws(self, all_cards):
        """Analyse les tirages de couleur"""
        suits = [card[1] for card in all_cards]
        suit_counts = {suit: suits.count(suit) for suit in set(suits)}

        max_suit = max(suit_counts.values()) if suit_counts else 0

        if max_suit >= 5:
            return {"type": "Couleur", "description": "🌈 Couleur faite", "outs": 0}
        elif max_suit == 4:
            outs = 9  # 13 cartes de la couleur - 4 déjà utilisées
            return {"type": "Tirage couleur", "description": f"🎨 Tirage couleur ({outs} outs)", "outs": outs}
        elif max_suit == 3:
            outs = 10  # Tirage couleur backdoor
            return {"type": "Tirage couleur backdoor", "description": f"🎨 Tirage couleur backdoor ({outs} outs)", "outs": outs}

        return {"type": "Aucun", "description": "", "outs": 0}

    def _analyze_straight_draws(self, all_cards):
        """Analyse les tirages de quinte"""
        value_map = {"A": 14, "K": 13, "Q": 12, "J": 11, "T": 10,
                     "9": 9, "8": 8, "7": 7, "6": 6, "5": 5, "4": 4, "3": 3, "2": 2}

        values = [card[0] for card in all_cards]
        numeric_values = sorted(set([value_map.get(v, 0) for v in values if v in value_map]))

        # Vérifier quinte faite
        for i in range(len(numeric_values) - 4):
            if numeric_values[i+4] - numeric_values[i] == 4:
                return {"type": "Quinte", "description": "🎯 Quinte faite", "outs": 0}

        # Vérifier wheel faite
        wheel = [14, 5, 4, 3, 2]
        if all(val in numeric_values for val in wheel):
            return {"type": "Quinte", "description": "🎯 Quinte faite (wheel)", "outs": 0}

        # Vérifier tirages de quinte
        # Tirage ouvert (8 outs)
        for i in range(len(numeric_values) - 3):
            if numeric_values[i+3] - numeric_values[i] == 3:
                # Vérifier si c'est un tirage ouvert
                low_card = numeric_values[i] - 1
                high_card = numeric_values[i+3] + 1
                if low_card >= 2 and high_card <= 14:
                    return {"type": "Tirage quinte ouvert", "description": "🎯 Tirage quinte ouvert (8 outs)", "outs": 8}
                else:
                    return {"type": "Tirage quinte fermé", "description": "🎯 Tirage quinte fermé (4 outs)", "outs": 4}

        # Vérifier tirage wheel
        wheel_present = sum(1 for val in wheel if val in numeric_values)
        if wheel_present == 4:
            return {"type": "Tirage quinte wheel", "description": "🎯 Tirage quinte wheel (4 outs)", "outs": 4}

        return {"type": "Aucun", "description": "", "outs": 0}

    def _analyze_pair_improvements(self, all_cards, hand_cards):
        """Analyse les améliorations de paires/brelans"""
        values = [card[0] for card in all_cards]
        hand_values = [card[0] for card in hand_cards]
        value_counts = {value: values.count(value) for value in set(values)}

        # Vérifier les améliorations possibles
        improvements = []
        total_outs = 0

        for value in hand_values:
            count = value_counts.get(value, 0)
            if count == 1:  # Pas de paire, peut faire une paire
                improvements.append(f"Paire de {value}")
                total_outs += 3  # 3 cartes restantes de cette valeur
            elif count == 2:  # Paire, peut faire un brelan
                improvements.append(f"Brelan de {value}")
                total_outs += 2  # 2 cartes restantes de cette valeur
            elif count == 3:  # Brelan, peut faire un carré
                improvements.append(f"Carré de {value}")
                total_outs += 1  # 1 carte restante de cette valeur

        if improvements:
            desc = " ou ".join(improvements)
            return {"type": "Amélioration paires", "description": f"👥 {desc} ({total_outs} outs)", "outs": total_outs}

        return {"type": "Aucun", "description": "", "outs": 0}

    def _analyze_overcards(self, board_cards, hand_cards):
        """Analyse les overcards (cartes supérieures au board)"""
        if not board_cards:
            return {"type": "Aucun", "description": "", "outs": 0}

        value_map = {"A": 14, "K": 13, "Q": 12, "J": 11, "T": 10,
                     "9": 9, "8": 8, "7": 7, "6": 6, "5": 5, "4": 4, "3": 3, "2": 2}

        board_values = [value_map.get(card[0], 0) for card in board_cards]
        hand_values = [value_map.get(card[0], 0) for card in hand_cards]

        max_board = max(board_values) if board_values else 0
        overcards = [v for v in hand_values if v > max_board]

        if overcards:
            overcard_names = [k for k, v in value_map.items() if v in overcards]
            outs = len(overcards) * 3  # 3 cartes de chaque valeur
            return {"type": "Overcards", "description": f"⬆️ Overcards {'/'.join(overcard_names)} ({outs} outs)", "outs": outs}

        return {"type": "Aucun", "description": "", "outs": 0}

    def estimate_equity(self, board_cards, hand_cards):
        """
        Estime l'équité de la main contre une range adverse

        Args:
            board_cards (list): Liste des cartes du board au format Texas Hold'em
            hand_cards (list): Liste des cartes en main au format Texas Hold'em

        Returns:
            tuple: (min_equity, max_equity) en pourcentage
        """
        # Si nous sommes au pre-flop, utiliser l'évaluation pre-flop
        if not board_cards and len(hand_cards) == 2:
            category, _, hand_rank = self.evaluate_preflop_hand(hand_cards)

            # Estimer l'équité en fonction de la catégorie de la main pre-flop
            if category == 1:  # Mains premium
                return (65, 85)  # AA, KK, QQ, AKs, etc.
            elif category == 2:  # Mains fortes
                return (55, 70)  # JJ, TT, AQo, AJs, etc.
            elif category == 3:  # Mains solides
                return (50, 65)  # 99, AJo, KQs, etc.
            elif category == 4:  # Mains jouables
                return (45, 60)  # 88, 77, ATs, KJs, etc.
            elif category == 5:  # Mains spéculatives fortes
                return (40, 55)  # 66, A9s, KTs, QJs, etc.
            elif category == 6:  # Mains spéculatives
                return (35, 50)  # 55, A8s, KTo, etc.
            elif category == 7:  # Mains marginales
                return (30, 45)  # 44, A7s, K9s, etc.
            elif category == 8:  # Mains faibles
                return (25, 40)  # 33, A5s, K8s, etc.
            elif category == 9:  # Mains très faibles
                # Vérifier si c'est une main particulièrement faible (comme 5-3)
                if hand_rank < 10:  # Mains avec un rang très bas
                    return (10, 25)  # 5-3, 7-2, etc. (mains très faibles)
                else:
                    return (15, 30)  # 22, A4s, K7s, etc. (mains faibles standard)

        # Sinon, évaluer la main avec toutes les cartes
        hand_strength = self.evaluate_hand_strength(board_cards, hand_cards)

        if hand_strength == "Carré":
            return (85, 95)
        elif hand_strength == "Full House":
            return (80, 90)
        elif hand_strength == "Couleur":
            return (75, 85)
        elif hand_strength == "Quinte possible":
            return (60, 75)
        elif hand_strength == "Brelan":
            return (65, 80)
        elif hand_strength == "Deux paires":
            return (55, 70)
        elif hand_strength == "Paire":
            return (40, 60)
        else:
            return (20, 40)

    def recommend_action(self, equity, pot_odds, stack_to_pot_ratio, my_stack=100, pot=10, bet_to_call=0):
        """
        Recommande une action optimale avec des montants proportionnels au stack

        Args:
            equity (tuple): (min_equity, max_equity) en pourcentage
            pot_odds (float): Pot odds en pourcentage
            stack_to_pot_ratio (float): Rapport stack-to-pot
            my_stack (float): Nombre de jetons du joueur
            pot (float): Taille du pot
            bet_to_call (float): Montant à suivre

        Returns:
            tuple: (action, raison)
        """
        min_equity, max_equity = equity
        avg_equity = (min_equity + max_equity) / 2

        # Catégoriser les tapis de manière plus nuancée
        if my_stack < 15:
            stack_category = "très_court"
        elif my_stack < 25:
            stack_category = "court"
        elif my_stack < 50:
            stack_category = "moyen"
        elif my_stack < 100:
            stack_category = "profond"
        else:
            stack_category = "très_profond"

        # Calculer des montants proportionnels au stack
        def get_proportional_bet(percentage, min_bb=2, max_bb=None):
            """Calcule un montant proportionnel au stack"""
            proportional = my_stack * (percentage / 100)
            proportional = max(proportional, min_bb)  # Minimum
            if max_bb:
                proportional = min(proportional, max_bb)  # Maximum
            return proportional

        # Montants standards selon le stack
        if stack_category == "très_court":  # < 15 BB
            small_raise = get_proportional_bet(20, 2, my_stack * 0.8)  # 20% du stack
            medium_raise = get_proportional_bet(40, 3, my_stack * 0.9)  # 40% du stack
            big_raise = my_stack  # All-in
        elif stack_category == "court":  # 15-25 BB
            small_raise = get_proportional_bet(12, 2, 6)  # 12% du stack, max 6BB
            medium_raise = get_proportional_bet(20, 3, 10)  # 20% du stack, max 10BB
            big_raise = get_proportional_bet(40, 5, my_stack * 0.8)  # 40% du stack
        elif stack_category == "moyen":  # 25-50 BB
            small_raise = get_proportional_bet(8, 2, 4)  # 8% du stack, max 4BB
            medium_raise = get_proportional_bet(12, 3, 8)  # 12% du stack, max 8BB
            big_raise = get_proportional_bet(25, 5, 15)  # 25% du stack, max 15BB
        elif stack_category == "profond":  # 50-100 BB
            small_raise = get_proportional_bet(4, 2, 3)  # 4% du stack, max 3BB
            medium_raise = get_proportional_bet(6, 3, 5)  # 6% du stack, max 5BB
            big_raise = get_proportional_bet(15, 5, 12)  # 15% du stack, max 12BB
        else:  # très_profond > 100 BB
            small_raise = get_proportional_bet(2, 2, 3)  # 2% du stack, max 3BB
            medium_raise = get_proportional_bet(3, 3, 4)  # 3% du stack, max 4BB
            big_raise = get_proportional_bet(8, 5, 10)  # 8% du stack, max 10BB

        # Ajuster les recommandations en fonction du nombre de jetons
        short_stack = my_stack < 20  # Tapis court
        medium_stack = 20 <= my_stack < 50  # Tapis moyen
        deep_stack = my_stack >= 50  # Tapis profond

        # Vérifier si nous sommes au pre-flop (en utilisant l'équité comme indicateur)
        is_preflop = False
        # Plage d'équité élargie pour inclure les mains très faibles
        if 10 <= min_equity <= 65 and 25 <= max_equity <= 85:
            is_preflop = True

        # Recommandations spécifiques pour le pre-flop
        if is_preflop:
            # Catégoriser la main en fonction de l'équité
            if avg_equity >= 75:  # Mains premium (AA, KK)
                if bet_to_call == 0:  # Personne n'a misé
                    if stack_category in ["très_court", "court"]:
                        return ("all-in", f"Main premium avec un tapis {stack_category}, maximiser la valeur.")
                    else:
                        return (f"raise {medium_raise:.0f} BB", f"Main premium, relance proportionnelle ({medium_raise/my_stack*100:.1f}% du stack).")
                elif bet_to_call <= small_raise:  # Petite mise à suivre
                    if stack_category == "très_court":
                        return ("all-in", "Main premium avec un tapis très court, maximiser la valeur.")
                    else:
                        return (f"raise {big_raise:.0f} BB", f"Main premium, grosse relance ({big_raise/my_stack*100:.1f}% du stack).")
                else:  # Grosse mise à suivre
                    if bet_to_call > my_stack * 0.3:
                        return ("all-in", "Main premium face à une grosse mise, maximiser la valeur.")
                    else:
                        return (f"raise {medium_raise:.0f} BB", f"Main premium, relance proportionnelle ({medium_raise/my_stack*100:.1f}% du stack).")

            elif avg_equity >= 60:  # Mains fortes (QQ, JJ, AK)
                if bet_to_call == 0:  # Personne n'a misé
                    if stack_category in ["très_court", "court"]:
                        return ("all-in", f"Main forte avec un tapis {stack_category}, maximiser la valeur.")
                    else:
                        return (f"raise {small_raise:.0f} BB", f"Main forte, relance proportionnelle ({small_raise/my_stack*100:.1f}% du stack).")
                elif bet_to_call <= small_raise:  # Petite mise à suivre
                    if stack_category == "très_court":
                        return ("all-in", "Main forte avec un tapis très court, maximiser la valeur.")
                    else:
                        return (f"raise {medium_raise:.0f} BB", f"Main forte, relance proportionnelle ({medium_raise/my_stack*100:.1f}% du stack).")
                else:  # Grosse mise à suivre
                    if bet_to_call > my_stack * 0.25:
                        return (f"call {bet_to_call:.0f} BB", "Main forte, suivre pour voir le flop.")
                    else:
                        return (f"raise {small_raise:.0f} BB", f"Main forte, petite relance ({small_raise/my_stack*100:.1f}% du stack).")

            elif avg_equity >= 50:  # Mains solides (TT, 99, AQ, AJs)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main solide avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 2.5BB", "Main solide, relance standard pour construire le pot.")
                    else:
                        return ("raise 2.5BB", "Main solide, relance standard pour construire le pot.")
                elif bet_to_call <= 3:  # Petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main solide avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 2.5x", "Main solide, relance pour isoler et construire le pot.")
                    else:
                        return ("raise 2.5x", "Main solide, relance pour isoler et construire le pot.")
                else:  # Grosse mise à suivre
                    if short_stack:
                        return ("all-in", "Main solide avec un tapis court, maximiser la valeur.")
                    elif bet_to_call > my_stack * 0.3:
                        return ("fold", "Main solide mais trop coûteux pour suivre.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Main solide, suivre pour voir le flop.")

            elif avg_equity >= 42:  # Mains jouables (88, 77, AJo, KQs)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main jouable avec un tapis court, prendre l'initiative.")
                    elif medium_stack:
                        return ("raise 2BB", "Main jouable, petite relance pour prendre l'initiative.")
                    else:
                        return ("raise 2BB", "Main jouable, petite relance pour prendre l'initiative.")
                elif bet_to_call <= 2:  # Très petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main jouable avec un tapis court, prendre l'initiative.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Main jouable, suivre pour voir le flop.")
                else:  # Mise à suivre plus importante
                    if short_stack and bet_to_call <= my_stack * 0.2:
                        return ("all-in", "Main jouable avec un tapis court, dernière chance.")
                    elif bet_to_call > my_stack * 0.15:
                        return ("fold", "Main jouable mais trop coûteux pour suivre.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Main jouable, suivre pour voir le flop.")

            elif avg_equity >= 35:  # Mains spéculatives (66, 55, A9s, KTs)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main spéculative avec un tapis court, prendre l'initiative.")
                    elif deep_stack:
                        return ("raise 2BB", "Main spéculative, petite relance en position tardive.")
                    else:
                        return ("limp", "Main spéculative, entrer doucement dans le pot.")
                elif bet_to_call <= 2:  # Très petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main spéculative avec un tapis court, prendre l'initiative.")
                    elif deep_stack and pot_odds < avg_equity:
                        return (f"call {bet_to_call:.0f} BB", "Main spéculative, suivre pour voir le flop.")
                    else:
                        return ("fold", "Main spéculative, se coucher face à une relance.")
                else:  # Mise à suivre plus importante
                    if short_stack and bet_to_call <= my_stack * 0.15:
                        return ("all-in", "Main spéculative avec un tapis court, dernière chance.")
                    else:
                        return ("fold", "Main spéculative, se coucher face à une relance importante.")

            elif avg_equity >= 25:  # Mains faibles mais jouables
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main faible avec un tapis court, dernière chance.")
                    elif deep_stack:
                        return ("limp", "Main faible, entrer doucement dans le pot en position tardive.")
                    else:
                        return ("fold", "Main faible, se coucher.")
                else:  # Mise à suivre
                    if short_stack and bet_to_call <= my_stack * 0.1:
                        return ("all-in", "Main faible avec un tapis court, dernière chance.")
                    else:
                        return ("fold", "Main faible, se coucher face à une mise.")
            else:  # Mains extrêmement faibles (comme 5-3, 7-2)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack and avg_equity > 15:  # Même avec un tapis court, être plus sélectif
                        return ("all-in", "Main très faible avec un tapis court, stratégie désespérée.")
                    else:
                        return ("fold", "Main extrêmement faible, se coucher immédiatement.")
                else:  # Mise à suivre
                    return ("fold", "Main extrêmement faible, se coucher face à toute mise.")

        # Recommandations pour le post-flop (code existant)
        else:
            # Décider de l'action en fonction de l'équité, des pot odds et du nombre de jetons
            if avg_equity < 25:
                if short_stack and avg_equity > 15:
                    return ("all-in", "Tapis court avec une main faible mais jouable.")
                return ("fold", "Main faible avec peu d'équité contre la range adverse.")

            if pot_odds > 0 and avg_equity > pot_odds:
                # Cas où l'équité est supérieure aux pot odds (rentable de suivre)
                if avg_equity > 75:
                    # Main très forte
                    if short_stack:
                        return ("all-in", "Main très forte avec un tapis court, maximiser la valeur.")
                    elif medium_stack and stack_to_pot_ratio < 3:
                        return ("all-in", "Main très forte avec un tapis moyen, maximiser la valeur.")
                    elif deep_stack:
                        if pot > my_stack * 0.3:
                            return ("value-bet 3/4 pot", "Main très forte avec un gros pot, extraire de la valeur.")
                        else:
                            return ("value-bet 2/3 pot", "Main très forte, construire le pot.")
                    else:
                        return ("value-bet 3/4 pot", "Main très forte, extraire de la valeur.")
                elif avg_equity > 60:
                    # Bonne main
                    if short_stack:
                        return ("all-in", "Bonne main avec un tapis court.")
                    elif medium_stack:
                        if pot > my_stack * 0.2:
                            return ("value-bet 1/2 pot", "Bonne main avec un tapis moyen, construire le pot.")
                        else:
                            return (f"call {bet_to_call:.0f} BB", "Bonne main, contrôler le pot avec un tapis moyen.")
                    else:
                        return ("value-bet 1/2 pot", "Bonne main avec équité favorable contre la range adverse.")
                else:
                    # Main moyenne
                    if short_stack and avg_equity > 40:
                        return ("all-in", "Main moyenne avec un tapis court.")
                    elif medium_stack and avg_equity > 50:
                        return (f"call {bet_to_call:.0f} BB", "Main moyenne avec un tapis moyen, voir le prochain tour.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Équité suffisante par rapport aux cotes du pot.")
            elif pot_odds == 0:
                # Cas où il n'y a pas de mise à suivre (check possible)
                if avg_equity > 60:
                    if short_stack:
                        return ("all-in", "Bonne main avec un tapis court, prendre l'initiative.")
                    elif medium_stack:
                        if pot > my_stack * 0.15:
                            return ("value-bet 1/2 pot", "Bonne main avec un tapis moyen, prendre l'initiative.")
                        else:
                            return ("value-bet 1/3 pot", "Bonne main, mise de sondage avec un tapis moyen.")
                    else:
                        return ("value-bet 2/3 pot", "Bonne main, prendre l'initiative.")
                elif avg_equity > 40:
                    if short_stack:
                        return ("all-in", "Main moyenne avec un tapis court.")
                    else:
                        return ("check", "Main moyenne, contrôler le pot.")
                else:
                    return ("check", "Main faible, voir le prochain tour gratuitement.")
            else:
                # Cas où l'équité est inférieure aux pot odds (non rentable de suivre)
                if avg_equity > 50 and stack_to_pot_ratio < 5:
                    if short_stack:
                        return ("all-in", "Équité marginale mais tapis court.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Équité marginale mais cote implicite favorable.")
                elif short_stack and avg_equity > 35:
                    return ("all-in", "Tapis court, dernière chance avec une main jouable.")
                else:
                    return ("fold", "Équité insuffisante par rapport aux cotes du pot.")

    def format_analysis(self, data, analysis):
        """
        Formate l'analyse pour l'affichage

        Args:
            data (dict): Données de poker
            analysis (dict): Résultats de l'analyse

        Returns:
            str: Analyse formatée
        """
        # Formater les cartes du board
        board_str = "Non détectées"
        if analysis["board_cards"]:
            board_str = " ".join(analysis["board_cards"])

        # Formater les cartes en main
        hand_str = "Non détectées"
        if analysis["hand_cards"]:
            hand_str = " ".join(analysis["hand_cards"])

        # Indicateurs visuels pour les cartes
        board_indicator = "🟢 " if analysis["board_cards"] else "🔴 "
        hand_indicator = "🟢 " if analysis["hand_cards"] else "🔴 "

        # Indicateurs visuels pour les mises et les jetons (pot retiré - seul pot_total utilisé)
        my_stack_indicator = "🟢 " if analysis.get("detected_chips", False) else "🔴 "
        player_stacks_indicator = "🟢 " if analysis.get("detected_chips", False) else "🔴 "
        bets_indicator = "🟢 " if analysis.get("detected_bets", False) else "🔴 "

        # Formater l'équité
        min_equity, max_equity = analysis["equity"]
        avg_equity = (min_equity + max_equity) / 2
        equity_str = f"{min_equity}-{max_equity}" if min_equity != max_equity else f"{min_equity}"

        # Formater les pot odds
        pot_odds_str = f"{analysis['pot_odds']:.1f}" if analysis['pot_odds'] > 0 else "N/A"

        # Formater l'action recommandée (unifiée si disponible)
        if analysis.get("unified_analysis"):
            # Utiliser la recommandation unifiée
            action_str = analysis["recommended_action"]
            confidence_score = analysis.get("confidence_score", 0)
            unified_info = analysis.get("unified_analysis", {})

            # Ajouter le score de confiance
            if confidence_score > 0:
                confidence_emoji = "🔥" if confidence_score >= 80 else "✅" if confidence_score >= 60 else "⚠️"
                action_str = f"{confidence_emoji} {action_str} ({confidence_score:.0f}% confiance)"

            # Ajouter l'info de consensus
            consensus = unified_info.get("consensus", "")
            if consensus:
                action_str += f" | {consensus}"
        else:
            # Utiliser l'action de base
            action_str = analysis["recommended_action"].upper() if analysis["recommended_action"] else "ATTENDRE"

            # Ajouter un indicateur visuel pour les recommandations "fold"
            if "fold" in action_str.lower():
                action_str = f"⛔ SE COUCHER ⛔"

            # Ajouter une note sur le montant à suivre si nécessaire
            elif analysis["bet_to_call"] > 0 and "call" in action_str.lower() and "BB" not in action_str:
                action_str = f"{action_str} {analysis['bet_to_call']:.0f} BB"

        # Formater les informations sur les jetons - UTILISER LA MOYENNE AU LIEU DE MES JETONS
        avg_stack = data.get('average_stack', 0)
        if avg_stack > 0:
            stack_info = f"{avg_stack:.1f} (moyenne)"
            # Déterminer la catégorie de tapis basée sur la MOYENNE
            if avg_stack < 20:
                stack_category = " (Tapis court ⚠️)"
            elif avg_stack < 50:
                stack_category = " (Tapis moyen)"
            else:
                stack_category = " (Tapis profond 💰)"
        else:
            # Fallback sur mes jetons si pas de moyenne
            stack_info = f"{data['my_stack']:.0f}" if data['my_stack'] != 100 else "100"
            if data['my_stack'] < 20:
                stack_category = " (Tapis court ⚠️)"
            elif data['my_stack'] < 50:
                stack_category = " (Tapis moyen)"
            elif data['my_stack'] >= 50:
                stack_category = " (Tapis profond 💰)"

        # Les informations sur les adversaires et les mises sont maintenant directement dans le template

        # Horodatage
        timestamp = time.strftime("%H:%M:%S")

        # Déterminer le nombre de régions détectées
        regions_count = len(analysis.get("detected_regions", []))

        # Déterminer le nombre de corrections manuelles
        manual_corrections_count = len(data.get("manual_corrections", []))

        # Créer l'info des régions
        regions_info = f"({regions_count} régions détectées"
        if manual_corrections_count > 0:
            regions_info += f", {manual_corrections_count} corrections manuelles"
        regions_info += ")" if regions_count > 0 or manual_corrections_count > 0 else ""

        # AJOUTER LES INFORMATIONS DE JEU AVANCÉES DANS L'AFFICHAGE
        game_info_lines = []

        # Afficher le bouton détecté
        button_position = data.get('button_position')
        if button_position:
            game_info_lines.append(f"🔘 Bouton: {button_position}")

        # Afficher la profondeur des tapis avec la moyenne
        stack_depth = data.get('stack_depth')
        avg_stack = data.get('average_stack', 0)
        if stack_depth:
            if avg_stack > 0:
                game_info_lines.append(f"💰 Tapis {stack_depth} (moyenne: {avg_stack:.1f} BB)")
            else:
                game_info_lines.append(f"💰 Tapis {stack_depth}")

        # Afficher les pot odds calculés
        pot_odds_calc = data.get('pot_odds_calculated', 0)
        if pot_odds_calc > 0:
            game_info_lines.append(f"📊 Pot odds: {pot_odds_calc:.1f}%")

        # Afficher le nombre de régions de jeu détectées
        game_regions = data.get('game_regions_detected', 0)
        if game_regions > 0:
            game_info_lines.append(f"📈 Infos jeu: {game_regions} régions")

        # Indicateur visuel pour le pot total (seule source de pot)
        pot_total_indicator = "🎯 " if "pot_total" in data.get("detected_regions", []) else "⚪ "

        # Indicateurs visuels pour les all-in
        allins_indicator = "🔥 " if data["player_allins"] else "⚪ "

        # Construire les informations sur les mises et all-in
        bets_info = []
        allins_info = []

        # Séparer les mises normales et les all-in
        for player, bet_value in data["player_bets"].items():
            player_short = f"J{player[-1:]}"
            if player in data["player_allins"]:
                # C'est un all-in : afficher dans la section all-in
                allins_info.append(f"{player_short}: {bet_value:.0f}")
            else:
                # Mise normale : afficher dans la section mises
                bets_info.append(f"{player_short}: {bet_value:.0f}")

        # Construire le template avec les nouvelles informations
        template = f"""### Situation {regions_info}
Board : {board_indicator}{board_str}
Main : {hand_indicator}{hand_str}
{pot_total_indicator}Pot total : {data['pot_total']:.0f} BB
Tapis moyen : {my_stack_indicator}{stack_info} BB{stack_category}
Mes jetons : {data['my_stack']:.0f} BB
{player_stacks_indicator}Adversaires : {', '.join([f"J{k[-1:]}: {v:.0f}" for k, v in data["player_stacks"].items()]) if data["player_stacks"] else "Non détectés"}
{bets_indicator}Mises : {', '.join(bets_info) if bets_info else "Non détectées"}
{allins_indicator}All-in : {', '.join(allins_info) if allins_info else "Aucun"}
{chr(10).join(game_info_lines) if game_info_lines else ""}

# 🚨 **ACTION RECOMMANDÉE** 🚨
## ⭐ **{action_str}** ⭐
### *{analysis['action_reason']}*
{"### ⚠️ **VOUS DEVRIEZ VOUS COUCHER DANS CETTE SITUATION** ⚠️" if "fold" in analysis["recommended_action"].lower() else ""}

📊 **Analyse détaillée** :
• Main actuelle : {analysis['hand_strength']}
• Équité estimée vs range ({equity_str}) : ~{avg_equity:.1f}%
• Pot odds : {pot_odds_str}%   Cote implicite : {analysis['implied_odds']}
• Montant recommandé : {self.format_recommended_amount(analysis.get('recommended_amount', 0), analysis['recommended_action'], analysis['bet_to_call'])}

{chr(10).join(analysis['notes']) if analysis['notes'] else ""}

{self._format_unified_analysis(analysis.get('unified_analysis', {}))}

{self._format_advanced_analysis(analysis.get('advanced_analysis', {}))}

---

### 🃏 Référence des mains (ordre de force)
9️⃣ Quinte flush royale (A-K-Q-J-10 même couleur)
8️⃣ Quinte flush (5 cartes consécutives même couleur)
7️⃣ Carré (4 cartes identiques)
6️⃣ Full house (brelan + paire)
5️⃣ Couleur (5 cartes même couleur)
4️⃣ Quinte (5 cartes consécutives)
3️⃣ Brelan (3 cartes identiques)
2️⃣ Deux paires
1️⃣ Paire
0️⃣ Hauteur (carte haute)

Dernière mise à jour : {timestamp}"""

        return template


class PokerDataCache:
    """Classe pour gérer le cache des données et des analyses de poker."""

    def __init__(self, max_size=20):
        """
        Initialise le cache avec une taille maximale.

        Args:
            max_size (int): Nombre maximum d'entrées dans le cache
        """
        self.max_size = max_size
        self.cache = OrderedDict()  # Utiliser OrderedDict pour conserver l'ordre d'insertion
        self.hits = 0  # Nombre de fois où le cache a été utilisé
        self.misses = 0  # Nombre de fois où le cache n'a pas été utilisé
        self.total_requests = 0  # Nombre total de requêtes

    def get_hash(self, data):
        """
        Calcule un hash unique pour les données de poker.

        Args:
            data (dict): Données de poker

        Returns:
            str: Hash unique des données
        """
        # Créer une représentation stable des données
        key_parts = [
            data.get("hand_cards_text", ""),
            data.get("board_cards_text", ""),
            str(data.get("pot", 0)),
            str(data.get("pot_total", 0)),
            str(data.get("effective_stack", 0))
        ]

        # Joindre les parties et calculer un hash
        key = "|".join(key_parts)
        return key

    def get(self, data):
        """
        Récupère les résultats d'analyse du cache si disponibles.

        Args:
            data (dict): Données de poker

        Returns:
            tuple: (analysis, formatted_analysis, from_cache) ou (None, None, False) si non trouvé
        """
        self.total_requests += 1

        # Calculer le hash des données
        data_hash = self.get_hash(data)

        # Vérifier si le hash est dans le cache
        if data_hash in self.cache:
            # Déplacer l'entrée à la fin (la plus récemment utilisée)
            entry = self.cache.pop(data_hash)
            self.cache[data_hash] = entry

            self.hits += 1
            return entry["analysis"], entry["formatted_analysis"], True

        self.misses += 1
        return None, None, False

    def put(self, data, analysis, formatted_analysis):
        """
        Ajoute une entrée au cache.

        Args:
            data (dict): Données de poker
            analysis (dict): Résultats de l'analyse
            formatted_analysis (str): Analyse formatée
        """
        # Calculer le hash des données
        data_hash = self.get_hash(data)

        # Ajouter au cache
        self.cache[data_hash] = {
            "analysis": analysis,
            "formatted_analysis": formatted_analysis,
            "timestamp": time.time()
        }

        # Si le cache dépasse la taille maximale, supprimer l'entrée la plus ancienne
        if len(self.cache) > self.max_size:
            self.cache.popitem(last=False)  # Supprimer le premier élément (le plus ancien)

    def clear(self):
        """
        Vide complètement le cache.

        Returns:
            int: Nombre d'entrées supprimées
        """
        count = len(self.cache)
        self.cache.clear()
        return count
