#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Diagnostic spécifique pour identifier le problème de crash lors de la détection.
"""

import sys
import os
import traceback
import psutil
import time
from datetime import datetime

def monitor_memory():
    """Surveille l'utilisation mémoire"""
    process = psutil.Process()
    return {
        'rss': process.memory_info().rss / 1024 / 1024,  # MB
        'vms': process.memory_info().vms / 1024 / 1024,  # MB
        'percent': process.memory_percent()
    }

def test_detector_import():
    """Test d'import du détecteur"""
    print("🔍 TEST D'IMPORT DU DÉTECTEUR")
    print("=" * 40)
    
    try:
        print("1. Import des modules de base...")
        import cv2
        import numpy as np
        from PyQt5.QtWidgets import QApplication
        print("   ✅ Modules de base importés")
        
        print("2. Import du détecteur...")
        from detector import Detector
        print("   ✅ Detector importé")
        
        print("3. Import de detector_gui...")
        # Ne pas importer la classe complète pour éviter l'interface
        import detector_gui
        print("   ✅ detector_gui importé")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur d'import: {e}")
        traceback.print_exc()
        return False

def test_detector_creation():
    """Test de création du détecteur"""
    print("\n🔧 TEST DE CRÉATION DU DÉTECTEUR")
    print("=" * 40)
    
    try:
        from detector import Detector
        
        print("1. Vérification du fichier de config...")
        config_path = "calibration_config.json"
        if os.path.exists(config_path):
            print(f"   ✅ Config trouvée: {config_path}")
        else:
            print(f"   ⚠️ Config manquante: {config_path}")
            return False
        
        print("2. Création du détecteur...")
        mem_before = monitor_memory()
        print(f"   Mémoire avant: {mem_before['rss']:.1f} MB")
        
        detector = Detector(config_path)
        
        mem_after = monitor_memory()
        print(f"   Mémoire après: {mem_after['rss']:.1f} MB")
        print(f"   Différence: {mem_after['rss'] - mem_before['rss']:.1f} MB")
        
        print("   ✅ Détecteur créé avec succès")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur création détecteur: {e}")
        traceback.print_exc()
        return False

def test_detection_simulation():
    """Test de simulation de détection"""
    print("\n🎯 TEST DE SIMULATION DE DÉTECTION")
    print("=" * 40)
    
    try:
        from detector import Detector
        import mss
        
        print("1. Création du détecteur...")
        detector = Detector("calibration_config.json")
        
        print("2. Capture d'écran de test...")
        with mss.mss() as sct:
            # Capture d'une petite zone pour test
            monitor = {"top": 100, "left": 100, "width": 200, "height": 200}
            screenshot = sct.grab(monitor)
            
        print("   ✅ Capture d'écran réussie")
        
        print("3. Test de détection simulée...")
        mem_before = monitor_memory()
        print(f"   Mémoire avant détection: {mem_before['rss']:.1f} MB")
        
        # Simuler une détection sans vraie analyse
        # (pour éviter le crash)
        results = {
            "card_1": {"value": "A", "suit": "Cœur", "confidence": 0.9},
            "card_2": {"value": "K", "suit": "Pique", "confidence": 0.8}
        }
        
        mem_after = monitor_memory()
        print(f"   Mémoire après détection: {mem_after['rss']:.1f} MB")
        print(f"   Différence: {mem_after['rss'] - mem_before['rss']:.1f} MB")
        
        print("   ✅ Simulation de détection réussie")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur simulation détection: {e}")
        traceback.print_exc()
        return False

def test_poker_advisor():
    """Test du conseiller poker"""
    print("\n🃏 TEST DU CONSEILLER POKER")
    print("=" * 40)
    
    try:
        print("1. Import du conseiller...")
        from poker_advisor_light import PokerAdvisorLight
        print("   ✅ PokerAdvisorLight importé")
        
        print("2. Création du conseiller...")
        mem_before = monitor_memory()
        advisor = PokerAdvisorLight()
        mem_after = monitor_memory()
        
        print(f"   Mémoire avant: {mem_before['rss']:.1f} MB")
        print(f"   Mémoire après: {mem_after['rss']:.1f} MB")
        print(f"   Différence: {mem_after['rss'] - mem_before['rss']:.1f} MB")
        
        print("3. Test d'analyse...")
        test_results = {
            "hand_card_1": {"value": "A", "suit": "Cœur"},
            "hand_card_2": {"value": "K", "suit": "Pique"},
            "board_card_1": {"value": "Q", "suit": "Trèfle"},
            "board_card_2": {"value": "J", "suit": "Carreau"},
            "board_card_3": {"value": "10", "suit": "Cœur"}
        }
        
        analysis, formatted = advisor.analyze_detection_results(test_results)
        print(f"   ✅ Analyse réussie: {len(formatted) if formatted else 0} caractères")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur conseiller poker: {e}")
        traceback.print_exc()
        return False

def test_memory_leak():
    """Test de fuite mémoire"""
    print("\n💾 TEST DE FUITE MÉMOIRE")
    print("=" * 40)
    
    try:
        from detector import Detector
        
        print("1. Test de créations multiples...")
        mem_start = monitor_memory()
        print(f"   Mémoire initiale: {mem_start['rss']:.1f} MB")
        
        for i in range(5):
            print(f"   Création #{i+1}...")
            detector = Detector("calibration_config.json")
            
            # Simuler une détection
            time.sleep(0.1)
            
            # Forcer la suppression
            del detector
            
            mem_current = monitor_memory()
            print(f"   Mémoire après #{i+1}: {mem_current['rss']:.1f} MB")
        
        # Forcer le garbage collection
        import gc
        gc.collect()
        
        mem_final = monitor_memory()
        print(f"   Mémoire finale: {mem_final['rss']:.1f} MB")
        print(f"   Différence totale: {mem_final['rss'] - mem_start['rss']:.1f} MB")
        
        if mem_final['rss'] - mem_start['rss'] > 100:  # Plus de 100MB
            print("   ⚠️ Possible fuite mémoire détectée")
        else:
            print("   ✅ Pas de fuite mémoire significative")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test mémoire: {e}")
        traceback.print_exc()
        return False

def check_system_resources():
    """Vérification des ressources système"""
    print("\n🖥️ VÉRIFICATION DES RESSOURCES SYSTÈME")
    print("=" * 40)
    
    try:
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   CPU: {cpu_percent}%")
        
        # RAM
        memory = psutil.virtual_memory()
        print(f"   RAM: {memory.used / 1024**3:.1f}GB / {memory.total / 1024**3:.1f}GB ({memory.percent}%)")
        
        # Processus Python
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            try:
                if 'python' in proc.info['name'].lower():
                    python_processes.append(proc)
            except:
                pass
        
        print(f"   Processus Python actifs: {len(python_processes)}")
        for proc in python_processes:
            try:
                mem_mb = proc.info['memory_info'].rss / 1024 / 1024
                print(f"     PID {proc.info['pid']}: {mem_mb:.1f} MB")
            except:
                pass
        
        # GPU (si disponible)
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                print(f"   GPU: {gpu_memory:.1f}GB disponible")
            else:
                print("   GPU: CUDA non disponible")
        except:
            print("   GPU: PyTorch non disponible")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur vérification système: {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🔍 DIAGNOSTIC DE CRASH LORS DE LA DÉTECTION")
    print("=" * 60)
    
    # Vérification système
    check_system_resources()
    
    # Tests séquentiels
    tests = [
        ("Import du détecteur", test_detector_import),
        ("Création du détecteur", test_detector_creation),
        ("Simulation de détection", test_detection_simulation),
        ("Conseiller poker", test_poker_advisor),
        ("Fuite mémoire", test_memory_leak)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ CRASH LORS DU TEST: {test_name}")
            print(f"Erreur: {e}")
            traceback.print_exc()
            results[test_name] = False
            break
    
    # Résumé
    print("\n📋 RÉSUMÉ DES TESTS")
    print("=" * 40)
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    # Recommandations
    print("\n💡 RECOMMANDATIONS")
    print("=" * 40)
    
    failed_tests = [name for name, success in results.items() if not success]
    if failed_tests:
        print(f"Tests échoués: {failed_tests}")
        print("Actions recommandées:")
        print("   1. Vérifier les dépendances manquantes")
        print("   2. Réduire la résolution de capture")
        print("   3. Désactiver temporairement le conseiller poker")
        print("   4. Vérifier l'espace disque disponible")
    else:
        print("Tous les tests sont réussis.")
        print("Le problème pourrait venir de:")
        print("   1. Threading/concurrence")
        print("   2. Gestion des événements PyQt5")
        print("   3. Interaction avec le système de fenêtres")
        print("   4. Problème spécifique à la détection en temps réel")

if __name__ == "__main__":
    main()
