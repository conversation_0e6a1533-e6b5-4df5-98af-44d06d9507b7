#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la configuration originale restaurée
"""

import cv2
import numpy as np
import time

def test_paddleocr_original():
    """Test de PaddleOCR avec la configuration originale"""
    print("🔍 Test PaddleOCR configuration originale...")
    
    try:
        from paddleocr import PaddleOCR
        
        # Créer une image de test simple
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        cv2.putText(test_image, "♠", (150, 120), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        
        print("🔄 Initialisation PaddleOCR (CPU d'abord)...")
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='fr',
            use_gpu=False,
            show_log=False,
            enable_mkldnn=True,
            use_mp=True
        )
        print("✅ PaddleOCR CPU initialisé")
        
        # Test de détection
        print("🔄 Test de détection...")
        start_time = time.time()
        results = ocr.ocr(test_image, cls=True)
        detection_time = time.time() - start_time
        
        print(f"✅ Détection terminée en {detection_time:.2f}s")
        print(f"✅ Résultats bruts: {results}")
        
        # Analyser les résultats
        detected_texts = []
        if results and results[0]:
            for line in results[0]:
                if len(line) >= 2:
                    text = line[1][0] if isinstance(line[1], (list, tuple)) else str(line[1])
                    confidence = line[1][1] if isinstance(line[1], (list, tuple)) and len(line[1]) > 1 else 1.0
                    
                    if confidence > 0.5:
                        detected_texts.append(text.strip().upper())
                        print(f"  - Texte: '{text}' (confiance: {confidence:.2f})")
        
        final_text = ' '.join(detected_texts)
        print(f"✅ Texte final détecté: '{final_text}'")
        
        return len(final_text) > 0
        
    except Exception as e:
        print(f"❌ Erreur PaddleOCR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detector_original():
    """Test du détecteur avec la configuration originale"""
    print("\n🔍 Test du détecteur restauré...")
    
    try:
        from detector import Detector
        
        print("🔄 Initialisation du détecteur (CPU)...")
        detector = Detector(use_cuda=False)
        
        # Créer une image de test
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "K", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        print("🔄 Test de détection rapide...")
        start_time = time.time()
        result = detector.detect_text_fast(test_image)
        detection_time = time.time() - start_time
        
        print(f"✅ Détection terminée en {detection_time:.2f}s")
        print(f"✅ Résultat: '{result}'")
        
        # Test de couleur
        colors = detector.detect_colors_fast(test_image)
        print(f"✅ Couleurs: {colors}")
        
        return len(result) > 0
        
    except Exception as e:
        print(f"❌ Erreur détecteur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cuda_if_available():
    """Test CUDA si disponible"""
    print("\n🔍 Test CUDA si disponible...")
    
    try:
        import paddle
        
        if paddle.device.is_compiled_with_cuda():
            print("✅ PaddlePaddle compilé avec CUDA")
            
            # Test du détecteur avec CUDA
            from detector import Detector
            
            print("🔄 Test détecteur avec CUDA...")
            detector = Detector(use_cuda=True)
            
            # Test simple
            test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, "Q", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
            
            start_time = time.time()
            result = detector.detect_text_fast(test_image)
            detection_time = time.time() - start_time
            
            print(f"✅ Détection CUDA: '{result}' en {detection_time:.2f}s")
            return True
        else:
            print("⚠️ PaddlePaddle non compilé avec CUDA")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test CUDA: {e}")
        return False

def main():
    print("🔥 TEST DE LA CONFIGURATION ORIGINALE RESTAURÉE")
    print("=" * 60)
    
    # Test 1: PaddleOCR direct
    paddleocr_works = test_paddleocr_original()
    
    # Test 2: Détecteur restauré
    detector_works = test_detector_original()
    
    # Test 3: CUDA si disponible
    cuda_works = test_cuda_if_available()
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"PaddleOCR direct: {'✅ Fonctionne' if paddleocr_works else '❌ Ne fonctionne pas'}")
    print(f"Détecteur restauré: {'✅ Fonctionne' if detector_works else '❌ Ne fonctionne pas'}")
    print(f"CUDA disponible: {'✅ Fonctionne' if cuda_works else '❌ Non disponible'}")
    
    if paddleocr_works and detector_works:
        print("\n🎉 SUCCÈS ! Votre détecteur est restauré et fonctionne !")
        print("💡 Vous pouvez maintenant utiliser votre application normalement.")
        if cuda_works:
            print("🚀 BONUS: CUDA est également disponible pour plus de vitesse !")
    elif paddleocr_works:
        print("\n⚠️ PaddleOCR fonctionne mais problème avec le détecteur")
        print("💡 Vérifiez l'intégration dans detector.py")
    else:
        print("\n❌ Problème avec PaddleOCR")
        print("💡 Vérifiez l'installation: pip install paddleocr")

if __name__ == "__main__":
    main()
