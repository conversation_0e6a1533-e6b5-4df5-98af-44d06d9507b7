#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la logique all-in simplifiée :
- Rouge dans jetons = indicateur all-in
- Montant dans mise = montant all-in (qui est sa mise)
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight

def test_allin_simplifie():
    """Test de la logique all-in simplifiée"""
    print("🧪 TEST DE LA LOGIQUE ALL-IN SIMPLIFIÉE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Situation réelle :
    # - Rouge dans jetons = all-in
    # - Montant dans mise = montant all-in (= sa mise)
    simulated_results = {
        # Cartes
        "carte_1m": {"text": "As", "colors": ["red"]},
        "carte_2m": {"text": "Roi", "colors": ["black"]},
        
        # Mes jetons
        "mes_jetons": {"text": "1000", "colors": []},
        
        # JOUEUR 1 : Mise normale (pas de rouge dans jetons)
        "jetons_joueur1": {"text": "800", "colors": ["white"]},  # Jetons normaux
        "mise_joueur1": {"text": "50", "colors": ["white"]},    # Mise normale 50 BB
        
        # JOUEUR 2 : Mise normale (pas de rouge dans jetons)
        "jetons_joueur2": {"text": "1200", "colors": ["white"]}, # Jetons normaux
        "mise_joueur2": {"text": "100", "colors": ["white"]},    # Mise normale 100 BB
        
        # JOUEUR 3 : ALL-IN (rouge dans jetons)
        "jetons_joueur3": {"text": "all in", "colors": ["red"]}, # ALL-IN (rouge)
        "mise_joueur3": {"text": "500", "colors": ["white"]},    # Sa mise = 500 BB (= montant all-in)
        
        # JOUEUR 4 : ALL-IN (rouge dans jetons)
        "jetons_joueur4": {"text": "", "colors": ["red"]},       # ALL-IN (rouge, pas de texte)
        "mise_joueur4": {"text": "300", "colors": ["white"]},    # Sa mise = 300 BB (= montant all-in)
        
        # Pot total
        "pot_total": {"text": "950", "colors": ["white"]},
    }
    
    print("📊 SITUATION SIMULÉE:")
    print("   💰 Mes jetons: 1000 BB")
    print("   👥 J1: Jetons normaux + mise 50 BB")
    print("   👥 J2: Jetons normaux + mise 100 BB")
    print("   🔥 J3: ROUGE dans jetons + mise 500 BB (= ALL-IN 500 BB)")
    print("   🔥 J4: ROUGE dans jetons + mise 300 BB (= ALL-IN 300 BB)")
    print("   🎯 Pot total: 950 BB")
    
    # Traiter les résultats
    print(f"\n🔍 TRAITEMENT DES RÉSULTATS...")
    data = advisor.extract_poker_data(simulated_results)
    
    print(f"\n📈 RÉSULTATS DE L'EXTRACTION:")
    print(f"   💰 Mes jetons: {data['my_stack']} BB")
    print(f"   🎯 Pot total: {data['pot_total']} BB")
    
    print(f"\n👥 JETONS DES ADVERSAIRES:")
    for player, amount in data["player_stacks"].items():
        print(f"   - {player}: {amount} BB")
    
    print(f"\n🔥 INDICATEURS ALL-IN (rouge dans jetons):")
    if data["allin_indicators"]:
        for player, is_allin in data["allin_indicators"].items():
            if is_allin:
                print(f"   - {player}: ALL-IN détecté (rouge dans jetons)")
    else:
        print("   Aucun indicateur all-in détecté")
    
    print(f"\n💲 TOUTES LES MISES (normales + all-in):")
    for player, amount in data["player_bets"].items():
        is_allin = player in data["player_allins"]
        type_mise = "ALL-IN" if is_allin else "Normale"
        print(f"   - {player}: {amount} BB ({type_mise})")
    
    print(f"\n🔥 ALL-IN CONFIRMÉS:")
    if data["player_allins"]:
        for player, amount in data["player_allins"].items():
            print(f"   - {player}: {amount} BB (montant = mise)")
    else:
        print("   Aucun all-in confirmé")
    
    # Analyser la situation complète
    print(f"\n🎯 ANALYSE COMPLÈTE:")
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    # Vérifications
    print(f"\n✅ VÉRIFICATIONS:")
    
    # Vérifier que J1 et J2 ne sont pas all-in
    j1_allin = "joueur1" in data["player_allins"]
    j2_allin = "joueur2" in data["player_allins"]
    print(f"   J1 all-in: {'❌ OUI' if j1_allin else '✅ NON'}")
    print(f"   J2 all-in: {'❌ OUI' if j2_allin else '✅ NON'}")
    
    # Vérifier que J3 et J4 sont all-in
    j3_allin = "joueur3" in data["player_allins"]
    j4_allin = "joueur4" in data["player_allins"]
    print(f"   J3 all-in: {'✅ OUI' if j3_allin else '❌ NON'}")
    print(f"   J4 all-in: {'✅ OUI' if j4_allin else '❌ NON'}")
    
    # Vérifier que les montants all-in = montants mises
    if j3_allin:
        j3_allin_amount = data["player_allins"]["joueur3"]
        j3_bet_amount = data["player_bets"]["joueur3"]
        print(f"   J3 all-in = mise: {'✅ OUI' if j3_allin_amount == j3_bet_amount else '❌ NON'} ({j3_allin_amount} = {j3_bet_amount})")
    
    if j4_allin:
        j4_allin_amount = data["player_allins"]["joueur4"]
        j4_bet_amount = data["player_bets"]["joueur4"]
        print(f"   J4 all-in = mise: {'✅ OUI' if j4_allin_amount == j4_bet_amount else '❌ NON'} ({j4_allin_amount} = {j4_bet_amount})")
    
    # Afficher le conseiller formaté
    print(f"\n📋 AFFICHAGE DU CONSEILLER:")
    print("=" * 60)
    print(formatted_analysis)
    
    return True

def test_affichage_separe():
    """Test que l'affichage sépare bien mises normales et all-in"""
    print("\n🧪 TEST DE L'AFFICHAGE SÉPARÉ")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Situation avec mélange mises normales et all-in
    results = {
        "jetons_joueur1": {"text": "500", "colors": ["white"]},  # Normal
        "mise_joueur1": {"text": "50", "colors": ["white"]},    # Mise normale
        
        "jetons_joueur2": {"text": "", "colors": ["red"]},       # ALL-IN
        "mise_joueur2": {"text": "200", "colors": ["white"]},    # Mise = all-in
        
        "jetons_joueur3": {"text": "800", "colors": ["white"]},  # Normal
        "mise_joueur3": {"text": "75", "colors": ["white"]},    # Mise normale
        
        "jetons_joueur4": {"text": "all in", "colors": ["red"]}, # ALL-IN
        "mise_joueur4": {"text": "150", "colors": ["white"]},    # Mise = all-in
    }
    
    data = advisor.extract_poker_data(results)
    
    print("📊 RÉSULTATS ATTENDUS:")
    print("   🟢 Mises normales: J1: 50, J3: 75")
    print("   🔥 All-in: J2: 200, J4: 150")
    
    # Construire l'affichage comme dans le conseiller
    bets_info = []
    allins_info = []
    
    for player, bet_value in data["player_bets"].items():
        player_short = f"J{player[-1:]}"
        if player in data["player_allins"]:
            allins_info.append(f"{player_short}: {bet_value:.0f}")
        else:
            bets_info.append(f"{player_short}: {bet_value:.0f}")
    
    print(f"\n📋 AFFICHAGE RÉEL:")
    print(f"   🟢 Mises: {', '.join(bets_info) if bets_info else 'Aucune'}")
    print(f"   🔥 All-in: {', '.join(allins_info) if allins_info else 'Aucun'}")
    
    # Vérifications
    expected_bets = ["J1: 50", "J3: 75"]
    expected_allins = ["J2: 200", "J4: 150"]
    
    bets_ok = all(bet in bets_info for bet in expected_bets)
    allins_ok = all(allin in allins_info for allin in expected_allins)
    
    print(f"\n✅ VÉRIFICATIONS:")
    print(f"   Mises normales: {'✅ OK' if bets_ok else '❌ ERREUR'}")
    print(f"   All-in: {'✅ OK' if allins_ok else '❌ ERREUR'}")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS ALL-IN SIMPLIFIÉS")
    print("=" * 60)
    
    # Test principal
    test_allin_simplifie()
    
    # Test affichage
    test_affichage_separe()
    
    print("\n✅ TESTS TERMINÉS")
