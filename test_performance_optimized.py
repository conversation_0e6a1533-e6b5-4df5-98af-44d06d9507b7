#!/usr/bin/env python3
"""
Test de performance rapide avec toutes les optimisations activées
"""

import time
import cv2
import numpy as np
import os
from detector import Detector

def create_test_image():
    """Crée une image de test simple avec des cartes"""
    img = np.ones((1080, 1920, 3), dtype=np.uint8) * 50
    
    # Positions des cartes
    card_positions = [
        (794, 581, 118, 89),   # card_1
        (937, 579, 132, 95),   # card_2
        (1097, 579, 129, 95),  # card_3
        (1251, 579, 132, 95),  # card_4
        (1405, 579, 134, 99),  # card_5
    ]
    
    # Dessiner des cartes simulées
    card_values = ['A', 'K', 'Q', 'J', '10']
    for i, (x, y, w, h) in enumerate(card_positions):
        # Fond blanc
        cv2.rectangle(img, (x, y), (x+w, y+h), (255, 255, 255), -1)
        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 0), 2)
        
        # Texte
        value = card_values[i]
        font = cv2.FONT_HERSHEY_SIMPLEX
        text_size = cv2.getTextSize(value, font, 2.0, 3)[0]
        text_x = x + (w - text_size[0]) // 2
        text_y = y + (h + text_size[1]) // 2
        cv2.putText(img, value, (text_x, text_y), font, 2.0, (0, 0, 0), 3)
    
    return img

def test_performance():
    """Test de performance rapide"""
    print("🚀 Test de performance avec optimisations")
    print("=" * 50)
    
    # Créer l'image de test
    test_image = create_test_image()
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return
    
    print("✅ Configuration chargée")
    
    # Test avec CUDA activé
    print("\n🔥 Test avec CUDA activé...")
    detector_cuda = Detector(config_path, use_cuda=True)
    
    # Test mode rapide
    print("⚡ Mode rapide...")
    start_time = time.time()
    
    if hasattr(detector_cuda, 'process_image_direct'):
        results = detector_cuda.process_image_direct(test_image, fast_mode=True)
    else:
        # Fallback vers la méthode standard
        temp_path = "temp_test.jpg"
        cv2.imwrite(temp_path, test_image)
        results = detector_cuda.process_image(temp_path)
        if os.path.exists(temp_path):
            os.remove(temp_path)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Compter les détections réussies
    card_detections = sum(1 for name, result in results.items() 
                         if name.startswith('card_') and result.get('text', '').strip())
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   Temps de traitement: {duration:.3f}s")
    print(f"   Cartes détectées: {card_detections}/5")
    print(f"   Vitesse: {len(results)/duration:.1f} régions/seconde")
    
    # Afficher les cartes détectées
    print(f"\n🃏 Cartes détectées:")
    for name, result in results.items():
        if name.startswith('card_') and result.get('text', '').strip():
            text = result.get('text', '')
            colors = result.get('colors', [])
            print(f"   {name}: {text} ({', '.join(colors)})")
    
    # Comparaison avec l'ancien temps
    old_time = 17.0  # Temps moyen précédent
    improvement = (old_time - duration) / old_time * 100
    
    print(f"\n🎯 AMÉLIORATION:")
    if improvement > 0:
        print(f"   Gain de performance: +{improvement:.1f}%")
        print(f"   Temps économisé: {old_time - duration:.3f}s")
    else:
        print(f"   Performance similaire (différence: {improvement:.1f}%)")
    
    return duration, card_detections

if __name__ == "__main__":
    test_performance()
    input("\nAppuyez sur Entrée pour fermer...")
