#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la nouvelle fonctionnalité "Pas de cartes" dans la correction des cartes
===============================================================================

Ce script teste la nouvelle option "Pas de cartes" ajoutée à l'interface
de correction des cartes.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def test_correction_pas_de_cartes():
    """Test de la fonctionnalité 'Pas de cartes'"""
    
    print("🧪 Test de la correction 'Pas de cartes'")
    print("=" * 50)
    
    # Créer une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection avec des fausses cartes
    fake_results = {
        "card_1": {"text": "A", "colors": ["black", "white"]},
        "card_2": {"text": "K", "colors": ["red", "white"]},
        "card_3": {"text": "8", "colors": ["black", "white"]},  # Fausse détection
        "card_4": {"text": "", "colors": []},  # Pas de carte
        "card_5": {"text": "", "colors": []},  # Pas de carte
        "carte_1m": {"text": "Q", "colors": ["red", "white"]},
        "carte_2m": {"text": "J", "colors": ["black", "white"]},
    }
    
    print("📊 Résultats de détection simulés:")
    for region, data in fake_results.items():
        text = data.get("text", "")
        colors = data.get("colors", [])
        print(f"  {region}: '{text}' {colors}")
    
    print("\n🔍 Analyse AVANT correction:")
    analysis, formatted = advisor.analyze_detection_results(fake_results)
    print(f"  Cartes board: {analysis.get('board_cards_text', 'N/A')}")
    print(f"  Cartes main: {analysis.get('hand_cards_text', 'N/A')}")
    
    # Test 1: Corriger card_3 en "Pas de cartes"
    print("\n🛠️ Test 1: Correction de card_3 en 'Pas de cartes'")
    success = advisor.set_manual_correction("card_3", "", "")
    print(f"  Correction appliquée: {'✅' if success else '❌'}")
    
    # Analyser après correction
    analysis, formatted = advisor.analyze_detection_results(fake_results)
    print(f"  Cartes board après correction: {analysis.get('board_cards_text', 'N/A')}")
    
    # Test 2: Corriger card_4 avec une vraie carte
    print("\n🛠️ Test 2: Correction de card_4 avec une vraie carte (10 de Trèfle)")
    success = advisor.set_manual_correction("card_4", "10", "Trèfle")
    print(f"  Correction appliquée: {'✅' if success else '❌'}")
    
    # Analyser après correction
    analysis, formatted = advisor.analyze_detection_results(fake_results)
    print(f"  Cartes board après correction: {analysis.get('board_cards_text', 'N/A')}")
    
    # Test 3: Corriger carte_1m en "Pas de cartes"
    print("\n🛠️ Test 3: Correction de carte_1m en 'Pas de cartes'")
    success = advisor.set_manual_correction("carte_1m", "", "")
    print(f"  Correction appliquée: {'✅' if success else '❌'}")
    
    # Analyser après correction
    analysis, formatted = advisor.analyze_detection_results(fake_results)
    print(f"  Cartes main après correction: {analysis.get('hand_cards_text', 'N/A')}")
    
    # Test 4: Vérifier les corrections actives
    print("\n📋 Corrections manuelles actives:")
    corrections = advisor.get_manual_corrections()
    if corrections:
        for region, correction in corrections.items():
            value = correction.get("value", "")
            suit = correction.get("suit", "")
            if value:
                print(f"  {region}: {value} de {suit}")
            else:
                print(f"  {region}: Pas de cartes")
    else:
        print("  Aucune correction active")
    
    # Test 5: Réinitialiser toutes les corrections
    print("\n🔄 Test 5: Réinitialisation de toutes les corrections")
    count = advisor.clear_manual_corrections()
    print(f"  Corrections effacées: {count}")
    
    # Analyser après réinitialisation
    analysis, formatted = advisor.analyze_detection_results(fake_results)
    print(f"  Cartes board après réinitialisation: {analysis.get('board_cards_text', 'N/A')}")
    print(f"  Cartes main après réinitialisation: {analysis.get('hand_cards_text', 'N/A')}")
    
    print("\n✅ Test terminé avec succès!")
    print("\n📝 Résumé des fonctionnalités testées:")
    print("  ✅ Correction 'Pas de cartes' (valeur vide)")
    print("  ✅ Correction avec vraie carte")
    print("  ✅ Gestion des corrections multiples")
    print("  ✅ Réinitialisation des corrections")
    print("  ✅ Intégration avec l'analyse poker")

if __name__ == "__main__":
    test_correction_pas_de_cartes()
