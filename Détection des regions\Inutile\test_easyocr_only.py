#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test minimal d'EasyOCR pour identifier le problème
"""

import cv2
import numpy as np
import time

def test_minimal():
    """Test minimal d'EasyOCR"""
    print("🔍 Test minimal EasyOCR...")
    
    try:
        print("🔄 Import EasyOCR...")
        import easyocr
        print("✅ EasyOCR importé")
        
        print("🔄 Création image test...")
        # Image très simple
        img = np.ones((100, 200, 3), dtype=np.uint8) * 255
        cv2.putText(img, "A", (50, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        print("✅ Image créée")
        
        print("🔄 Initialisation EasyOCR (CPU)...")
        reader = easyocr.Reader(['en'], gpu=False, verbose=True)
        print("✅ EasyOCR initialisé")
        
        print("🔄 Détection...")
        results = reader.readtext(img)
        print(f"✅ Résultats: {results}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔥 TEST MINIMAL EASYOCR")
    print("=" * 30)
    
    success = test_minimal()
    
    if success:
        print("\n✅ EasyOCR fonctionne")
    else:
        print("\n❌ Problème avec EasyOCR")
        print("💡 Essayez: pip uninstall easyocr && pip install easyocr")
