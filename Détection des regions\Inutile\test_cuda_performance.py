#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de performance CUDA pour l'application poker
"""

import os
import sys
import time
import numpy as np
import cv2

def force_cuda_environment():
    """Force l'environnement CUDA"""
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    os.environ['PADDLE_USE_GPU'] = '1'
    os.environ['USE_CUDA'] = '1'
    print("🔥 Variables d'environnement CUDA configurées")

def test_cuda_availability():
    """Test la disponibilité de CUDA"""
    print("🔍 Test de disponibilité CUDA...")
    print("=" * 50)

    # Test PyTorch CUDA
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        print(f"✅ PyTorch CUDA disponible: {cuda_available}")
        if cuda_available:
            print(f"✅ Device: {torch.cuda.get_device_name(0)}")
            print(f"✅ VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
            # Nettoyer le cache
            torch.cuda.empty_cache()
    except Exception as e:
        print(f"❌ Erreur PyTorch: {e}")

    # Test PaddlePaddle CUDA
    try:
        import paddle
        paddle_cuda = paddle.device.is_compiled_with_cuda()
        print(f"✅ PaddlePaddle CUDA disponible: {paddle_cuda}")
        if paddle_cuda:
            print(f"✅ CUDA devices: {paddle.device.cuda.device_count()}")
            paddle.device.set_device('gpu:0')
            print("✅ GPU configuré pour PaddlePaddle")
    except Exception as e:
        print(f"❌ Erreur PaddlePaddle: {e}")

def test_paddleocr_simple():
    """Test simple de PaddleOCR avec CUDA"""
    print("\n🚀 Test simple PaddleOCR CUDA...")
    print("=" * 50)

    try:
        # Forcer l'environnement CUDA
        force_cuda_environment()

        # Créer une image de test simple
        test_image = np.ones((100, 200, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (50, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)

        print("🔄 Initialisation PaddleOCR avec CUDA...")
        start_time = time.time()

        # Importer et initialiser PaddleOCR avec paramètres optimisés
        from paddleocr import PaddleOCR

        ocr = PaddleOCR(
            use_gpu=True,
            show_log=False,
            lang='en',
            use_angle_cls=False,  # Désactiver pour plus de vitesse
            gpu_mem=1500,  # Limiter la mémoire GPU
            enable_mkldnn=False,
            use_mp=False,
            use_tensorrt=False
        )

        init_time = time.time() - start_time
        print(f"✅ Initialisation: {init_time:.2f}s")

        # Test de détection
        print("🔄 Test de détection...")
        start_time = time.time()
        result = ocr.ocr(test_image, cls=False)
        detection_time = time.time() - start_time
        print(f"✅ Détection: {detection_time:.2f}s")

        if result and result[0]:
            for line in result[0]:
                print(f"✅ Texte détecté: {line[1][0]} (confiance: {line[1][1]:.2f})")
        else:
            print("⚠️ Aucun texte détecté")

        # Nettoyer
        del ocr

    except Exception as e:
        print(f"❌ Erreur PaddleOCR: {e}")
        import traceback
        traceback.print_exc()

def test_detector_cuda():
    """Test du détecteur principal avec CUDA"""
    print("\n🎯 Test du détecteur principal avec CUDA...")
    print("=" * 50)

    try:
        # Forcer l'environnement CUDA
        force_cuda_environment()

        # Importer le détecteur
        sys.path.append('.')
        from detector import Detector

        print("🔄 Initialisation du détecteur avec CUDA forcé...")
        start_time = time.time()

        # Créer le détecteur avec CUDA forcé
        detector = Detector(use_cuda=True)

        init_time = time.time() - start_time
        print(f"✅ Initialisation détecteur: {init_time:.2f}s")

        # Créer une image de test
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "K", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 0, 0), 4)
        cv2.putText(test_image, "♠", (100, 180), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)

        # Test de détection rapide
        print("🔄 Test de détection rapide...")
        start_time = time.time()

        text_result = detector.detect_text_fast(test_image)

        detection_time = time.time() - start_time
        print(f"✅ Détection rapide: {detection_time:.2f}s")
        print(f"✅ Résultat texte: '{text_result}'")

        # Test de détection de couleur
        print("🔄 Test de détection de couleur...")
        start_time = time.time()

        color_result = detector.detect_colors_fast(test_image)

        color_time = time.time() - start_time
        print(f"✅ Détection couleur: {color_time:.2f}s")
        print(f"✅ Résultat couleur: {color_result}")

        # Test de traitement d'image complet
        print("🔄 Test de traitement d'image complet...")
        start_time = time.time()

        full_results = detector.process_image_direct(test_image, fast_mode=True)

        full_time = time.time() - start_time
        print(f"✅ Traitement complet: {full_time:.2f}s")
        print(f"✅ Résultats: {len(full_results)} régions traitées")

        # Nettoyer la mémoire GPU
        if hasattr(detector, '_cleanup_gpu_memory'):
            detector._cleanup_gpu_memory()
            print("✅ Mémoire GPU nettoyée")

    except Exception as e:
        print(f"❌ Erreur détecteur: {e}")
        import traceback
        traceback.print_exc()

def benchmark_performance():
    """Benchmark de performance avec plusieurs tests"""
    print("\n⚡ Benchmark de performance...")
    print("=" * 50)

    try:
        force_cuda_environment()

        # Créer plusieurs images de test
        test_images = []
        for i in range(5):
            img = np.ones((150, 200, 3), dtype=np.uint8) * 255
            cards = ['A', 'K', 'Q', 'J', '10']
            cv2.putText(img, cards[i], (70, 90), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
            test_images.append(img)

        from detector import Detector
        detector = Detector(use_cuda=True)

        print("🔄 Benchmark sur 5 images...")
        total_start = time.time()

        for i, img in enumerate(test_images):
            start = time.time()
            result = detector.detect_text_fast(img)
            elapsed = time.time() - start
            print(f"  Image {i+1}: {elapsed:.3f}s -> '{result}'")

        total_time = time.time() - total_start
        avg_time = total_time / len(test_images)

        print(f"✅ Temps total: {total_time:.2f}s")
        print(f"✅ Temps moyen par image: {avg_time:.3f}s")
        print(f"✅ Images par seconde: {1/avg_time:.1f}")

    except Exception as e:
        print(f"❌ Erreur benchmark: {e}")

if __name__ == "__main__":
    print("🔥 TEST DE PERFORMANCE CUDA POUR POKER ADVISOR")
    print("=" * 60)

    test_cuda_availability()
    test_paddleocr_simple()
    test_detector_cuda()
    benchmark_performance()

    print("\n✅ Tests terminés!")
    print("💡 Si les performances sont lentes, vérifiez que CUDA est bien utilisé dans les logs.")
