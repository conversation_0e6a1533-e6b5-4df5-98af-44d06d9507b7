#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la détection des mises et all-in des adversaires
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight

def test_mises_et_allin():
    """Test de la détection des mises et all-in"""
    print("🧪 TEST DE LA DÉTECTION DES MISES ET ALL-IN")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection avec mises et all-in
    simulated_results = {
        # Cartes
        "carte_1m": {"text": "As", "colors": ["red"]},
        "carte_2m": {"text": "Roi", "colors": ["black"]},
        
        # Jetons
        "mes_jetons": {"text": "1000", "colors": []},
        "jetons_joueur1": {"text": "800", "colors": []},
        "jetons_joueur2": {"text": "1200", "colors": []},
        "jetons_joueur3": {"text": "500", "colors": []},
        
        # Mises normales
        "mise_joueur1": {"text": "50", "colors": ["white"]},  # Mise normale
        "mise_joueur2": {"text": "100", "colors": ["white"]}, # Mise normale
        
        # All-in (montants rouges)
        "mise_joueur3": {"text": "500", "colors": ["red"]},   # ALL-IN (rouge)
        "mise_joueur4": {"text": "300", "colors": ["red"]},   # ALL-IN (rouge)
        
        # Pot total
        "pot_total": {"text": "950", "colors": ["white"]},
    }
    
    print("📊 DONNÉES SIMULÉES:")
    print("   💰 Mes jetons: 1000 BB")
    print("   👥 Jetons adversaires: J1=800, J2=1200, J3=500")
    print("   💲 Mises normales: J1=50, J2=100")
    print("   🔥 All-in (rouge): J3=500, J4=300")
    print("   🎯 Pot total: 950 BB")
    
    # Traiter les résultats
    print(f"\n🔍 TRAITEMENT DES RÉSULTATS...")
    data = advisor.extract_poker_data(simulated_results)
    
    print(f"\n📈 RÉSULTATS DE L'EXTRACTION:")
    print(f"   💰 Mes jetons: {data['my_stack']} BB")
    print(f"   🎯 Pot total: {data['pot_total']} BB")
    
    print(f"\n👥 JETONS DES ADVERSAIRES:")
    for player, amount in data["player_stacks"].items():
        print(f"   - {player}: {amount} BB")
    
    print(f"\n💲 MISES DES ADVERSAIRES:")
    for player, amount in data["player_bets"].items():
        print(f"   - {player}: {amount} BB")
    
    print(f"\n🔥 ALL-IN DÉTECTÉS:")
    if data["player_allins"]:
        for player, amount in data["player_allins"].items():
            print(f"   - {player}: {amount} BB (couleur rouge)")
    else:
        print("   Aucun all-in détecté")
    
    # Analyser la situation complète
    print(f"\n🎯 ANALYSE COMPLÈTE:")
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"\n📊 STATISTIQUES:")
    print(f"   🔍 Régions détectées: {len(data['detected_regions'])}")
    print(f"   💰 Tapis effectif: {data['effective_stack']} BB")
    
    # Calculer les statistiques des mises
    if data["player_bets"]:
        total_bets = sum(data["player_bets"].values())
        avg_bet = total_bets / len(data["player_bets"])
        print(f"   💲 Total des mises: {total_bets} BB")
        print(f"   📊 Mise moyenne: {avg_bet:.1f} BB")
    
    if data["player_allins"]:
        allin_count = len(data["player_allins"])
        total_allin = sum(data["player_allins"].values())
        print(f"   🔥 Nombre d'all-in: {allin_count}")
        print(f"   🔥 Total all-in: {total_allin} BB")
    
    # Afficher le conseiller formaté
    print(f"\n📋 AFFICHAGE DU CONSEILLER:")
    print("=" * 60)
    print(formatted_analysis)
    
    return True

def test_detection_couleurs():
    """Test spécifique de la détection des couleurs pour all-in"""
    print("\n🎨 TEST DE LA DÉTECTION DES COULEURS")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Test avec différentes couleurs
    test_cases = [
        {"text": "100", "colors": ["white"], "expected": "Mise normale"},
        {"text": "200", "colors": ["red"], "expected": "ALL-IN"},
        {"text": "150", "colors": ["red", "white"], "expected": "ALL-IN"},
        {"text": "50", "colors": [], "expected": "Mise normale"},
        {"text": "300", "colors": ["black"], "expected": "Mise normale"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['text']} BB - Couleurs: {test_case['colors']}")
        
        # Simuler une mise
        results = {
            "mise_joueur1": {
                "text": test_case["text"], 
                "colors": test_case["colors"]
            }
        }
        
        data = advisor.extract_poker_data(results)
        
        # Vérifier le résultat
        is_allin = "joueur1" in data["player_allins"]
        result_type = "ALL-IN" if is_allin else "Mise normale"
        
        status = "✅" if result_type == test_case["expected"] else "❌"
        print(f"   {status} Résultat: {result_type} (attendu: {test_case['expected']})")
        
        if is_allin:
            print(f"   🔥 Montant all-in: {data['player_allins']['joueur1']} BB")
        else:
            print(f"   💲 Montant mise: {data['player_bets'].get('joueur1', 0)} BB")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS MISES ET ALL-IN")
    print("=" * 60)
    
    # Test 1: Mises et all-in
    test_mises_et_allin()
    
    # Test 2: Détection couleurs
    test_detection_couleurs()
    
    print("\n✅ TESTS TERMINÉS")
