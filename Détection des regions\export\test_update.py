#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script pour simuler des mises à jour du fichier realtime_results.
Utile pour tester l'application Conseiller Poker.
"""

import os
import time
import random
from datetime import datetime

# Chemin vers le fichier à mettre à jour
FILE_PATH = r"C:\Users\<USER>\PokerAdvisor\Détection des regions\export\realtime_results"

# Données pour la simulation
CARDS = ["As", "Roi", "Dame", "Valet", "10", "9", "8", "7", "6", "5", "4", "3", "2"]
SUITS = ["Cœur", "Pi<PERSON>", "Trèfle", "Carreau"]
ACTIONS = ["<PERSON>lancer", "Suivre", "Se coucher", "All-in", "Checker"]

def generate_random_hand():
    """Génère une main aléatoire."""
    hand = []
    for _ in range(2):
        card = random.choice(CARDS)
        suit = random.choice(SUITS)
        hand.append(f"{card} de {suit}")
    return ", ".join(hand)

def generate_random_board(num_cards=3):
    """Génère un board aléatoire avec un nombre spécifié de cartes."""
    board = []
    for _ in range(num_cards):
        card = random.choice(CARDS)
        suit = random.choice(SUITS)
        board.append(f"{card} de {suit}")
    return ", ".join(board)

def generate_random_probability():
    """Génère une probabilité aléatoire de gagner."""
    return round(random.uniform(0, 100), 1)

def generate_random_action():
    """Génère une action aléatoire recommandée."""
    return random.choice(ACTIONS)

def update_file():
    """Met à jour le fichier avec des données aléatoires."""
    hand = generate_random_hand()
    board_cards = random.randint(3, 5)
    board = generate_random_board(board_cards)
    probability = generate_random_probability()
    action = generate_random_action()
    current_time = datetime.now().strftime("%H:%M:%S")
    
    content = f"""Cartes en main: {hand}
Cartes sur le board: {board}
Probabilité de gagner: {probability}%
Action recommandée: {action}

Dernière mise à jour: {current_time}"""
    
    with open(FILE_PATH, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Fichier mis à jour à {current_time}")

def main():
    """Fonction principale."""
    print("Démarrage de la simulation de mises à jour...")
    print(f"Le fichier sera mis à jour toutes les 3 secondes à {FILE_PATH}")
    print("Appuyez sur Ctrl+C pour arrêter")
    
    try:
        while True:
            update_file()
            time.sleep(3)
    except KeyboardInterrupt:
        print("\nSimulation arrêtée")

if __name__ == "__main__":
    main()
