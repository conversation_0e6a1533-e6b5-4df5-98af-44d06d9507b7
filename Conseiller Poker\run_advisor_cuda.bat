@echo off
echo ===================================================
echo Lancement de Poker-Coach Pro v5 avec CUDA
echo ===================================================
echo.

echo Verification de CUDA...
python -c "import torch; print(f'CUDA disponible: {torch.cuda.is_available()}')"
if %ERRORLEVEL% NEQ 0 (
    echo PyTorch n'est pas installe ou CUDA n'est pas disponible.
    echo Veuillez executer install_paddle_cuda.bat pour installer les dependances.
    pause
    exit /b 1
)

echo.
echo Verification de PaddleOCR avec CUDA...
python -c "import paddle; print(f'PaddlePaddle compile avec CUDA: {paddle.device.is_compiled_with_cuda()}')"
if %ERRORLEVEL% NEQ 0 (
    echo PaddlePaddle n'est pas installe.
    echo Veuillez executer install_paddle_cuda.bat pour installer les dependances.
    pause
    exit /b 1
)

echo.
echo Lancement de Poker-Coach Pro v5 avec CUDA active...
echo.

rem Définir la variable d'environnement pour activer CUDA
set USE_CUDA=1

rem Lancer l'application principale avec CUDA activé
cd /d "%~dp0"
python poker_advisor_app.py --use-cuda

echo.
pause
