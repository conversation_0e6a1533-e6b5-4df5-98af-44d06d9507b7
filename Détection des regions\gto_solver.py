#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Solver GTO (Game Theory Optimal) pour poker
"""

import numpy as np
from scipy.optimize import minimize
import itertools
from collections import defaultdict

class GTOSolver:
    """Solver GTO pour situations de poker"""
    
    def __init__(self):
        self.positions = ["UTG", "MP", "CO", "BTN", "SB", "BB"]
        self.actions = ["fold", "call", "raise_small", "raise_medium", "raise_large", "all_in"]
        
        # Tailles de mise standards
        self.bet_sizes = {
            "raise_small": 2.5,
            "raise_medium": 3.5,
            "raise_large": 5.0,
            "all_in": "stack"
        }
        
        # Fréquences GTO de base par position (preflop)
        self.gto_frequencies = {
            "UTG": {"vpip": 0.12, "pfr": 0.10, "3bet": 0.04},
            "MP": {"vpip": 0.15, "pfr": 0.12, "3bet": 0.05},
            "CO": {"vpip": 0.25, "pfr": 0.20, "3bet": 0.07},
            "BTN": {"vpip": 0.45, "pfr": 0.35, "3bet": 0.10},
            "SB": {"vpip": 0.35, "pfr": 0.15, "3bet": 0.08},
            "BB": {"vpip": 0.30, "pfr": 0.08, "3bet": 0.12}
        }
    
    def solve_situation(self, situation):
        """
        Résout une situation de poker selon la théorie des jeux
        
        Args:
            situation (dict): Description de la situation
                - position: Position du joueur
                - stack_size: Taille du stack en BB
                - pot_size: Taille du pot en BB
                - action_to_call: Montant à suivre
                - opponents: Liste des adversaires actifs
                - board: Cartes du board
                - hand_range: Range de mains du joueur
                
        Returns:
            dict: Solution GTO avec fréquences d'actions
        """
        if not self._validate_situation(situation):
            return {"error": "Situation invalide"}
        
        # Analyser le contexte
        context = self._analyze_context(situation)
        
        # Calculer les fréquences optimales
        optimal_frequencies = self._calculate_optimal_frequencies(situation, context)
        
        # Déterminer les tailles de mise optimales
        optimal_bet_sizes = self._calculate_optimal_bet_sizes(situation, context)
        
        # Calculer l'EV de chaque action
        action_evs = self._calculate_action_evs(situation, optimal_frequencies)
        
        # Générer la stratégie mixte
        mixed_strategy = self._generate_mixed_strategy(optimal_frequencies, action_evs)
        
        return {
            "optimal_frequencies": optimal_frequencies,
            "optimal_bet_sizes": optimal_bet_sizes,
            "action_evs": action_evs,
            "mixed_strategy": mixed_strategy,
            "context_analysis": context,
            "exploitability": self._calculate_exploitability(mixed_strategy),
            "recommendations": self._generate_recommendations(mixed_strategy, situation)
        }
    
    def _validate_situation(self, situation):
        """Valide les paramètres de la situation"""
        required_fields = ["position", "stack_size", "pot_size"]
        return all(field in situation for field in required_fields)
    
    def _analyze_context(self, situation):
        """Analyse le contexte de la situation"""
        context = {
            "street": self._determine_street(situation.get("board", [])),
            "stack_to_pot_ratio": situation["stack_size"] / situation["pot_size"],
            "position_type": self._categorize_position(situation["position"]),
            "action_pressure": self._calculate_action_pressure(situation),
            "pot_odds": self._calculate_pot_odds(situation)
        }
        
        # Analyser la texture du board
        if situation.get("board"):
            context["board_texture"] = self._analyze_board_texture(situation["board"])
        
        return context
    
    def _determine_street(self, board):
        """Détermine la street actuelle"""
        if not board:
            return "preflop"
        elif len(board) == 3:
            return "flop"
        elif len(board) == 4:
            return "turn"
        elif len(board) == 5:
            return "river"
        else:
            return "unknown"
    
    def _categorize_position(self, position):
        """Catégorise la position"""
        early_positions = ["UTG", "UTG+1", "MP"]
        middle_positions = ["MP+1", "CO"]
        late_positions = ["BTN"]
        blinds = ["SB", "BB"]
        
        if position in early_positions:
            return "early"
        elif position in middle_positions:
            return "middle"
        elif position in late_positions:
            return "late"
        elif position in blinds:
            return "blind"
        else:
            return "unknown"
    
    def _calculate_action_pressure(self, situation):
        """Calcule la pression d'action"""
        opponents = situation.get("opponents", [])
        action_to_call = situation.get("action_to_call", 0)
        
        pressure = 0
        
        # Pression du montant à suivre
        if action_to_call > 0:
            pressure += min(action_to_call / situation["stack_size"] * 100, 50)
        
        # Pression du nombre d'adversaires
        pressure += len(opponents) * 5
        
        # Pression de la position
        position_pressure = {
            "early": 20,
            "middle": 10,
            "late": 0,
            "blind": 15
        }
        pressure += position_pressure.get(self._categorize_position(situation["position"]), 10)
        
        return min(pressure, 100)
    
    def _calculate_pot_odds(self, situation):
        """Calcule les pot odds"""
        action_to_call = situation.get("action_to_call", 0)
        if action_to_call == 0:
            return 0
        
        return action_to_call / (situation["pot_size"] + action_to_call) * 100
    
    def _analyze_board_texture(self, board):
        """Analyse la texture du board"""
        if len(board) < 3:
            return {"type": "preflop"}
        
        # Analyser les couleurs
        suits = [card[1] for card in board]
        suit_counts = {suit: suits.count(suit) for suit in set(suits)}
        max_suit_count = max(suit_counts.values()) if suit_counts else 0
        
        # Analyser les rangs
        ranks = [card[0] for card in board]
        rank_values = {'2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, 'T': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14}
        numeric_ranks = sorted([rank_values.get(rank, 0) for rank in ranks])
        
        # Déterminer le type de board
        board_type = "rainbow"
        if max_suit_count >= 3:
            board_type = "flush_draw" if max_suit_count == 3 else "flush_possible"
        
        # Vérifier les tirages de quinte
        straight_draw = False
        if len(numeric_ranks) >= 3:
            for i in range(len(numeric_ranks) - 2):
                if numeric_ranks[i+2] - numeric_ranks[i] <= 4:
                    straight_draw = True
                    break
        
        # Déterminer la connectivité
        connectivity = "low"
        if any(rank in ranks for rank in ['J', 'Q', 'K', 'A']):
            connectivity = "high"
        elif any(rank in ranks for rank in ['8', '9', 'T']):
            connectivity = "medium"
        
        return {
            "type": board_type,
            "straight_draw": straight_draw,
            "connectivity": connectivity,
            "max_suit_count": max_suit_count,
            "paired": len(set(ranks)) < len(ranks)
        }
    
    def _calculate_optimal_frequencies(self, situation, context):
        """Calcule les fréquences optimales d'actions"""
        base_frequencies = self.gto_frequencies.get(situation["position"], self.gto_frequencies["MP"])
        
        # Ajuster selon le contexte
        frequencies = base_frequencies.copy()
        
        # Ajustements selon la street
        if context["street"] == "preflop":
            # Fréquences preflop de base
            pass
        else:
            # Ajustements postflop
            frequencies = self._adjust_postflop_frequencies(frequencies, context, situation)
        
        # Ajustements selon la pression d'action
        if context["action_pressure"] > 50:
            frequencies["fold"] = frequencies.get("fold", 0.3) + 0.2
            frequencies["call"] = frequencies.get("call", 0.4) - 0.1
            frequencies["raise"] = frequencies.get("raise", 0.3) - 0.1
        
        # Normaliser les fréquences
        return self._normalize_frequencies(frequencies)
    
    def _adjust_postflop_frequencies(self, frequencies, context, situation):
        """Ajuste les fréquences pour le postflop"""
        adjusted = {
            "fold": 0.3,
            "call": 0.4,
            "bet_small": 0.15,
            "bet_medium": 0.10,
            "bet_large": 0.05
        }
        
        # Ajuster selon la texture du board
        if context.get("board_texture"):
            texture = context["board_texture"]
            
            if texture["type"] == "flush_possible":
                adjusted["bet_large"] += 0.1
                adjusted["call"] -= 0.05
                adjusted["fold"] -= 0.05
            
            if texture["straight_draw"]:
                adjusted["bet_medium"] += 0.1
                adjusted["call"] -= 0.05
                adjusted["fold"] -= 0.05
        
        return adjusted
    
    def _normalize_frequencies(self, frequencies):
        """Normalise les fréquences pour qu'elles totalisent 1.0"""
        total = sum(frequencies.values())
        if total == 0:
            return frequencies
        
        return {action: freq / total for action, freq in frequencies.items()}
    
    def _calculate_optimal_bet_sizes(self, situation, context):
        """Calcule les tailles de mise optimales"""
        pot_size = situation["pot_size"]
        stack_size = situation["stack_size"]
        
        # Tailles de base en fonction du pot
        sizes = {
            "small": pot_size * 0.33,
            "medium": pot_size * 0.66,
            "large": pot_size * 1.0,
            "overbet": pot_size * 1.5
        }
        
        # Ajuster selon le stack-to-pot ratio
        spr = context["stack_to_pot_ratio"]
        
        if spr < 2:
            # Stack court - privilégier all-in
            sizes["all_in"] = stack_size
        elif spr > 10:
            # Stack profond - plus de sizing options
            sizes["xl"] = pot_size * 2.0
        
        # Limiter par la taille du stack
        for size_name, size_value in sizes.items():
            if size_value > stack_size:
                sizes[size_name] = stack_size
        
        return sizes
    
    def _calculate_action_evs(self, situation, frequencies):
        """Calcule l'EV de chaque action"""
        # Simplification - calculs EV basiques
        pot_size = situation["pot_size"]
        stack_size = situation["stack_size"]
        action_to_call = situation.get("action_to_call", 0)
        
        evs = {}
        
        # EV du fold
        evs["fold"] = 0
        
        # EV du call
        if action_to_call > 0:
            pot_odds = action_to_call / (pot_size + action_to_call)
            estimated_equity = 0.4  # Estimation simplifiée
            evs["call"] = estimated_equity * (pot_size + action_to_call) - action_to_call
        else:
            evs["call"] = 0
        
        # EV des raises
        for action in ["raise_small", "raise_medium", "raise_large"]:
            if action in self.bet_sizes:
                bet_size = self.bet_sizes[action]
                if isinstance(bet_size, (int, float)):
                    fold_equity = 0.3  # Estimation simplifiée
                    win_equity = 0.4   # Estimation simplifiée
                    evs[action] = fold_equity * pot_size + win_equity * (pot_size + bet_size * 2) - bet_size
        
        return evs
    
    def _generate_mixed_strategy(self, frequencies, action_evs):
        """Génère une stratégie mixte optimale"""
        strategy = {}
        
        # Combiner fréquences et EVs pour la stratégie finale
        for action in frequencies:
            if action in action_evs:
                # Pondérer par l'EV
                ev_weight = max(0, action_evs[action]) / max(1, max(action_evs.values()))
                strategy[action] = frequencies[action] * (0.7 + 0.3 * ev_weight)
            else:
                strategy[action] = frequencies[action]
        
        # Renormaliser
        return self._normalize_frequencies(strategy)
    
    def _calculate_exploitability(self, strategy):
        """Calcule l'exploitabilité de la stratégie"""
        # Mesure simplifiée de l'exploitabilité
        entropy = -sum(freq * np.log(freq + 1e-10) for freq in strategy.values() if freq > 0)
        max_entropy = np.log(len(strategy))
        
        return {
            "entropy": entropy,
            "normalized_entropy": entropy / max_entropy if max_entropy > 0 else 0,
            "exploitability_score": max(0, 1 - entropy / max_entropy) * 100
        }
    
    def _generate_recommendations(self, strategy, situation):
        """Génère des recommandations basées sur la stratégie"""
        recommendations = []
        
        # Trier les actions par fréquence
        sorted_actions = sorted(strategy.items(), key=lambda x: x[1], reverse=True)
        
        # Recommandation principale
        main_action = sorted_actions[0]
        recommendations.append({
            "type": "primary",
            "action": main_action[0],
            "frequency": main_action[1],
            "description": f"Action principale: {main_action[0]} ({main_action[1]*100:.1f}%)"
        })
        
        # Actions alternatives
        for action, freq in sorted_actions[1:3]:
            if freq > 0.1:  # Seulement si fréquence > 10%
                recommendations.append({
                    "type": "alternative",
                    "action": action,
                    "frequency": freq,
                    "description": f"Alternative: {action} ({freq*100:.1f}%)"
                })
        
        return recommendations

if __name__ == "__main__":
    # Test du solver GTO
    solver = GTOSolver()
    
    print("🧪 Test du solver GTO")
    print("=" * 50)
    
    # Situation test
    situation = {
        "position": "BTN",
        "stack_size": 100,
        "pot_size": 15,
        "action_to_call": 5,
        "opponents": ["BB"],
        "board": ["Ah", "5h", "2c"]
    }
    
    result = solver.solve_situation(situation)
    
    if "error" not in result:
        print("Stratégie optimale:")
        for action, freq in result["mixed_strategy"].items():
            print(f"  {action}: {freq*100:.1f}%")
        
        print(f"\nExploitabilité: {result['exploitability']['exploitability_score']:.1f}%")
        
        print("\nRecommandations:")
        for rec in result["recommendations"]:
            print(f"  {rec['description']}")
    else:
        print(f"Erreur: {result['error']}")
