#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test spécifique pour DetectorGUI.
"""

import sys
import os

def test_detector_gui_creation():
    """Test de création de DetectorGUI"""
    print("🔍 Test de création de DetectorGUI...")
    
    try:
        # Importer les modules nécessaires
        from PyQt5.QtWidgets import QApplication
        from detector_gui import DetectorGUI
        
        print("✅ Imports réussis")
        
        # Créer l'application Qt
        app = QApplication(sys.argv)
        print("✅ QApplication créée")
        
        # Créer l'instance DetectorGUI
        print("🔧 Création de DetectorGUI...")
        window = DetectorGUI()
        print("✅ DetectorGUI créée avec succès")
        
        # Tester l'affichage
        print("🖼️ Test d'affichage...")
        window.show()
        print("✅ Fenêtre affichée")
        
        # Fermer immédiatement
        window.close()
        app.quit()
        print("✅ Application fermée proprement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de DetectorGUI: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detector_gui_with_env_vars():
    """Test avec variables d'environnement"""
    print("\n🔍 Test avec variables d'environnement...")
    
    try:
        # Définir les variables d'environnement
        os.environ['USE_CUDA'] = '1'
        os.environ['USE_POKER_ADVISOR'] = '1'
        os.environ['ENABLE_MONITORING'] = '1'
        print("✅ Variables d'environnement définies")
        
        # Importer les modules nécessaires
        from PyQt5.QtWidgets import QApplication
        from detector_gui import DetectorGUI
        
        print("✅ Imports réussis")
        
        # Créer l'application Qt
        app = QApplication(sys.argv)
        print("✅ QApplication créée")
        
        # Créer l'instance DetectorGUI
        print("🔧 Création de DetectorGUI avec toutes les options...")
        window = DetectorGUI()
        print("✅ DetectorGUI créée avec succès")
        
        # Tester l'affichage
        print("🖼️ Test d'affichage...")
        window.show()
        print("✅ Fenêtre affichée")
        
        # Fermer immédiatement
        window.close()
        app.quit()
        print("✅ Application fermée proprement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur avec variables d'environnement: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_by_step():
    """Test étape par étape de l'initialisation"""
    print("\n🔍 Test étape par étape...")
    
    try:
        print("1. Import PyQt5...")
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 importé")
        
        print("2. Création QApplication...")
        app = QApplication(sys.argv)
        print("✅ QApplication créée")
        
        print("3. Import detector_gui...")
        import detector_gui
        print("✅ detector_gui importé")
        
        print("4. Accès à la classe DetectorGUI...")
        DetectorGUI = detector_gui.DetectorGUI
        print("✅ Classe DetectorGUI accessible")
        
        print("5. Création de l'instance...")
        window = DetectorGUI()
        print("✅ Instance créée")
        
        print("6. Test show()...")
        window.show()
        print("✅ show() réussi")
        
        print("7. Fermeture...")
        window.close()
        app.quit()
        print("✅ Fermeture réussie")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur à l'étape: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TESTS SPÉCIFIQUES POUR DETECTOR_GUI")
    print("=" * 60)
    
    # Test 1: Création basique
    if not test_detector_gui_creation():
        print("\n❌ ÉCHEC: Problème de création basique")
        return False
    
    # Test 2: Avec variables d'environnement
    if not test_detector_gui_with_env_vars():
        print("\n❌ ÉCHEC: Problème avec variables d'environnement")
        return False
    
    # Test 3: Étape par étape
    if not test_step_by_step():
        print("\n❌ ÉCHEC: Problème dans l'initialisation")
        return False
    
    print("\n✅ TOUS LES TESTS DETECTOR_GUI SONT RÉUSSIS!")
    print("L'application devrait fonctionner normalement.")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 ERREUR CRITIQUE: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 Tests DetectorGUI terminés")
