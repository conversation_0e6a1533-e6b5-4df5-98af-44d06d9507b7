#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'installation d'EasyOCR pour Poker Advisor
==================================================

Ce script installe EasyOCR et ses dépendances pour le module multi-OCR.
Il vérifie également la compatibilité avec CUDA pour l'accélération GPU.

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import sys
import subprocess
import platform
import time

def print_header(message):
    """Affiche un message d'en-tête formaté"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80 + "\n")

def print_step(message):
    """Affiche un message d'étape formaté"""
    print(f"\n>> {message}")

def run_command(command, description=None):
    """Exécute une commande et affiche le résultat"""
    if description:
        print_step(description)
    
    print(f"Exécution de: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Commande exécutée avec succès")
        if result.stdout.strip():
            print("\nSortie standard:")
            print(result.stdout.strip())
    else:
        print("❌ Erreur lors de l'exécution de la commande")
        if result.stderr.strip():
            print("\nErreur standard:")
            print(result.stderr.strip())
    
    return result.returncode == 0, result.stdout

def check_python_version():
    """Vérifie la version de Python"""
    print_step("Vérification de la version de Python")
    
    version = platform.python_version()
    print(f"Version de Python: {version}")
    
    major, minor, _ = map(int, version.split('.'))
    if major == 3 and minor >= 8:
        print("✅ Version de Python compatible")
        return True
    else:
        print("⚠️ Version de Python non optimale. Python 3.8 ou supérieur est recommandé.")
        return False

def check_cuda():
    """Vérifie si CUDA est disponible"""
    print_step("Vérification de CUDA")
    
    try:
        # Vérifier si torch est installé
        import_success, _ = run_command(
            f"{sys.executable} -c \"import torch; print('CUDA disponible:', torch.cuda.is_available())\"",
            "Vérification de PyTorch et CUDA"
        )
        
        if not import_success:
            print("PyTorch n'est pas installé. Installation de PyTorch avec CUDA...")
            return False
        
        # Vérifier si CUDA est disponible
        success, output = run_command(
            f"{sys.executable} -c \"import torch; print(torch.cuda.is_available()); print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'Pas de GPU')\"",
            "Vérification de la disponibilité de CUDA"
        )
        
        if "True" in output:
            print("✅ CUDA est disponible")
            
            # Afficher les informations sur le GPU
            run_command(
                f"{sys.executable} -c \"import torch; print('GPU:', torch.cuda.get_device_name(0)); print('Mémoire totale:', torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024, 'Go')\"",
                "Informations sur le GPU"
            )
            
            return True
        else:
            print("⚠️ CUDA n'est pas disponible")
            return False
    
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de CUDA: {e}")
        return False

def install_pytorch_with_cuda():
    """Installe PyTorch avec support CUDA"""
    print_step("Installation de PyTorch avec support CUDA")
    
    # Commande d'installation de PyTorch avec CUDA 11.8
    command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    
    success, _ = run_command(command, "Installation de PyTorch avec CUDA 11.8")
    return success

def install_easyocr():
    """Installe EasyOCR et ses dépendances"""
    print_step("Installation d'EasyOCR")
    
    # Installer les dépendances d'EasyOCR
    dependencies = [
        "numpy",
        "opencv-python",
        "Pillow",
        "scikit-image",
        "python-bidi",
        "PyYAML",
        "Shapely",
        "pyclipper",
        "ninja"
    ]
    
    for dep in dependencies:
        run_command(f"{sys.executable} -m pip install {dep}", f"Installation de {dep}")
    
    # Installer EasyOCR
    success, _ = run_command(f"{sys.executable} -m pip install easyocr", "Installation d'EasyOCR")
    return success

def test_easyocr():
    """Teste l'installation d'EasyOCR"""
    print_step("Test d'EasyOCR")
    
    test_code = """
import easyocr
import time
import cv2
import numpy as np

# Créer une image de test avec un J
img = np.ones((100, 100, 3), dtype=np.uint8) * 255
cv2.putText(img, "J", (30, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)

# Initialiser EasyOCR
print("Initialisation d'EasyOCR...")
start_time = time.time()
reader = easyocr.Reader(['en'], gpu=True)
init_time = time.time() - start_time
print(f"Temps d'initialisation: {init_time:.2f} secondes")

# Détecter le texte
print("Détection du texte...")
start_time = time.time()
result = reader.readtext(img)
detect_time = time.time() - start_time
print(f"Temps de détection: {detect_time:.2f} secondes")

# Afficher le résultat
print("Résultat:", result)
print("Test terminé avec succès!")
"""
    
    # Écrire le code de test dans un fichier temporaire
    with open("test_easyocr.py", "w") as f:
        f.write(test_code)
    
    # Exécuter le test
    success, _ = run_command(f"{sys.executable} test_easyocr.py", "Exécution du test EasyOCR")
    
    # Supprimer le fichier temporaire
    try:
        os.remove("test_easyocr.py")
    except:
        pass
    
    return success

def main():
    """Fonction principale"""
    print_header("Installation d'EasyOCR pour Poker Advisor")
    
    # Vérifier la version de Python
    check_python_version()
    
    # Vérifier si CUDA est disponible
    cuda_available = check_cuda()
    
    # Si CUDA n'est pas disponible, installer PyTorch avec CUDA
    if not cuda_available:
        print_step("Installation de PyTorch avec support CUDA")
        install_pytorch_with_cuda()
        
        # Vérifier à nouveau si CUDA est disponible
        cuda_available = check_cuda()
    
    # Installer EasyOCR
    install_success = install_easyocr()
    
    if install_success:
        print("\n✅ EasyOCR a été installé avec succès!")
        
        # Tester EasyOCR
        print_step("Test de l'installation")
        test_success = test_easyocr()
        
        if test_success:
            print("\n✅ EasyOCR fonctionne correctement!")
        else:
            print("\n⚠️ Le test d'EasyOCR a échoué. Veuillez vérifier l'installation.")
    else:
        print("\n❌ L'installation d'EasyOCR a échoué.")
    
    print_header("Installation terminée")
    print("Vous pouvez maintenant utiliser le module multi-OCR pour la détection des cartes.")
    print("Pour cela, assurez-vous que le fichier multi_ocr_detector.py est présent dans le même dossier que detector.py.")
    
    # Attendre avant de fermer
    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
