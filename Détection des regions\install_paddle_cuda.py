#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'installation de PaddlePaddle avec support CUDA pour Poker Advisor
=========================================================================

Ce script désinstalle la version actuelle de PaddlePaddle et installe
une version compatible avec CUDA pour améliorer les performances.

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import sys
import subprocess
import platform
import time

def print_header(message):
    """Affiche un message d'en-tête formaté"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80 + "\n")

def print_step(message):
    """Affiche un message d'étape formaté"""
    print(f"\n>> {message}")

def run_command(command, description=None):
    """Exécute une commande et affiche le résultat"""
    if description:
        print_step(description)
    
    print(f"Exécution de: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Commande exécutée avec succès")
        if result.stdout.strip():
            print("\nSortie standard:")
            print(result.stdout.strip())
    else:
        print("❌ Erreur lors de l'exécution de la commande")
        if result.stderr.strip():
            print("\nErreur standard:")
            print(result.stderr.strip())
    
    return result.returncode == 0, result.stdout

def check_cuda():
    """Vérifie si CUDA est disponible"""
    print_step("Vérification de CUDA")
    
    try:
        # Vérifier si torch est installé
        import_success, _ = run_command(
            f"{sys.executable} -c \"import torch; print('CUDA disponible:', torch.cuda.is_available())\"",
            "Vérification de PyTorch et CUDA"
        )
        
        if not import_success:
            print("PyTorch n'est pas installé. Installation de PyTorch avec CUDA...")
            return False
        
        # Vérifier si CUDA est disponible
        success, output = run_command(
            f"{sys.executable} -c \"import torch; print(torch.cuda.is_available()); print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'Pas de GPU')\"",
            "Vérification de la disponibilité de CUDA"
        )
        
        if "True" in output:
            print("✅ CUDA est disponible")
            
            # Afficher les informations sur le GPU
            run_command(
                f"{sys.executable} -c \"import torch; print('GPU:', torch.cuda.get_device_name(0)); print('Mémoire totale:', torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024, 'Go')\"",
                "Informations sur le GPU"
            )
            
            return True
        else:
            print("⚠️ CUDA n'est pas disponible")
            return False
    
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de CUDA: {e}")
        return False

def check_paddle():
    """Vérifie si PaddlePaddle est installé et s'il est compilé avec CUDA"""
    print_step("Vérification de PaddlePaddle")
    
    try:
        # Vérifier si paddle est installé
        import_success, _ = run_command(
            f"{sys.executable} -c \"import paddle; print('PaddlePaddle installé')\"",
            "Vérification de PaddlePaddle"
        )
        
        if not import_success:
            print("PaddlePaddle n'est pas installé.")
            return False, False
        
        # Vérifier si PaddlePaddle est compilé avec CUDA
        success, output = run_command(
            f"{sys.executable} -c \"import paddle; print('Compilé avec CUDA:', paddle.device.is_compiled_with_cuda()); print('Devices disponibles:', paddle.device.get_available_device())\"",
            "Vérification du support CUDA dans PaddlePaddle"
        )
        
        if "Compilé avec CUDA: True" in output:
            print("✅ PaddlePaddle est compilé avec CUDA")
            return True, True
        else:
            print("⚠️ PaddlePaddle est installé mais sans support CUDA")
            return True, False
    
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de PaddlePaddle: {e}")
        return False, False

def uninstall_paddle():
    """Désinstalle PaddlePaddle"""
    print_step("Désinstallation de PaddlePaddle")
    
    success, _ = run_command(
        f"{sys.executable} -m pip uninstall -y paddlepaddle paddlepaddle-gpu paddleocr",
        "Désinstallation de PaddlePaddle et PaddleOCR"
    )
    
    return success

def install_paddle_with_cuda():
    """Installe PaddlePaddle avec support CUDA"""
    print_step("Installation de PaddlePaddle avec support CUDA")
    
    # Déterminer la version de CUDA
    success, output = run_command(
        f"{sys.executable} -c \"import torch; print(torch.version.cuda)\"",
        "Détection de la version de CUDA"
    )
    
    cuda_version = None
    if success:
        output = output.strip()
        if "11.8" in output:
            cuda_version = "11.8"
        elif "11.7" in output:
            cuda_version = "11.7"
        elif "11.6" in output:
            cuda_version = "11.6"
        elif "11.2" in output:
            cuda_version = "11.2"
        elif "10.2" in output:
            cuda_version = "10.2"
        elif "10.1" in output:
            cuda_version = "10.1"
        else:
            # Extraire la version majeure et mineure
            parts = output.split('.')
            if len(parts) >= 2:
                major, minor = parts[0], parts[1]
                if int(major) == 11:
                    cuda_version = "11.2"  # Utiliser la version 11.2 pour CUDA 11.x
                elif int(major) == 10:
                    cuda_version = "10.2"  # Utiliser la version 10.2 pour CUDA 10.x
    
    if not cuda_version:
        print("⚠️ Version de CUDA non détectée, utilisation de CUDA 11.2 par défaut")
        cuda_version = "11.2"
    
    print(f"Installation de PaddlePaddle pour CUDA {cuda_version}")
    
    # Installer PaddlePaddle avec CUDA
    if cuda_version == "11.8" or cuda_version == "11.7" or cuda_version == "11.6":
        command = f"{sys.executable} -m pip install paddlepaddle-gpu==2.5.2.post118 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html"
    elif cuda_version == "11.2":
        command = f"{sys.executable} -m pip install paddlepaddle-gpu==2.5.2.post112 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html"
    elif cuda_version == "10.2":
        command = f"{sys.executable} -m pip install paddlepaddle-gpu==2.5.2.post102 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html"
    elif cuda_version == "10.1":
        command = f"{sys.executable} -m pip install paddlepaddle-gpu==2.5.2.post101 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html"
    else:
        command = f"{sys.executable} -m pip install paddlepaddle-gpu==2.5.2 -f https://www.paddlepaddle.org.cn/whl/windows/mkl/avx/stable.html"
    
    success, _ = run_command(command, "Installation de PaddlePaddle avec CUDA")
    
    # Installer PaddleOCR
    if success:
        run_command(
            f"{sys.executable} -m pip install paddleocr",
            "Installation de PaddleOCR"
        )
    
    return success

def test_paddle_cuda():
    """Teste si PaddlePaddle fonctionne avec CUDA"""
    print_step("Test de PaddlePaddle avec CUDA")
    
    test_code = """
import paddle
import time

# Vérifier si PaddlePaddle est compilé avec CUDA
print("PaddlePaddle compilé avec CUDA:", paddle.device.is_compiled_with_cuda())
print("Devices disponibles:", paddle.device.get_available_device())

if paddle.device.is_compiled_with_cuda():
    # Définir le device sur GPU
    paddle.device.set_device('gpu:0')
    print("Device actuel:", paddle.device.get_device())
    
    # Créer un tenseur sur GPU
    start_time = time.time()
    x = paddle.ones([1000, 1000], dtype='float32')
    y = paddle.matmul(x, x)
    paddle.device.synchronize()
    gpu_time = time.time() - start_time
    print(f"Temps d'exécution sur GPU: {gpu_time:.4f} secondes")
    
    # Créer un tenseur sur CPU pour comparaison
    paddle.device.set_device('cpu')
    start_time = time.time()
    x = paddle.ones([1000, 1000], dtype='float32')
    y = paddle.matmul(x, x)
    cpu_time = time.time() - start_time
    print(f"Temps d'exécution sur CPU: {cpu_time:.4f} secondes")
    
    if gpu_time < cpu_time:
        print(f"✅ GPU est {cpu_time/gpu_time:.2f}x plus rapide que CPU")
    else:
        print("⚠️ GPU n'est pas plus rapide que CPU, vérifiez la configuration")
else:
    print("❌ PaddlePaddle n'est pas compilé avec CUDA")

print("Test terminé avec succès!")
"""
    
    # Écrire le code de test dans un fichier temporaire
    with open("test_paddle_cuda.py", "w") as f:
        f.write(test_code)
    
    # Exécuter le test
    success, _ = run_command(f"{sys.executable} test_paddle_cuda.py", "Exécution du test PaddlePaddle avec CUDA")
    
    # Supprimer le fichier temporaire
    try:
        os.remove("test_paddle_cuda.py")
    except:
        pass
    
    return success

def main():
    """Fonction principale"""
    print_header("Installation de PaddlePaddle avec support CUDA pour Poker Advisor")
    
    # Vérifier si CUDA est disponible
    cuda_available = check_cuda()
    
    if not cuda_available:
        print("❌ CUDA n'est pas disponible sur ce système. Impossible d'installer PaddlePaddle avec CUDA.")
        print("Veuillez installer les pilotes NVIDIA et CUDA Toolkit.")
        return
    
    # Vérifier si PaddlePaddle est déjà installé avec CUDA
    paddle_installed, paddle_cuda = check_paddle()
    
    if paddle_installed and paddle_cuda:
        print("✅ PaddlePaddle est déjà installé avec support CUDA.")
        test_paddle_cuda()
        return
    
    # Désinstaller PaddlePaddle existant
    if paddle_installed:
        uninstall_paddle()
    
    # Installer PaddlePaddle avec CUDA
    install_success = install_paddle_with_cuda()
    
    if install_success:
        print("\n✅ PaddlePaddle a été installé avec support CUDA!")
        
        # Tester PaddlePaddle avec CUDA
        print_step("Test de l'installation")
        test_success = test_paddle_cuda()
        
        if test_success:
            print("\n✅ PaddlePaddle fonctionne correctement avec CUDA!")
        else:
            print("\n⚠️ Le test de PaddlePaddle avec CUDA a échoué. Veuillez vérifier l'installation.")
    else:
        print("\n❌ L'installation de PaddlePaddle avec CUDA a échoué.")
    
    print_header("Installation terminée")
    print("Vous pouvez maintenant utiliser PaddlePaddle avec accélération GPU pour de meilleures performances.")
    
    # Attendre avant de fermer
    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
