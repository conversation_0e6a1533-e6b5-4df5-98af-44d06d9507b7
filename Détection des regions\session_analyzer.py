#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Analyseur post-session pour review automatique des erreurs
"""

import json
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics

class SessionAnalyzer:
    """Analyseur de sessions de poker pour review automatique"""
    
    def __init__(self):
        # Critères d'erreurs communes
        self.error_patterns = {
            "preflop_errors": {
                "too_loose_utg": {"vpip_threshold": 15, "severity": "medium"},
                "too_tight_btn": {"vpip_threshold": 35, "severity": "low"},
                "no_3bet": {"3bet_threshold": 3, "severity": "medium"},
                "over_3bet": {"3bet_threshold": 15, "severity": "high"}
            },
            "postflop_errors": {
                "cbet_too_high": {"cbet_threshold": 80, "severity": "medium"},
                "cbet_too_low": {"cbet_threshold": 50, "severity": "low"},
                "fold_to_cbet_high": {"fold_threshold": 70, "severity": "high"},
                "no_bluffs": {"bluff_threshold": 10, "severity": "medium"}
            },
            "sizing_errors": {
                "bet_too_small": {"size_threshold": 0.3, "severity": "medium"},
                "bet_too_large": {"size_threshold": 1.5, "severity": "medium"},
                "inconsistent_sizing": {"variance_threshold": 0.5, "severity": "low"}
            },
            "emotional_errors": {
                "tilt_detected": {"loss_streak": 5, "severity": "high"},
                "overplay_weak": {"showdown_threshold": 30, "severity": "medium"},
                "underplay_strong": {"value_threshold": 70, "severity": "medium"}
            }
        }
        
        # Benchmarks par position
        self.position_benchmarks = {
            "UTG": {"vpip": 12, "pfr": 10, "3bet": 4, "fold_to_3bet": 65},
            "MP": {"vpip": 15, "pfr": 12, "3bet": 5, "fold_to_3bet": 60},
            "CO": {"vpip": 25, "pfr": 20, "3bet": 7, "fold_to_3bet": 55},
            "BTN": {"vpip": 45, "pfr": 35, "3bet": 10, "fold_to_3bet": 50},
            "SB": {"vpip": 35, "pfr": 15, "3bet": 8, "fold_to_3bet": 55},
            "BB": {"vpip": 30, "pfr": 8, "3bet": 12, "fold_to_3bet": 50}
        }
    
    def analyze_session(self, session_data):
        """
        Analyse complète d'une session de poker
        
        Args:
            session_data (dict): Données de la session
                - hands: Liste des mains jouées
                - duration: Durée de la session
                - stakes: Limites jouées
                - result: Résultat final
                
        Returns:
            dict: Analyse complète avec erreurs détectées
        """
        if not session_data or "hands" not in session_data:
            return {"error": "Données de session invalides"}
        
        hands = session_data["hands"]
        
        # Statistiques générales
        general_stats = self._calculate_general_stats(hands, session_data)
        
        # Analyse par position
        position_analysis = self._analyze_by_position(hands)
        
        # Détection d'erreurs
        detected_errors = self._detect_errors(hands, general_stats, position_analysis)
        
        # Analyse des tendances
        trend_analysis = self._analyze_trends(hands)
        
        # Recommandations d'amélioration
        recommendations = self._generate_recommendations(detected_errors, general_stats, position_analysis)
        
        # Score de performance
        performance_score = self._calculate_performance_score(general_stats, detected_errors)
        
        # Analyse émotionnelle/tilt
        tilt_analysis = self._analyze_tilt_patterns(hands)
        
        return {
            "session_summary": general_stats,
            "position_analysis": position_analysis,
            "detected_errors": detected_errors,
            "trend_analysis": trend_analysis,
            "recommendations": recommendations,
            "performance_score": performance_score,
            "tilt_analysis": tilt_analysis,
            "improvement_areas": self._identify_improvement_areas(detected_errors),
            "session_rating": self._rate_session(performance_score, detected_errors)
        }
    
    def _calculate_general_stats(self, hands, session_data):
        """Calcule les statistiques générales de la session"""
        total_hands = len(hands)
        if total_hands == 0:
            return {}
        
        # Statistiques de base
        vpip_hands = sum(1 for hand in hands if hand.get("vpip", False))
        pfr_hands = sum(1 for hand in hands if hand.get("pfr", False))
        three_bet_hands = sum(1 for hand in hands if hand.get("3bet", False))
        
        # Résultats
        total_result = sum(hand.get("result", 0) for hand in hands)
        winning_hands = sum(1 for hand in hands if hand.get("result", 0) > 0)
        
        # Showdowns
        showdown_hands = [hand for hand in hands if hand.get("went_to_showdown", False)]
        won_at_showdown = sum(1 for hand in showdown_hands if hand.get("result", 0) > 0)
        
        return {
            "total_hands": total_hands,
            "vpip": (vpip_hands / total_hands) * 100,
            "pfr": (pfr_hands / total_hands) * 100,
            "3bet": (three_bet_hands / total_hands) * 100,
            "total_result": total_result,
            "bb_per_100": (total_result / total_hands) * 100,
            "win_rate": (winning_hands / total_hands) * 100,
            "showdown_stats": {
                "went_to_showdown": len(showdown_hands),
                "won_at_showdown": won_at_showdown,
                "showdown_winrate": (won_at_showdown / len(showdown_hands)) * 100 if showdown_hands else 0
            },
            "session_duration": session_data.get("duration", 0),
            "hands_per_hour": total_hands / (session_data.get("duration", 1) / 60) if session_data.get("duration") else 0
        }
    
    def _analyze_by_position(self, hands):
        """Analyse les statistiques par position"""
        position_stats = defaultdict(lambda: {
            "hands": 0, "vpip": 0, "pfr": 0, "3bet": 0, 
            "fold_to_3bet": 0, "cbet": 0, "fold_to_cbet": 0,
            "result": 0
        })
        
        for hand in hands:
            position = hand.get("position", "Unknown")
            stats = position_stats[position]
            
            stats["hands"] += 1
            if hand.get("vpip", False):
                stats["vpip"] += 1
            if hand.get("pfr", False):
                stats["pfr"] += 1
            if hand.get("3bet", False):
                stats["3bet"] += 1
            if hand.get("fold_to_3bet", False):
                stats["fold_to_3bet"] += 1
            if hand.get("cbet", False):
                stats["cbet"] += 1
            if hand.get("fold_to_cbet", False):
                stats["fold_to_cbet"] += 1
            
            stats["result"] += hand.get("result", 0)
        
        # Convertir en pourcentages
        for position, stats in position_stats.items():
            if stats["hands"] > 0:
                stats["vpip_pct"] = (stats["vpip"] / stats["hands"]) * 100
                stats["pfr_pct"] = (stats["pfr"] / stats["hands"]) * 100
                stats["3bet_pct"] = (stats["3bet"] / stats["hands"]) * 100
                stats["bb_per_100"] = (stats["result"] / stats["hands"]) * 100
        
        return dict(position_stats)
    
    def _detect_errors(self, hands, general_stats, position_analysis):
        """Détecte les erreurs communes"""
        errors = []
        
        # Erreurs preflop
        errors.extend(self._detect_preflop_errors(general_stats, position_analysis))
        
        # Erreurs postflop
        errors.extend(self._detect_postflop_errors(hands))
        
        # Erreurs de sizing
        errors.extend(self._detect_sizing_errors(hands))
        
        # Erreurs émotionnelles
        errors.extend(self._detect_emotional_errors(hands))
        
        return errors
    
    def _detect_preflop_errors(self, general_stats, position_analysis):
        """Détecte les erreurs preflop"""
        errors = []
        
        for position, stats in position_analysis.items():
            if position in self.position_benchmarks and stats["hands"] >= 10:
                benchmark = self.position_benchmarks[position]
                
                # VPIP trop élevé/bas
                vpip_diff = stats["vpip_pct"] - benchmark["vpip"]
                if abs(vpip_diff) > 10:
                    severity = "high" if abs(vpip_diff) > 20 else "medium"
                    errors.append({
                        "type": "preflop_vpip",
                        "position": position,
                        "severity": severity,
                        "description": f"VPIP {position}: {stats['vpip_pct']:.1f}% (optimal: ~{benchmark['vpip']}%)",
                        "recommendation": "Ajuster la sélection de mains" if vpip_diff > 0 else "Jouer plus de mains"
                    })
                
                # PFR trop élevé/bas
                pfr_diff = stats["pfr_pct"] - benchmark["pfr"]
                if abs(pfr_diff) > 8:
                    errors.append({
                        "type": "preflop_pfr",
                        "position": position,
                        "severity": "medium",
                        "description": f"PFR {position}: {stats['pfr_pct']:.1f}% (optimal: ~{benchmark['pfr']}%)",
                        "recommendation": "Ajuster l'agressivité preflop"
                    })
        
        return errors
    
    def _detect_postflop_errors(self, hands):
        """Détecte les erreurs postflop"""
        errors = []
        
        postflop_hands = [hand for hand in hands if hand.get("saw_flop", False)]
        if not postflop_hands:
            return errors
        
        # Analyse des cbets
        cbet_opportunities = [hand for hand in postflop_hands if hand.get("cbet_opportunity", False)]
        cbets_made = [hand for hand in cbet_opportunities if hand.get("cbet", False)]
        
        if cbet_opportunities:
            cbet_frequency = (len(cbets_made) / len(cbet_opportunities)) * 100
            
            if cbet_frequency > 80:
                errors.append({
                    "type": "cbet_too_high",
                    "severity": "medium",
                    "description": f"C-bet trop fréquent: {cbet_frequency:.1f}%",
                    "recommendation": "Réduire la fréquence de c-bet, surtout sur boards défavorables"
                })
            elif cbet_frequency < 50:
                errors.append({
                    "type": "cbet_too_low",
                    "severity": "low",
                    "description": f"C-bet pas assez fréquent: {cbet_frequency:.1f}%",
                    "recommendation": "Augmenter la fréquence de c-bet pour value et bluff"
                })
        
        return errors
    
    def _detect_sizing_errors(self, hands):
        """Détecte les erreurs de sizing"""
        errors = []
        
        bet_sizes = []
        for hand in hands:
            if hand.get("bet_sizes"):
                bet_sizes.extend(hand["bet_sizes"])
        
        if bet_sizes:
            avg_size = statistics.mean(bet_sizes)
            size_variance = statistics.variance(bet_sizes) if len(bet_sizes) > 1 else 0
            
            if avg_size < 0.4:
                errors.append({
                    "type": "bet_too_small",
                    "severity": "medium",
                    "description": f"Mises trop petites en moyenne: {avg_size:.1f}x pot",
                    "recommendation": "Augmenter les tailles de mise pour plus de value/protection"
                })
            elif avg_size > 1.2:
                errors.append({
                    "type": "bet_too_large",
                    "severity": "medium",
                    "description": f"Mises trop grosses en moyenne: {avg_size:.1f}x pot",
                    "recommendation": "Réduire les tailles de mise pour optimiser la value"
                })
            
            if size_variance > 0.3:
                errors.append({
                    "type": "inconsistent_sizing",
                    "severity": "low",
                    "description": f"Sizing incohérent (variance: {size_variance:.2f})",
                    "recommendation": "Standardiser les tailles de mise par situation"
                })
        
        return errors
    
    def _detect_emotional_errors(self, hands):
        """Détecte les erreurs émotionnelles/tilt"""
        errors = []
        
        # Détecter les streaks de pertes
        results = [hand.get("result", 0) for hand in hands]
        current_streak = 0
        max_loss_streak = 0
        
        for result in results:
            if result < 0:
                current_streak += 1
                max_loss_streak = max(max_loss_streak, current_streak)
            else:
                current_streak = 0
        
        if max_loss_streak >= 5:
            errors.append({
                "type": "potential_tilt",
                "severity": "high",
                "description": f"Streak de {max_loss_streak} mains perdantes détecté",
                "recommendation": "Prendre une pause, revoir les décisions pendant cette période"
            })
        
        return errors
    
    def _analyze_trends(self, hands):
        """Analyse les tendances au cours de la session"""
        if len(hands) < 20:
            return {"insufficient_data": True}
        
        # Diviser la session en segments
        segment_size = len(hands) // 4
        segments = []
        
        for i in range(4):
            start = i * segment_size
            end = start + segment_size if i < 3 else len(hands)
            segment_hands = hands[start:end]
            
            segment_result = sum(hand.get("result", 0) for hand in segment_hands)
            segment_vpip = sum(1 for hand in segment_hands if hand.get("vpip", False))
            
            segments.append({
                "segment": i + 1,
                "hands": len(segment_hands),
                "result": segment_result,
                "vpip": (segment_vpip / len(segment_hands)) * 100 if segment_hands else 0
            })
        
        # Analyser les tendances
        results_trend = [seg["result"] for seg in segments]
        vpip_trend = [seg["vpip"] for seg in segments]
        
        return {
            "segments": segments,
            "result_trend": "improving" if results_trend[-1] > results_trend[0] else "declining",
            "vpip_trend": "tightening" if vpip_trend[-1] < vpip_trend[0] else "loosening",
            "consistency": statistics.stdev(results_trend) if len(results_trend) > 1 else 0
        }
    
    def _generate_recommendations(self, errors, general_stats, position_analysis):
        """Génère des recommandations d'amélioration"""
        recommendations = []
        
        # Recommandations basées sur les erreurs
        error_types = Counter(error["type"] for error in errors)
        
        for error_type, count in error_types.most_common(3):
            if error_type.startswith("preflop"):
                recommendations.append({
                    "priority": "high",
                    "area": "Preflop",
                    "description": f"Travailler la sélection de mains preflop ({count} erreurs détectées)",
                    "action": "Réviser les ranges par position"
                })
            elif error_type.startswith("postflop"):
                recommendations.append({
                    "priority": "medium",
                    "area": "Postflop",
                    "description": f"Améliorer le jeu postflop ({count} erreurs détectées)",
                    "action": "Étudier les situations de c-bet et de bluff"
                })
        
        # Recommandations générales
        if general_stats.get("showdown_stats", {}).get("showdown_winrate", 0) < 45:
            recommendations.append({
                "priority": "high",
                "area": "Sélection de mains",
                "description": "Faible winrate au showdown",
                "action": "Resserrer la sélection de mains, éviter les mains marginales"
            })
        
        return recommendations
    
    def _calculate_performance_score(self, general_stats, errors):
        """Calcule un score de performance global"""
        base_score = 100
        
        # Pénalités pour les erreurs
        for error in errors:
            if error["severity"] == "high":
                base_score -= 15
            elif error["severity"] == "medium":
                base_score -= 10
            else:
                base_score -= 5
        
        # Bonus pour les bonnes performances
        if general_stats.get("bb_per_100", 0) > 5:
            base_score += 10
        
        if general_stats.get("showdown_stats", {}).get("showdown_winrate", 0) > 55:
            base_score += 5
        
        return max(0, min(100, base_score))
    
    def _analyze_tilt_patterns(self, hands):
        """Analyse les patterns de tilt"""
        tilt_indicators = []
        
        # Analyser les décisions après de grosses pertes
        for i, hand in enumerate(hands[1:], 1):
            prev_hand = hands[i-1]
            if prev_hand.get("result", 0) < -20:  # Grosse perte
                if hand.get("vpip", False) and hand.get("position") in ["UTG", "MP"]:
                    tilt_indicators.append({
                        "hand_number": i,
                        "type": "loose_after_loss",
                        "description": "Jeu loose après une grosse perte"
                    })
        
        return {
            "tilt_indicators": tilt_indicators,
            "tilt_risk": "high" if len(tilt_indicators) > 3 else "low"
        }
    
    def _identify_improvement_areas(self, errors):
        """Identifie les domaines d'amélioration prioritaires"""
        areas = defaultdict(int)
        
        for error in errors:
            if error["type"].startswith("preflop"):
                areas["Preflop"] += 1
            elif error["type"].startswith("postflop"):
                areas["Postflop"] += 1
            elif error["type"].startswith("sizing"):
                areas["Bet Sizing"] += 1
            elif error["type"].startswith("emotional"):
                areas["Mental Game"] += 1
        
        return sorted(areas.items(), key=lambda x: x[1], reverse=True)
    
    def _rate_session(self, performance_score, errors):
        """Évalue la qualité de la session"""
        high_severity_errors = sum(1 for error in errors if error["severity"] == "high")
        
        if performance_score >= 85 and high_severity_errors == 0:
            return {"rating": "Excellent", "color": "green"}
        elif performance_score >= 70 and high_severity_errors <= 1:
            return {"rating": "Bon", "color": "blue"}
        elif performance_score >= 55:
            return {"rating": "Moyen", "color": "orange"}
        else:
            return {"rating": "À améliorer", "color": "red"}

if __name__ == "__main__":
    # Test de l'analyseur de session
    analyzer = SessionAnalyzer()
    
    print("🧪 Test de l'analyseur de session")
    print("=" * 50)
    
    # Données de test
    test_hands = [
        {"position": "BTN", "vpip": True, "pfr": True, "result": 5},
        {"position": "UTG", "vpip": True, "pfr": False, "result": -2},
        {"position": "BB", "vpip": False, "pfr": False, "result": 0},
        # ... plus de mains
    ]
    
    session_data = {
        "hands": test_hands,
        "duration": 120,  # minutes
        "stakes": "NL10",
        "result": 15
    }
    
    result = analyzer.analyze_session(session_data)
    
    if "error" not in result:
        print(f"Score de performance: {result['performance_score']}/100")
        print(f"Évaluation: {result['session_rating']['rating']}")
        print(f"Erreurs détectées: {len(result['detected_errors'])}")
        print(f"Recommandations: {len(result['recommendations'])}")
    else:
        print(f"Erreur: {result['error']}")
