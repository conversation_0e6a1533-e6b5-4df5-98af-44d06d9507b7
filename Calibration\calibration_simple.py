#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Calibration simplifiée
=====================================

Version simplifiée du script de calibration pour résoudre les problèmes d'initialisation.

Auteur: Augment Agent
Date: 2023
"""

import os
import time
import json
import cv2
import numpy as np
import mss
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from PIL import Image, ImageTk

# Créer les dossiers nécessaires
os.makedirs('config', exist_ok=True)
os.makedirs('screenshots', exist_ok=True)

# Fichier de configuration par défaut
CONFIG_FILE = os.path.join('config', 'poker_advisor_config.json')

# ============================================================================
#                       GESTION DE LA CONFIGURATION
# ============================================================================

def load_config(config_file=CONFIG_FILE):
    """Charge la configuration depuis un fichier JSON"""
    try:
        if os.path.exists(config_file):
            # Créer une sauvegarde de la configuration existante
            backup_file = f"{config_file}.backup"
            try:
                import shutil
                shutil.copy2(config_file, backup_file)
                print(f"✅ Sauvegarde de la configuration créée: {backup_file}")
            except Exception as e:
                print(f"⚠️ Impossible de créer une sauvegarde: {e}")

            with open(config_file, 'r') as f:
                config = json.load(f)
            print(f"✅ Configuration chargée depuis {config_file}")

            # Vérifier si la configuration contient toutes les zones nécessaires
            default_config = create_default_config()
            config_updated = False

            # Supprimer les régions à remplacer
            regions_to_remove = ["cartes_communes", "cartes_main", "main_carte_1", "main_carte_2", "historique", "table_entiere", "pot"]
            for region in regions_to_remove:
                if region in config['roi']:
                    print(f"⚠️ Suppression de la région '{region}' pour la remplacer par des régions individuelles")
                    del config['roi'][region]
                    config_updated = True

            # Ajouter les zones manquantes tout en préservant les coordonnées existantes
            for zone, coords in default_config['roi'].items():
                if zone not in config['roi']:
                    print(f"⚠️ Zone manquante ajoutée: {zone}")
                    config['roi'][zone] = coords
                    config_updated = True

            # Si la configuration a été mise à jour, la sauvegarder
            if config_updated:
                print("⚠️ Configuration mise à jour avec de nouvelles zones")
                save_config(config)

            return config
        else:
            print(f"ℹ️ Le fichier de configuration {config_file} n'existe pas.")

            # Vérifier s'il existe une sauvegarde
            backup_file = f"{config_file}.backup"
            if os.path.exists(backup_file):
                try:
                    with open(backup_file, 'r') as f:
                        backup_config = json.load(f)
                    print(f"✅ Configuration restaurée depuis la sauvegarde: {backup_file}")

                    # Ajouter les zones manquantes à la configuration restaurée
                    default_config = create_default_config()

                    # Supprimer les régions à remplacer
                    regions_to_remove = ["cartes_communes", "cartes_main", "carte_1", "carte_2"]
                    for region in regions_to_remove:
                        if region in backup_config['roi']:
                            print(f"⚠️ Suppression de la région '{region}' pour la remplacer par des régions individuelles")
                            del backup_config['roi'][region]

                    for zone, coords in default_config['roi'].items():
                        if zone not in backup_config['roi']:
                            print(f"⚠️ Zone manquante ajoutée à la sauvegarde restaurée: {zone}")
                            backup_config['roi'][zone] = coords

                    # Sauvegarder la configuration restaurée et mise à jour
                    save_config(backup_config)
                    return backup_config
                except Exception as e:
                    print(f"❌ Erreur lors de la restauration de la sauvegarde: {e}")

            return create_default_config()
    except Exception as e:
        print(f"❌ Erreur lors du chargement de la configuration: {e}")
        return create_default_config()

def save_config(config, config_file=CONFIG_FILE):
    """Sauvegarde la configuration dans un fichier JSON"""
    try:
        # S'assurer que le répertoire existe
        config_dir = os.path.dirname(config_file)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir)
            print(f"✅ Répertoire créé: {config_dir}")

        # Afficher des informations de débogage
        print(f"📝 Sauvegarde de la configuration dans: {config_file}")
        print(f"📝 Chemin absolu: {os.path.abspath(config_file)}")

        # Vérifier que la configuration contient bien toutes les zones
        if 'roi' in config:
            # Afficher les zones disponibles
            zones = list(config['roi'].keys())
            print(f"📝 Zones disponibles: {zones}")

            # Vérifier toutes les paires de zones pour détecter les coordonnées identiques
            zones_modifiees = set()  # Pour suivre les zones déjà modifiées

            for i, zone1 in enumerate(zones):
                for zone2 in zones[i+1:]:
                    # Ignorer les comparaisons avec des zones déjà modifiées
                    if zone1 in zones_modifiees or zone2 in zones_modifiees:
                        continue

                    # Vérifier si les coordonnées sont identiques
                    if config['roi'][zone1] == config['roi'][zone2]:
                        print(f"⚠️ ATTENTION: Les coordonnées de {zone1} et {zone2} sont identiques!")

                        # Modifier légèrement les coordonnées de la deuxième zone pour les différencier
                        if config['roi'][zone2]['left'] == config['roi'][zone1]['left']:
                            config['roi'][zone2]['left'] += 1
                            zones_modifiees.add(zone2)
                            print(f"⚠️ Modification automatique des coordonnées de {zone2}: {config['roi'][zone2]}")

            # Afficher un résumé des modifications
            if zones_modifiees:
                print(f"⚠️ {len(zones_modifiees)} zones ont été modifiées pour éviter les doublons: {zones_modifiees}")
            else:
                print("✅ Toutes les zones ont des coordonnées uniques.")

        # S'assurer que les régions à remplacer sont également supprimées de "all_regions"
        regions_to_remove = ["cartes_communes", "cartes_main", "main_carte_1", "main_carte_2", "historique", "table_entiere", "pot"]
        if 'all_regions' in config:
            for region in regions_to_remove:
                if region in config['all_regions']:
                    print(f"⚠️ Suppression de la région '{region}' de 'all_regions'")
                    del config['all_regions'][region]

        # S'assurer que les nouvelles régions de cartes sont présentes dans "all_regions"
        if 'all_regions' in config:
            # Ajouter les cartes communes
            for i in range(1, 6):
                card_key = f"card_{i}"
                if card_key in config['roi'] and card_key not in config['all_regions']:
                    config['all_regions'][card_key] = {
                        "x": config['roi'][card_key]['left'],
                        "y": config['roi'][card_key]['top'],
                        "width": config['roi'][card_key]['width'],
                        "height": config['roi'][card_key]['height']
                    }
                    print(f"⚠️ Ajout de la région '{card_key}' à 'all_regions'")

            # Ajouter les cartes en main
            main_cards = ["carte_1m", "carte_2m"]
            for card_key in main_cards:
                if card_key in config['roi'] and card_key not in config['all_regions']:
                    config['all_regions'][card_key] = {
                        "x": config['roi'][card_key]['left'],
                        "y": config['roi'][card_key]['top'],
                        "width": config['roi'][card_key]['width'],
                        "height": config['roi'][card_key]['height']
                    }
                    print(f"⚠️ Ajout de la région '{card_key}' à 'all_regions'")

        # Sauvegarder la configuration
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=4)

        print(f"✅ Configuration sauvegardée avec succès dans {config_file}")
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde de la configuration: {e}")
        print(f"❌ Type d'erreur: {type(e).__name__}")
        print(f"❌ Détails: {str(e)}")
        return False

def create_default_config():
    """Crée une configuration par défaut"""
    # Configuration par défaut pour une résolution 1920x1080
    default_config = {
        "roi": {
            # Cartes communes individuelles
            "card_1": {
                "left": 700,
                "top": 400,
                "width": 100,
                "height": 120
            },
            "card_2": {
                "left": 805,
                "top": 400,
                "width": 100,
                "height": 120
            },
            "card_3": {
                "left": 910,
                "top": 400,
                "width": 100,
                "height": 120
            },
            "card_4": {
                "left": 1015,
                "top": 400,
                "width": 100,
                "height": 120
            },
            "card_5": {
                "left": 1120,
                "top": 400,
                "width": 100,
                "height": 120
            },
            "carte_1m": {
                "left": 850,
                "top": 600,
                "width": 110,
                "height": 100
            },
            "carte_2m": {
                "left": 960,
                "top": 600,
                "width": 110,
                "height": 100
            },
            "pot_total": {
                "left": 960,
                "top": 380,
                "width": 100,
                "height": 30
            },
            "mes_jetons": {
                "left": 960,
                "top": 700,
                "width": 100,
                "height": 30
            },
            "montant_call": {
                "left": 1100,
                "top": 700,
                "width": 100,
                "height": 30
            },
            "montant_relance": {
                "left": 1240,
                "top": 700,
                "width": 100,
                "height": 30
            },

            # Jetons des autres joueurs (positions à la table)
            "jetons_joueur1": {
                "left": 500,
                "top": 300,
                "width": 100,
                "height": 30
            },
            "jetons_joueur2": {
                "left": 700,
                "top": 200,
                "width": 100,
                "height": 30
            },
            "jetons_joueur3": {
                "left": 900,
                "top": 200,
                "width": 100,
                "height": 30
            },
            "jetons_joueur4": {
                "left": 1100,
                "top": 200,
                "width": 100,
                "height": 30
            },
            "jetons_joueur5": {
                "left": 1300,
                "top": 300,
                "width": 100,
                "height": 30
            },
            "jetons_joueur6": {
                "left": 1200,
                "top": 500,
                "width": 100,
                "height": 30
            },
            "jetons_joueur7": {
                "left": 1000,
                "top": 500,
                "width": 100,
                "height": 30
            },

            # Mises des joueurs
            "mise_joueur1": {
                "left": 600,
                "top": 350,
                "width": 100,
                "height": 30
            },
            "mise_joueur2": {
                "left": 700,
                "top": 300,
                "width": 100,
                "height": 30
            },
            "mise_joueur3": {
                "left": 900,
                "top": 300,
                "width": 100,
                "height": 30
            },
            "mise_joueur4": {
                "left": 1100,
                "top": 300,
                "width": 100,
                "height": 30
            },
            "mise_joueur5": {
                "left": 1200,
                "top": 350,
                "width": 100,
                "height": 30
            },
            "mise_joueur6": {
                "left": 1100,
                "top": 450,
                "width": 100,
                "height": 30
            },
            "mise_joueur7": {
                "left": 900,
                "top": 450,
                "width": 100,
                "height": 30
            },
            "ma_mise": {
                "left": 800,
                "top": 500,
                "width": 100,
                "height": 30
            },
            "mon_allin": {
                "left": 800,
                "top": 550,
                "width": 100,
                "height": 30
            },

            # All-in des joueurs
            "allin_joueur1": {
                "left": 600,
                "top": 380,
                "width": 100,
                "height": 30
            },
            "allin_joueur2": {
                "left": 700,
                "top": 330,
                "width": 100,
                "height": 30
            },
            "allin_joueur3": {
                "left": 900,
                "top": 330,
                "width": 100,
                "height": 30
            },
            "allin_joueur4": {
                "left": 1100,
                "top": 330,
                "width": 100,
                "height": 30
            },
            "allin_joueur5": {
                "left": 1200,
                "top": 380,
                "width": 100,
                "height": 30
            },
            "allin_joueur6": {
                "left": 1100,
                "top": 480,
                "width": 100,
                "height": 30
            },
            "allin_joueur7": {
                "left": 900,
                "top": 480,
                "width": 100,
                "height": 30
            },

            # Boutons des joueurs (position du dealer button)
            "bouton_joueur1": {
                "left": 450,
                "top": 280,
                "width": 30,
                "height": 30
            },
            "bouton_joueur2": {
                "left": 650,
                "top": 180,
                "width": 30,
                "height": 30
            },
            "bouton_joueur3": {
                "left": 850,
                "top": 180,
                "width": 30,
                "height": 30
            },
            "bouton_joueur4": {
                "left": 1050,
                "top": 180,
                "width": 30,
                "height": 30
            },
            "bouton_joueur5": {
                "left": 1250,
                "top": 280,
                "width": 30,
                "height": 30
            },
            "bouton_joueur6": {
                "left": 1150,
                "top": 480,
                "width": 30,
                "height": 30
            },
            "bouton_joueur7": {
                "left": 950,
                "top": 480,
                "width": 30,
                "height": 30
            },
            "bouton_moi": {
                "left": 750,
                "top": 580,
                "width": 30,
                "height": 30
            },

            # Pseudos des joueurs
            "pseudo_joueur1": {
                "left": 480,
                "top": 250,
                "width": 120,
                "height": 25
            },
            "pseudo_joueur2": {
                "left": 680,
                "top": 150,
                "width": 120,
                "height": 25
            },
            "pseudo_joueur3": {
                "left": 880,
                "top": 150,
                "width": 120,
                "height": 25
            },
            "pseudo_joueur4": {
                "left": 1080,
                "top": 150,
                "width": 120,
                "height": 25
            },
            "pseudo_joueur5": {
                "left": 1280,
                "top": 250,
                "width": 120,
                "height": 25
            },
            "pseudo_joueur6": {
                "left": 1180,
                "top": 450,
                "width": 120,
                "height": 25
            },
            "pseudo_joueur7": {
                "left": 980,
                "top": 450,
                "width": 120,
                "height": 25
            }
        },
        "presets": {
            "defaut": {
                "roi": {
                    # Cartes communes individuelles
                    "card_1": {
                        "left": 700,
                        "top": 400,
                        "width": 100,
                        "height": 120
                    },
                    "card_2": {
                        "left": 805,
                        "top": 400,
                        "width": 100,
                        "height": 120
                    },
                    "card_3": {
                        "left": 910,
                        "top": 400,
                        "width": 100,
                        "height": 120
                    },
                    "card_4": {
                        "left": 1015,
                        "top": 400,
                        "width": 100,
                        "height": 120
                    },
                    "card_5": {
                        "left": 1120,
                        "top": 400,
                        "width": 100,
                        "height": 120
                    },
                    "carte_1m": {
                        "left": 850,
                        "top": 600,
                        "width": 110,
                        "height": 100
                    },
                    "carte_2m": {
                        "left": 960,
                        "top": 600,
                        "width": 110,
                        "height": 100
                    },
                    "pot_total": {
                        "left": 960,
                        "top": 380,
                        "width": 100,
                        "height": 30
                    },
                    "mes_jetons": {
                        "left": 960,
                        "top": 700,
                        "width": 100,
                        "height": 30
                    },
                    "montant_call": {
                        "left": 1100,
                        "top": 700,
                        "width": 100,
                        "height": 30
                    },
                    "montant_relance": {
                        "left": 1240,
                        "top": 700,
                        "width": 100,
                        "height": 30
                    },

                    # Jetons des autres joueurs (positions à la table)
                    "jetons_joueur1": {
                        "left": 500,
                        "top": 300,
                        "width": 100,
                        "height": 30
                    },
                    "jetons_joueur2": {
                        "left": 700,
                        "top": 200,
                        "width": 100,
                        "height": 30
                    },
                    "jetons_joueur3": {
                        "left": 900,
                        "top": 200,
                        "width": 100,
                        "height": 30
                    },
                    "jetons_joueur4": {
                        "left": 1100,
                        "top": 200,
                        "width": 100,
                        "height": 30
                    },
                    "jetons_joueur5": {
                        "left": 1300,
                        "top": 300,
                        "width": 100,
                        "height": 30
                    },
                    "jetons_joueur6": {
                        "left": 1200,
                        "top": 500,
                        "width": 100,
                        "height": 30
                    },

                    # Mises des joueurs
                    "mise_joueur1": {
                        "left": 600,
                        "top": 350,
                        "width": 100,
                        "height": 30
                    },
                    "mise_joueur2": {
                        "left": 700,
                        "top": 300,
                        "width": 100,
                        "height": 30
                    },
                    "mise_joueur3": {
                        "left": 900,
                        "top": 300,
                        "width": 100,
                        "height": 30
                    },
                    "mise_joueur4": {
                        "left": 1100,
                        "top": 300,
                        "width": 100,
                        "height": 30
                    },
                    "mise_joueur5": {
                        "left": 1200,
                        "top": 350,
                        "width": 100,
                        "height": 30
                    },
                    "mise_joueur6": {
                        "left": 1100,
                        "top": 450,
                        "width": 100,
                        "height": 30
                    },
                    "ma_mise": {
                        "left": 800,
                        "top": 500,
                        "width": 100,
                        "height": 30
                    },
                    "mon_allin": {
                        "left": 800,
                        "top": 550,
                        "width": 100,
                        "height": 30
                    },

                    # All-in des joueurs
                    "allin_joueur1": {
                        "left": 600,
                        "top": 380,
                        "width": 100,
                        "height": 30
                    },
                    "allin_joueur2": {
                        "left": 700,
                        "top": 330,
                        "width": 100,
                        "height": 30
                    },
                    "allin_joueur3": {
                        "left": 900,
                        "top": 330,
                        "width": 100,
                        "height": 30
                    },
                    "allin_joueur4": {
                        "left": 1100,
                        "top": 330,
                        "width": 100,
                        "height": 30
                    },
                    "allin_joueur5": {
                        "left": 1200,
                        "top": 380,
                        "width": 100,
                        "height": 30
                    },
                    "allin_joueur6": {
                        "left": 1100,
                        "top": 480,
                        "width": 100,
                        "height": 30
                    },
                    "allin_joueur7": {
                        "left": 900,
                        "top": 480,
                        "width": 100,
                        "height": 30
                    },

                    # Boutons des joueurs (position du dealer button)
                    "bouton_joueur1": {
                        "left": 450,
                        "top": 280,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_joueur2": {
                        "left": 650,
                        "top": 180,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_joueur3": {
                        "left": 850,
                        "top": 180,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_joueur4": {
                        "left": 1050,
                        "top": 180,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_joueur5": {
                        "left": 1250,
                        "top": 280,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_joueur6": {
                        "left": 1150,
                        "top": 480,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_joueur7": {
                        "left": 950,
                        "top": 480,
                        "width": 30,
                        "height": 30
                    },
                    "bouton_moi": {
                        "left": 750,
                        "top": 580,
                        "width": 30,
                        "height": 30
                    },

                    # Pseudos des joueurs
                    "pseudo_joueur1": {
                        "left": 480,
                        "top": 250,
                        "width": 120,
                        "height": 25
                    },
                    "pseudo_joueur2": {
                        "left": 680,
                        "top": 150,
                        "width": 120,
                        "height": 25
                    },
                    "pseudo_joueur3": {
                        "left": 880,
                        "top": 150,
                        "width": 120,
                        "height": 25
                    },
                    "pseudo_joueur4": {
                        "left": 1080,
                        "top": 150,
                        "width": 120,
                        "height": 25
                    },
                    "pseudo_joueur5": {
                        "left": 1280,
                        "top": 250,
                        "width": 120,
                        "height": 25
                    },
                    "pseudo_joueur6": {
                        "left": 1180,
                        "top": 450,
                        "width": 120,
                        "height": 25
                    },
                    "pseudo_joueur7": {
                        "left": 980,
                        "top": 450,
                        "width": 120,
                        "height": 25
                    }
                }
            }
        },
        "capture": {
            "method": "mss",
            "fix_colors": True,
            "interval": 0.5
        }
    }

    print("ℹ️ Configuration par défaut créée")
    return default_config

# ============================================================================
#                       CAPTURE D'ÉCRAN
# ============================================================================

def capture_screen(region=None):
    """Capture l'écran ou une région spécifique"""
    try:
        with mss.mss() as sct:
            # Si aucune région n'est spécifiée, capturer tout l'écran
            if region is None:
                region = sct.monitors[1]  # Le moniteur principal

            # Capturer l'écran
            sct_img = sct.grab(region)

            # Convertir en tableau numpy
            img = np.array(sct_img)

            # Convertir de BGRA à BGR (supprimer le canal alpha)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

            return img

    except Exception as e:
        print(f"❌ Erreur lors de la capture d'écran: {e}")
        return None

def save_screenshot(image, filename=None):
    """Sauvegarde une capture d'écran"""
    try:
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        filepath = os.path.join('screenshots', filename)
        cv2.imwrite(filepath, image)
        print(f"✅ Capture d'écran sauvegardée à {filepath}")
        return filepath

    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde de la capture d'écran: {e}")
        messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde de la capture d'écran: {str(e)}")
        return None

# ============================================================================
#                       FONCTION PRINCIPALE
# ============================================================================

def main():
    """Fonction principale pour la calibration simplifiée"""
    print("=" * 80)
    print("POKER ADVISOR - CALIBRATION SIMPLIFIÉE")
    print("=" * 80)

    # Charger la configuration
    config = load_config()

    # Créer la fenêtre principale
    root = tk.Tk()
    root.title("Poker Advisor - Calibration")
    root.geometry("1200x800")

    # Créer un style pour les boutons d'accent
    style = ttk.Style()
    style.configure("Accent.TButton",
                    background="#007bff",
                    foreground="white",
                    font=("Arial", 10, "bold"))

    # Créer les variables
    current_region = tk.StringVar(value=list(config['roi'].keys())[0])
    zoom_factor = tk.DoubleVar(value=1.0)  # Facteur de zoom pour l'aperçu

    # Descriptions des zones
    region_descriptions = {
        # Cartes communes individuelles
        "card_1": "Carte commune 1 (flop)",
        "card_2": "Carte commune 2 (flop)",
        "card_3": "Carte commune 3 (flop)",
        "card_4": "Carte commune 4 (turn)",
        "card_5": "Carte commune 5 (river)",
        "carte_1m": "Votre première carte en main",
        "carte_2m": "Votre deuxième carte en main",
        "pot_total": "Montant du pot total",
        "mes_jetons": "Vos jetons",
        "montant_call": "Montant à suivre (call)",
        "montant_relance": "Montant à relancer",
        "ma_mise": "Votre mise actuelle",
        "mon_allin": "Votre all-in (texte 'All-in')",

        # Jetons des autres joueurs
        "jetons_joueur1": "Jetons du joueur 1 (position 1)",
        "jetons_joueur2": "Jetons du joueur 2 (position 2)",
        "jetons_joueur3": "Jetons du joueur 3 (position 3)",
        "jetons_joueur4": "Jetons du joueur 4 (position 4)",
        "jetons_joueur5": "Jetons du joueur 5 (position 5)",
        "jetons_joueur6": "Jetons du joueur 6 (position 6)",
        "jetons_joueur7": "Jetons du joueur 7 (position 7)",

        # Mises des joueurs
        "mise_joueur1": "Mise du joueur 1",
        "mise_joueur2": "Mise du joueur 2",
        "mise_joueur3": "Mise du joueur 3",
        "mise_joueur4": "Mise du joueur 4",
        "mise_joueur5": "Mise du joueur 5",
        "mise_joueur6": "Mise du joueur 6",
        "mise_joueur7": "Mise du joueur 7",

        # All-in des joueurs
        "allin_joueur1": "All-in du joueur 1 (texte 'All-in')",
        "allin_joueur2": "All-in du joueur 2 (texte 'All-in')",
        "allin_joueur3": "All-in du joueur 3 (texte 'All-in')",
        "allin_joueur4": "All-in du joueur 4 (texte 'All-in')",
        "allin_joueur5": "All-in du joueur 5 (texte 'All-in')",
        "allin_joueur6": "All-in du joueur 6 (texte 'All-in')",
        "allin_joueur7": "All-in du joueur 7 (texte 'All-in')",

        # Boutons des joueurs (dealer button)
        "bouton_joueur1": "Bouton dealer du joueur 1",
        "bouton_joueur2": "Bouton dealer du joueur 2",
        "bouton_joueur3": "Bouton dealer du joueur 3",
        "bouton_joueur4": "Bouton dealer du joueur 4",
        "bouton_joueur5": "Bouton dealer du joueur 5",
        "bouton_joueur6": "Bouton dealer du joueur 6",
        "bouton_joueur7": "Bouton dealer du joueur 7",
        "bouton_moi": "Mon bouton dealer",

        # Pseudos des joueurs
        "pseudo_joueur1": "Pseudo du joueur 1",
        "pseudo_joueur2": "Pseudo du joueur 2",
        "pseudo_joueur3": "Pseudo du joueur 3",
        "pseudo_joueur4": "Pseudo du joueur 4",
        "pseudo_joueur5": "Pseudo du joueur 5",
        "pseudo_joueur6": "Pseudo du joueur 6",
        "pseudo_joueur7": "Pseudo du joueur 7"
    }

    # Catégories de zones
    zone_categories = {
        "Zones principales": ["card_1", "card_2", "card_3", "card_4", "card_5", "carte_1m", "carte_2m", "pot_total", "mes_jetons", "montant_call", "montant_relance", "ma_mise", "mon_allin"],
        "Jetons des joueurs": [f"jetons_joueur{i}" for i in range(1, 8)],
        "Mises des joueurs": [f"mise_joueur{i}" for i in range(1, 8)],
        "All-in des joueurs": [f"allin_joueur{i}" for i in range(1, 8)],
        "Boutons des joueurs": [f"bouton_joueur{i}" for i in range(1, 8)] + ["bouton_moi"],
        "Pseudos des joueurs": [f"pseudo_joueur{i}" for i in range(1, 8)]
    }

    # Fonction pour restaurer la configuration à partir d'une sauvegarde
    def restore_backup():
        backup_file = f"{CONFIG_FILE}.backup"
        if os.path.exists(backup_file):
            try:
                with open(backup_file, 'r') as f:
                    backup_config = json.load(f)

                # Ajouter les zones manquantes à la configuration restaurée
                default_config = create_default_config()

                # Supprimer les régions à remplacer
                regions_to_remove = ["cartes_communes", "cartes_main", "historique", "table_entiere", "pot"]
                for region in regions_to_remove:
                    if region in backup_config['roi']:
                        print(f"⚠️ Suppression de la région '{region}' pour la remplacer par des régions individuelles")
                        del backup_config['roi'][region]

                for zone, coords in default_config['roi'].items():
                    if zone not in backup_config['roi']:
                        backup_config['roi'][zone] = coords

                # Sauvegarder la configuration restaurée
                save_config(backup_config)
                messagebox.showinfo("Restauration", "Configuration restaurée avec succès. Redémarrez l'application pour appliquer les changements.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la restauration de la sauvegarde: {e}")
        else:
            messagebox.showinfo("Information", "Aucune sauvegarde disponible.")

    # Créer les frames
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Barre de menu
    menu_bar = tk.Menu(root)
    root.config(menu=menu_bar)

    # Menu Fichier
    file_menu = tk.Menu(menu_bar, tearoff=0)
    menu_bar.add_cascade(label="Fichier", menu=file_menu)
    file_menu.add_command(label="Restaurer la configuration précédente", command=restore_backup)
    file_menu.add_separator()
    file_menu.add_command(label="Quitter", command=root.quit)

    left_frame = ttk.Frame(main_frame, padding=10, width=300)
    left_frame.pack(side=tk.LEFT, fill=tk.Y)

    right_frame = ttk.Frame(main_frame, padding=10)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

    # Créer la figure pour l'aperçu
    fig = plt.figure(figsize=(8, 6))
    ax = fig.add_subplot(111)
    canvas = FigureCanvasTkAgg(fig, master=right_frame)
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # Variables pour la sélection par rectangle
    is_selecting = False
    selection_start = None
    selection_rect = None

    # Fonction pour activer/désactiver la sélection par rectangle
    def toggle_rectangle_selection():
        nonlocal is_selecting, selection_rect

        if not is_selecting:
            # Activer la sélection
            is_selecting = True

            # Connecter les événements de la souris
            canvas.mpl_connect('button_press_event', on_mouse_press)
            canvas.mpl_connect('button_release_event', on_mouse_release)
            canvas.mpl_connect('motion_notify_event', on_mouse_move)

            # Changer le texte du bouton
            for widget in canvas.get_tk_widget().master.winfo_children():
                if isinstance(widget, ttk.Button) and widget['text'] == "Activer la sélection par rectangle":
                    widget.config(text="Désactiver la sélection par rectangle")
                    break

            messagebox.showinfo("Sélection par rectangle",
                               "Mode sélection par rectangle activé. Cliquez et faites glisser sur l'image pour définir une zone.")
        else:
            # Désactiver la sélection
            is_selecting = False

            # Changer le texte du bouton
            for widget in canvas.get_tk_widget().master.winfo_children():
                if isinstance(widget, ttk.Button) and widget['text'] == "Désactiver la sélection par rectangle":
                    widget.config(text="Activer la sélection par rectangle")
                    break

            # Supprimer le rectangle de sélection s'il existe
            if selection_rect:
                selection_rect.remove()
                selection_rect = None
                canvas.draw()

    # Fonction pour gérer le clic de souris
    def on_mouse_press(event):
        nonlocal is_selecting, selection_start, selection_rect

        if not is_selecting or event.inaxes != ax:
            return

        # Enregistrer le point de départ
        selection_start = (event.xdata, event.ydata)

        # Créer un rectangle initial
        if selection_rect:
            selection_rect.remove()

        selection_rect = plt.Rectangle(
            selection_start, 0, 0,
            fill=False, edgecolor='red', linewidth=2
        )
        ax.add_patch(selection_rect)
        canvas.draw()

    # Fonction pour gérer le déplacement de la souris
    def on_mouse_move(event):
        nonlocal is_selecting, selection_start, selection_rect

        if not is_selecting or not selection_start or event.inaxes != ax:
            return

        # Mettre à jour le rectangle
        x0, y0 = selection_start
        width = event.xdata - x0
        height = event.ydata - y0

        selection_rect.set_width(width)
        selection_rect.set_height(height)
        canvas.draw()

    # Fonction pour gérer le relâchement du clic de souris
    def on_mouse_release(event):
        nonlocal is_selecting, selection_start, selection_rect

        if not is_selecting or not selection_start or event.inaxes != ax:
            return

        # Calculer les coordonnées du rectangle
        x0, y0 = selection_start
        x1, y1 = event.xdata, event.ydata

        # S'assurer que x0 < x1 et y0 < y1
        if x1 < x0:
            x0, x1 = x1, x0
        if y1 < y0:
            y0, y1 = y1, y0

        # Convertir les coordonnées de l'image en coordonnées d'écran
        region_name = current_region.get()
        if region_name not in config['roi']:
            messagebox.showerror("Erreur", "Aucune zone sélectionnée")
            return

        region = config['roi'][region_name]
        base_x, base_y = region['left'], region['top']

        # Calculer les nouvelles coordonnées en tenant compte du zoom
        zoom = zoom_factor.get()

        # Calculer les coordonnées en pixels
        left = int(base_x + x0 / zoom)
        top = int(base_y + y0 / zoom)
        width = max(1, int((x1 - x0) / zoom))
        height = max(1, int((y1 - y0) / zoom))

        # Mettre à jour les entrées de coordonnées
        coord_entries['left'].delete(0, tk.END)
        coord_entries['left'].insert(0, str(left))

        coord_entries['top'].delete(0, tk.END)
        coord_entries['top'].insert(0, str(top))

        coord_entries['width'].delete(0, tk.END)
        coord_entries['width'].insert(0, str(width))

        coord_entries['height'].delete(0, tk.END)
        coord_entries['height'].insert(0, str(height))

        # Note: Les curseurs ne sont pas utilisés dans cette version simplifiée
        # Les lignes ci-dessous sont commentées car coord_vars n'est pas défini
        # if 'left' in coord_vars:
        #     coord_vars['left'].set(left)
        # if 'top' in coord_vars:
        #     coord_vars['top'].set(top)
        # if 'width' in coord_vars:
        #     coord_vars['width'].set(width)
        # if 'height' in coord_vars:
        #     coord_vars['height'].set(height)

        # Appliquer les coordonnées
        update_preview()

        # Réinitialiser la sélection
        selection_start = None

        # Désactiver la sélection par rectangle
        toggle_rectangle_selection()

    # Bouton pour activer la sélection par rectangle
    ttk.Button(
        right_frame,
        text="Activer la sélection par rectangle",
        command=toggle_rectangle_selection
    ).pack(fill=tk.X, pady=5)

    # Ajouter un frame pour les fonctionnalités avancées
    advanced_frame = ttk.LabelFrame(right_frame, text="Fonctionnalités avancées")
    advanced_frame.pack(fill=tk.X, pady=10, padx=5)

    # Bouton pour copier les coordonnées d'une zone à une autre
    copy_frame = ttk.Frame(advanced_frame)
    copy_frame.pack(fill=tk.X, pady=5)

    ttk.Label(copy_frame, text="Copier les coordonnées de:").pack(side=tk.LEFT, padx=5)

    # Combobox pour sélectionner la zone source
    source_zone_var = tk.StringVar()
    source_zone_combo = ttk.Combobox(copy_frame, textvariable=source_zone_var, width=20)
    source_zone_combo.pack(side=tk.LEFT, padx=5)

    # Fonction pour mettre à jour la liste des zones sources
    def update_source_zones():
        # Récupérer toutes les zones disponibles
        all_zones = []
        for zone in config['roi'].keys():
            all_zones.append(f"{zone} - {region_descriptions.get(zone, zone)}")

        # Mettre à jour le combobox
        source_zone_combo['values'] = all_zones
        if all_zones:
            source_zone_combo.current(0)

    # Fonction pour copier les coordonnées
    def copy_coordinates():
        # Obtenir la zone source
        source_selected = source_zone_var.get()
        if not source_selected:
            messagebox.showerror("Erreur", "Aucune zone source sélectionnée")
            return

        # Extraire la clé de la zone source
        source_key = source_selected.split(' - ')[0]

        # Obtenir la zone cible (zone actuelle)
        target_key = current_region.get()

        if source_key == target_key:
            messagebox.showinfo("Information", "La zone source et la zone cible sont identiques")
            return

        if source_key not in config['roi'] or target_key not in config['roi']:
            messagebox.showerror("Erreur", "Zone source ou cible non trouvée dans la configuration")
            return

        # Copier les coordonnées
        source_coords = config['roi'][source_key]

        # Mettre à jour les entrées
        for coord in coord_entries:
            if coord in source_coords:
                # Mettre à jour l'entrée
                coord_entries[coord].delete(0, tk.END)
                coord_entries[coord].insert(0, str(source_coords[coord]))

        # Mettre à jour la configuration
        for coord, value in source_coords.items():
            config['roi'][target_key][coord] = value

        # Mettre à jour l'aperçu
        schedule_preview_update()

        # Sauvegarder la configuration
        if save_config(config):
            messagebox.showinfo("Succès", f"Coordonnées copiées de '{source_key}' vers '{target_key}'")
        else:
            messagebox.showwarning("Attention", "Coordonnées copiées mais erreur lors de la sauvegarde")

    # Bouton pour copier
    ttk.Button(
        copy_frame,
        text="Copier",
        command=copy_coordinates
    ).pack(side=tk.LEFT, padx=5)

    # Mettre à jour la liste des zones sources
    update_source_zones()

    # Ajouter un label d'aide
    ttk.Label(
        right_frame,
        text="Astuce: Utilisez les curseurs, le rectangle de sélection ou la copie pour définir rapidement les coordonnées",
        font=("Arial", 9, "italic"),
        foreground="gray"
    ).pack(fill=tk.X, pady=(0, 10))

    # Capture d'écran initiale
    screenshot = capture_screen()

    # Variables pour le debounce
    global last_adjustment_time, update_pending, debounce_delay
    last_adjustment_time = 0
    update_pending = None
    debounce_delay = 300  # Délai en millisecondes

    # Fonction pour planifier une mise à jour différée de l'aperçu
    def schedule_preview_update():
        """Planifie une mise à jour différée de l'aperçu pour éviter les mises à jour trop fréquentes"""
        global update_pending, debounce_delay

        # Annuler la mise à jour précédente si elle est en attente
        if update_pending is not None:
            root.after_cancel(update_pending)
            update_pending = None

        # Sauvegarder les coordonnées actuelles dans la configuration
        region_name = current_region.get()
        if region_name and region_name in config['roi']:
            # Récupérer les valeurs des entrées
            coords_changed = False
            for coord, entry in coord_entries.items():
                value = entry.get().strip()
                if value:  # Ignorer les champs vides
                    try:
                        int_value = int(value)
                        # Vérifier si la valeur a changé
                        if config['roi'][region_name][coord] != int_value:
                            config['roi'][region_name][coord] = int_value
                            coords_changed = True
                            print(f"Coordonnée {coord} de {region_name} mise à jour: {int_value}")
                    except ValueError:
                        print(f"Valeur invalide pour {coord}: {value}")

            # Sauvegarder la configuration seulement si des coordonnées ont changé
            if coords_changed:
                save_config(config)
                print(f"Configuration sauvegardée pour la zone: {region_name}")

        # Planifier une nouvelle mise à jour après le délai de debounce
        update_pending = root.after(debounce_delay, update_preview)

    # Fonction pour mettre à jour l'aperçu
    def update_preview():
        """Met à jour l'aperçu de la zone sélectionnée"""
        global update_pending
        update_pending = None
        print(f"Mise à jour de l'aperçu pour la zone: {current_region.get()}")

        # Vérifier si une capture d'écran est disponible
        if screenshot is None:
            print("Pas de capture d'écran disponible, capture d'un nouvel écran...")
            capture_new_screenshot()
            return

        # Vérifier si la capture d'écran est valide
        try:
            if hasattr(screenshot, 'size') and screenshot.size == 0:
                print("Capture d'écran invalide (taille 0), capture d'un nouvel écran...")
                capture_new_screenshot()
                return

            # Vérifier si la capture d'écran a une forme valide
            if not hasattr(screenshot, 'shape') or len(screenshot.shape) < 2:
                print("Capture d'écran invalide (forme incorrecte), capture d'un nouvel écran...")
                capture_new_screenshot()
                return
        except Exception as e:
            print(f"Erreur lors de la vérification de la capture d'écran: {e}")
            capture_new_screenshot()
            return

        # Effacer l'axe
        ax.clear()

        # Obtenir la région actuelle
        region_name = current_region.get()
        if region_name not in config['roi']:
            print(f"Zone '{region_name}' non trouvée dans la configuration")
            return

        region = config['roi'][region_name]
        print(f"Coordonnées de la zone: {region}")

        # Extraire la région de la capture d'écran
        x, y, w, h = region['left'], region['top'], region['width'], region['height']

        # Vérifier que les coordonnées sont valides
        if x < 0 or y < 0 or w <= 0 or h <= 0 or x + w > screenshot.shape[1] or y + h > screenshot.shape[0]:
            print(f"Coordonnées invalides: x={x}, y={y}, w={w}, h={h}, image={screenshot.shape}")
            ax.text(0.5, 0.5, "Coordonnées invalides", ha='center', va='center', fontsize=12)
            canvas.draw()
            return

        # Afficher l'image complète au lieu de juste la région
        try:
            # Créer une copie de l'image pour ne pas modifier l'original
            full_img = screenshot.copy()

            # Dessiner les rectangles de toutes les zones sur l'image
            for zone_key, zone_coords in config['roi'].items():
                if 'left' in zone_coords and 'top' in zone_coords and 'width' in zone_coords and 'height' in zone_coords:
                    zx, zy, zw, zh = zone_coords['left'], zone_coords['top'], zone_coords['width'], zone_coords['height']
                    # Utiliser une couleur différente pour la zone actuellement sélectionnée
                    if zone_key == region_name:
                        color = (0, 0, 255)  # Rouge pour la zone sélectionnée
                        thickness = 2
                    else:
                        color = (0, 255, 0)  # Vert pour les autres zones
                        thickness = 1

                    # Dessiner le rectangle
                    cv2.rectangle(full_img, (zx, zy), (zx + zw, zy + zh), color, thickness)

                    # Ajouter le nom de la zone
                    cv2.putText(full_img, zone_key, (zx, zy - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # Appliquer le zoom si nécessaire
            current_zoom = zoom_factor.get()
            if current_zoom != 1.0:
                h_full, w_full = full_img.shape[:2]
                full_img = cv2.resize(full_img, (int(w_full*current_zoom), int(h_full*current_zoom)), interpolation=cv2.INTER_CUBIC)

            # Afficher l'image complète
            ax.imshow(cv2.cvtColor(full_img, cv2.COLOR_BGR2RGB))
            zone_title = region_descriptions.get(region_name, region_name)
            ax.set_title(f"Zone: {zone_title} (Zoom: {current_zoom:.1f}x)")
            ax.axis('off')

            # Fonction pour gérer le clic de souris sur l'aperçu principal
            def on_preview_mouse_press(event):
                nonlocal selection_start, is_selecting

                if event.inaxes != ax:
                    return

                # Enregistrer le point de départ
                selection_start = (event.xdata, event.ydata)
                is_selecting = True

                # Créer un rectangle initial
                global selection_rect
                if 'selection_rect' in globals() and selection_rect:
                    selection_rect.remove()

                selection_rect = plt.Rectangle(
                    selection_start, 0, 0,
                    fill=False, edgecolor='yellow', linewidth=3
                )
                ax.add_patch(selection_rect)
                canvas.draw()

            # Fonction pour gérer le déplacement de la souris sur l'aperçu principal
            def on_preview_mouse_move(event):
                nonlocal selection_start, is_selecting

                if not is_selecting or not selection_start or event.inaxes != ax:
                    return

                # Mettre à jour le rectangle
                x0, y0 = selection_start
                width = event.xdata - x0
                height = event.ydata - y0

                global selection_rect
                if 'selection_rect' in globals() and selection_rect:
                    selection_rect.set_width(width)
                    selection_rect.set_height(height)
                    canvas.draw()

            # Fonction pour gérer le relâchement du clic de souris sur l'aperçu principal
            def on_preview_mouse_release(event):
                nonlocal selection_start, is_selecting

                if not is_selecting or not selection_start or event.inaxes != ax:
                    return

                # Calculer les coordonnées du rectangle
                x0, y0 = selection_start
                x1, y1 = event.xdata, event.ydata

                # S'assurer que x0 < x1 et y0 < y1
                if x1 < x0:
                    x0, x1 = x1, x0
                if y1 < y0:
                    y0, y1 = y1, y0

                # Calculer les coordonnées en pixels en tenant compte du zoom
                current_zoom = zoom_factor.get()

                # Calculer les coordonnées en pixels
                left = int(x0 / current_zoom)
                top = int(y0 / current_zoom)
                width = max(1, int((x1 - x0) / current_zoom))
                height = max(1, int((y1 - y0) / current_zoom))

                # Mettre à jour les entrées de coordonnées
                coord_entries['left'].delete(0, tk.END)
                coord_entries['left'].insert(0, str(left))

                coord_entries['top'].delete(0, tk.END)
                coord_entries['top'].insert(0, str(top))

                coord_entries['width'].delete(0, tk.END)
                coord_entries['width'].insert(0, str(width))

                coord_entries['height'].delete(0, tk.END)
                coord_entries['height'].insert(0, str(height))

                # Appliquer les coordonnées
                schedule_preview_update()

                # Réinitialiser la sélection
                selection_start = None
                is_selecting = False

            # Connecter les événements de la souris
            canvas.mpl_connect('button_press_event', on_preview_mouse_press)
            canvas.mpl_connect('button_release_event', on_preview_mouse_release)
            canvas.mpl_connect('motion_notify_event', on_preview_mouse_move)

        except Exception as e:
            print(f"Erreur lors de l'affichage de l'image: {e}")
            ax.text(0.5, 0.5, f"Erreur: {str(e)}", ha='center', va='center', fontsize=12)
            canvas.draw()
            return

        # Mettre à jour le canvas et forcer le rafraîchissement
        canvas.draw()
        canvas.flush_events()  # Forcer le traitement des événements en attente
        root.update_idletasks()  # Forcer la mise à jour de l'interface
        print("Canvas mis à jour avec succès")

        # Afficher l'aperçu de la zone sélectionnée dans un petit cadre
        if 'zone_preview_frame' not in globals():
            # Créer un cadre pour l'aperçu de la zone
            global zone_preview_frame, zone_preview_label, zone_info_label
            zone_preview_frame = ttk.LabelFrame(right_frame, text="Aperçu de la zone sélectionnée")
            zone_preview_frame.pack(fill=tk.X, pady=10, padx=5, after=canvas.get_tk_widget())

            # Créer un label pour l'image
            zone_preview_label = ttk.Label(zone_preview_frame)
            zone_preview_label.pack(padx=10, pady=10)

            # Créer un label pour les informations
            zone_info_label = ttk.Label(zone_preview_frame, font=("Arial", 9))
            zone_info_label.pack(pady=5)

        # Extraire la région sélectionnée
        try:
            region_img = screenshot[y:y+h, x:x+w]
            if region_img.size == 0:
                return

            # Convertir l'image pour l'affichage
            img = cv2.cvtColor(region_img, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(img)

            # Calculer le facteur de zoom pour afficher l'image plus grande si elle est petite
            preview_zoom = 1
            if w < 200 or h < 200:
                preview_zoom = min(3, 200 / min(w, h))
                img = img.resize((int(w * preview_zoom), int(h * preview_zoom)), Image.LANCZOS)

            # Limiter la taille maximale de l'aperçu
            max_width = 400
            max_height = 300
            if img.width > max_width or img.height > max_height:
                ratio = min(max_width / img.width, max_height / img.height)
                img = img.resize((int(img.width * ratio), int(img.height * ratio)), Image.LANCZOS)
                preview_zoom *= ratio

            img_tk = ImageTk.PhotoImage(img)

            # Mettre à jour l'image
            zone_preview_label.config(image=img_tk)
            zone_preview_label.image = img_tk  # Garder une référence

            # Mettre à jour les informations
            zone_title = region_descriptions.get(region_name, region_name)
            zone_info_label.config(text=f"Zone: {zone_title} | Position: ({x}, {y}) | Taille: {w}x{h} | Zoom: {preview_zoom:.1f}x")

        except Exception as e:
            print(f"Erreur lors de l'affichage de l'aperçu de la zone: {e}")

        # Mettre à jour les entrées de coordonnées si elles existent
        if 'coord_entries' in globals() or 'coord_entries' in locals():
            print(f"Mise à jour des coordonnées pour la zone: {region_name}")
            print(f"Coordonnées à appliquer: {region}")

            # Forcer la mise à jour des entrées
            for coord in coord_entries:
                if coord in region:
                    try:
                        # Effacer et réinsérer la valeur pour s'assurer qu'elle est bien mise à jour
                        coord_entries[coord].delete(0, tk.END)
                        coord_entries[coord].insert(0, str(region[coord]))
                    except Exception as e:
                        print(f"Erreur lors de la mise à jour de l'entrée {coord}: {e}")
                else:
                    print(f"Attention: Coordonnée {coord} non trouvée dans la région {region_name}")

            # Forcer le rafraîchissement de l'interface
            root.update_idletasks()
            print("Mise à jour des coordonnées terminée")

    # Fonction pour capturer l'écran
    def capture_new_screenshot():
        nonlocal screenshot
        print("Capture d'un nouvel écran...")
        try:
            screenshot = capture_screen()
            print(f"Capture d'écran effectuée: {screenshot.shape if screenshot is not None else 'None'}")

            # Vérifier si la capture d'écran est valide
            if screenshot is None or (hasattr(screenshot, 'size') and screenshot.size == 0):
                print("Capture d'écran invalide ou vide")
                messagebox.showerror("Erreur", "La capture d'écran a échoué. Veuillez réessayer.")
                return

            # Sauvegarder la capture d'écran dans un fichier temporaire
            try:
                temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                temp_file = os.path.join(temp_dir, "last_screenshot.png")
                cv2.imwrite(temp_file, screenshot)
                print(f"Capture d'écran sauvegardée dans {temp_file}")
            except Exception as e:
                print(f"Erreur lors de la sauvegarde de la capture d'écran: {e}")

            # Mettre à jour l'aperçu immédiatement
            print("Mise à jour immédiate de l'aperçu après capture d'écran")
            update_preview()

            # Puis programmer une autre mise à jour après un délai pour s'assurer que tout est bien mis à jour
            root.after(300, update_preview)

            # Afficher l'aperçu à l'échelle 1:1
            show_fullsize_preview()
        except Exception as e:
            print(f"Erreur lors de la capture d'écran: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la capture d'écran: {str(e)}")
            screenshot = None

    # Fonction pour afficher un aperçu à l'échelle 1:1 de la capture d'écran avec des poignées interactives
    def show_fullsize_preview():
        # Utiliser la variable globale pour suivre la fenêtre d'aperçu
        global fullsize_preview_window

        if screenshot is None:
            messagebox.showerror("Erreur", "Aucune capture d'écran disponible")
            return

        # Fermer la fenêtre existante si elle existe
        try:
            if 'fullsize_preview_window' in globals() and fullsize_preview_window is not None:
                if fullsize_preview_window.winfo_exists():
                    print("Fermeture de la fenêtre d'aperçu existante")
                    fullsize_preview_window.destroy()
        except Exception as e:
            print(f"Erreur lors de la fermeture de la fenêtre d'aperçu existante: {e}")

        # Créer une nouvelle fenêtre pour l'aperçu
        preview_window = tk.Toplevel(root)
        fullsize_preview_window = preview_window  # Stocker la référence
        preview_window.title("Aperçu à l'échelle 1:1")
        preview_window.geometry("1024x768")  # Taille initiale, sera ajustée

        # Frame principal
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Frame pour les contrôles
        controls_frame = ttk.Frame(main_frame)
        controls_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        # Frame pour le canvas
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # Barres de défilement
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        v_scrollbar = ttk.Scrollbar(canvas_frame)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Canvas
        canvas = tk.Canvas(canvas_frame, xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Configurer les barres de défilement
        h_scrollbar.config(command=canvas.xview)
        v_scrollbar.config(command=canvas.yview)

        # Convertir l'image pour l'affichage
        img = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(img)
        img_tk = ImageTk.PhotoImage(img)

        # Ajouter l'image au canvas
        canvas.create_image(0, 0, anchor=tk.NW, image=img_tk)
        canvas.image = img_tk  # Garder une référence

        # Configurer la région de défilement
        canvas.config(scrollregion=canvas.bbox(tk.ALL))

        # Ajuster la taille de la fenêtre
        screen_width = preview_window.winfo_screenwidth()
        screen_height = preview_window.winfo_screenheight()
        window_width = min(img.width + 30, screen_width - 100)
        window_height = min(img.height + 30, screen_height - 100)
        preview_window.geometry(f"{window_width}x{window_height}")

        # Centrer la fenêtre
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        preview_window.geometry(f"+{x}+{y}")

        # Variables pour le rectangle et les poignées
        rect_id = None
        handles = []
        handle_size = 8
        handle_ids = []
        start_x, start_y = 0, 0
        current_x, current_y = 0, 0
        dragging = False
        resizing = False
        current_handle = None

        # Ajouter un message de débogage
        print("Initialisation de l'aperçu à l'échelle 1:1")

        # Obtenir les coordonnées actuelles de la zone
        region_name = current_region.get()
        if region_name in config['roi']:
            region = config['roi'][region_name]
            start_x = region['left']
            start_y = region['top']
            current_x = start_x + region['width']
            current_y = start_y + region['height']

            # Créer le rectangle initial
            rect_id = canvas.create_rectangle(
                start_x, start_y, current_x, current_y,
                outline="red", width=2, tags="rect"
            )

            # Créer les poignées
            handle_positions = [
                (start_x, start_y, "nw"),                  # Coin supérieur gauche
                (start_x + (current_x - start_x) / 2, start_y, "n"),  # Milieu haut
                (current_x, start_y, "ne"),                # Coin supérieur droit
                (current_x, start_y + (current_y - start_y) / 2, "e"),  # Milieu droit
                (current_x, current_y, "se"),              # Coin inférieur droit
                (start_x + (current_x - start_x) / 2, current_y, "s"),  # Milieu bas
                (start_x, current_y, "sw"),                # Coin inférieur gauche
                (start_x, start_y + (current_y - start_y) / 2, "w")   # Milieu gauche
            ]

            for x, y, tag in handle_positions:
                handle_id = canvas.create_rectangle(
                    x - handle_size / 2, y - handle_size / 2,
                    x + handle_size / 2, y + handle_size / 2,
                    fill="blue", outline="white", tags=f"handle {tag}"
                )
                handle_ids.append(handle_id)
                handles.append((handle_id, tag))

        # Fonction pour mettre à jour les coordonnées dans l'interface principale
        def update_main_interface():
            # Calculer les nouvelles coordonnées
            x1, y1, x2, y2 = canvas.coords(rect_id)
            left = min(x1, x2)
            top = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)

            # Mettre à jour les entrées de coordonnées
            coord_entries['left'].delete(0, tk.END)
            coord_entries['left'].insert(0, str(int(left)))

            coord_entries['top'].delete(0, tk.END)
            coord_entries['top'].insert(0, str(int(top)))

            coord_entries['width'].delete(0, tk.END)
            coord_entries['width'].insert(0, str(int(width)))

            coord_entries['height'].delete(0, tk.END)
            coord_entries['height'].insert(0, str(int(height)))

            # Mettre à jour l'aperçu
            schedule_preview_update()

            # Mettre à jour le label d'information
            info_label.config(text=f"Zone: {region_name} | Position: ({int(left)}, {int(top)}) | Taille: {int(width)}x{int(height)}")

            # Sauvegarder automatiquement les coordonnées
            region_name = current_region.get()
            if region_name in config['roi']:
                config['roi'][region_name]['left'] = int(left)
                config['roi'][region_name]['top'] = int(top)
                config['roi'][region_name]['width'] = int(width)
                config['roi'][region_name]['height'] = int(height)
                save_config(config)
                print(f"Coordonnées sauvegardées automatiquement pour la zone: {region_name}")

        # Fonction pour mettre à jour les poignées
        def update_handles():
            if not rect_id:
                return

            x1, y1, x2, y2 = canvas.coords(rect_id)

            # Positions des poignées
            handle_positions = [
                (x1, y1, "nw"),                  # Coin supérieur gauche
                (x1 + (x2 - x1) / 2, y1, "n"),   # Milieu haut
                (x2, y1, "ne"),                  # Coin supérieur droit
                (x2, y1 + (y2 - y1) / 2, "e"),   # Milieu droit
                (x2, y2, "se"),                  # Coin inférieur droit
                (x1 + (x2 - x1) / 2, y2, "s"),   # Milieu bas
                (x1, y2, "sw"),                  # Coin inférieur gauche
                (x1, y1 + (y2 - y1) / 2, "w")    # Milieu gauche
            ]

            # Mettre à jour les positions des poignées
            for i, (handle_id, _) in enumerate(handles):
                x, y, _ = handle_positions[i]
                canvas.coords(
                    handle_id,
                    x - handle_size / 2, y - handle_size / 2,
                    x + handle_size / 2, y + handle_size / 2
                )

        # Fonction pour afficher l'aperçu de la zone sélectionnée
        def show_zone_preview():
            if not rect_id:
                return

            x1, y1, x2, y2 = canvas.coords(rect_id)
            left = min(x1, x2)
            top = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)

            # Extraire la région de l'image
            try:
                region_img = screenshot[int(top):int(top+height), int(left):int(left+width)]
                if region_img.size == 0:
                    messagebox.showerror("Erreur", "La région sélectionnée est trop petite")
                    return

                # Créer une nouvelle fenêtre pour l'aperçu de la zone
                zone_preview = tk.Toplevel(preview_window)
                zone_preview.title(f"Aperçu de la zone: {region_name}")

                # Convertir l'image pour l'affichage
                img = cv2.cvtColor(region_img, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(img)

                # Calculer le facteur de zoom pour afficher l'image plus grande si elle est petite
                zoom_factor = 1
                if width < 200 or height < 200:
                    zoom_factor = min(5, 200 / min(width, height))
                    img = img.resize((int(width * zoom_factor), int(height * zoom_factor)), Image.LANCZOS)

                img_tk = ImageTk.PhotoImage(img)

                # Ajouter l'image à un label
                label = ttk.Label(zone_preview, image=img_tk)
                label.image = img_tk  # Garder une référence
                label.pack(padx=10, pady=10)

                # Ajouter un label d'information
                ttk.Label(
                    zone_preview,
                    text=f"Zone: {region_name} | Position: ({int(left)}, {int(top)}) | Taille: {int(width)}x{int(height)} | Zoom: {zoom_factor:.1f}x",
                    font=("Arial", 10)
                ).pack(pady=5)

                # Ajouter un bouton pour fermer la fenêtre
                ttk.Button(zone_preview, text="Fermer", command=zone_preview.destroy).pack(pady=10)

                # Centrer la fenêtre
                zone_preview.update_idletasks()
                preview_width = zone_preview.winfo_width()
                preview_height = zone_preview.winfo_height()
                x = (screen_width - preview_width) // 2
                y = (screen_height - preview_height) // 2
                zone_preview.geometry(f"+{x}+{y}")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'extraction de la région: {str(e)}")

        # Gestionnaires d'événements pour le canvas
        def on_press(event):
            nonlocal dragging, resizing, current_handle, start_x, start_y, current_x, current_y, rect_id, handles

            # Ajouter un message de débogage
            print(f"Clic détecté à la position: ({event.x}, {event.y})")

            # Convertir les coordonnées de l'événement en coordonnées du canvas
            canvas_x = canvas.canvasx(event.x)
            canvas_y = canvas.canvasy(event.y)
            print(f"Coordonnées du canvas: ({canvas_x}, {canvas_y})")

            # Vérifier si on clique sur une poignée
            items = canvas.find_withtag(tk.CURRENT)
            tags = canvas.gettags(tk.CURRENT) if items else []
            print(f"Éléments sous le curseur: {items}, tags: {tags}")

            if "handle" in str(tags):  # Utiliser str() pour éviter les problèmes de comparaison
                print("Poignée détectée")
                resizing = True
                for handle_id, tag in handles:
                    if handle_id == items[0]:
                        current_handle = tag
                        print(f"Poignée sélectionnée: {tag}")
                        break
            elif rect_id and items and items[0] == rect_id:
                print("Rectangle existant détecté")
                dragging = True
                # Enregistrer le point de départ pour le déplacement
                start_x = canvas_x
                start_y = canvas_y
            else:
                print("Création d'un nouveau rectangle")
                # Créer un nouveau rectangle
                if rect_id:
                    print(f"Suppression du rectangle existant: {rect_id}")
                    canvas.delete(rect_id)
                    for handle_id, _ in handles:
                        canvas.delete(handle_id)
                    handles.clear()

                start_x = canvas_x
                start_y = canvas_y
                current_x = canvas_x
                current_y = canvas_y

                print(f"Création d'un rectangle à: ({start_x}, {start_y}, {current_x}, {current_y})")
                rect_id = canvas.create_rectangle(
                    start_x, start_y, current_x, current_y,
                    outline="red", width=2, tags="rect"
                )
                print(f"Rectangle créé avec ID: {rect_id}")

                # Créer les poignées (elles seront mises à jour dans on_drag)
                handle_positions = [
                    (start_x, start_y, "nw"),
                    (start_x, start_y, "n"),
                    (start_x, start_y, "ne"),
                    (start_x, start_y, "e"),
                    (start_x, start_y, "se"),
                    (start_x, start_y, "s"),
                    (start_x, start_y, "sw"),
                    (start_x, start_y, "w")
                ]

                handles.clear()
                for x, y, tag in handle_positions:
                    handle_id = canvas.create_rectangle(
                        x - handle_size / 2, y - handle_size / 2,
                        x + handle_size / 2, y + handle_size / 2,
                        fill="blue", outline="white", tags=f"handle {tag}"
                    )
                    handles.append((handle_id, tag))
                print(f"Poignées créées: {len(handles)}")

                dragging = True

        def on_drag(event):
            nonlocal current_x, current_y, start_x, start_y, rect_id

            if not rect_id:
                print("Pas de rectangle à déplacer/redimensionner")
                return

            # Convertir les coordonnées de l'événement en coordonnées du canvas
            canvas_x = canvas.canvasx(event.x)
            canvas_y = canvas.canvasy(event.y)
            print(f"Déplacement à: ({canvas_x}, {canvas_y})")

            if resizing and current_handle:
                print(f"Redimensionnement avec la poignée: {current_handle}")
                # Redimensionner le rectangle en fonction de la poignée
                try:
                    x1, y1, x2, y2 = canvas.coords(rect_id)
                    print(f"Coordonnées actuelles du rectangle: ({x1}, {y1}, {x2}, {y2})")

                    if current_handle == "nw":  # Coin supérieur gauche
                        canvas.coords(rect_id, canvas_x, canvas_y, x2, y2)
                    elif current_handle == "n":  # Milieu haut
                        canvas.coords(rect_id, x1, canvas_y, x2, y2)
                    elif current_handle == "ne":  # Coin supérieur droit
                        canvas.coords(rect_id, x1, canvas_y, canvas_x, y2)
                    elif current_handle == "e":  # Milieu droit
                        canvas.coords(rect_id, x1, y1, canvas_x, y2)
                    elif current_handle == "se":  # Coin inférieur droit
                        canvas.coords(rect_id, x1, y1, canvas_x, canvas_y)
                    elif current_handle == "s":  # Milieu bas
                        canvas.coords(rect_id, x1, y1, x2, canvas_y)
                    elif current_handle == "sw":  # Coin inférieur gauche
                        canvas.coords(rect_id, canvas_x, y1, x2, canvas_y)
                    elif current_handle == "w":  # Milieu gauche
                        canvas.coords(rect_id, canvas_x, y1, x2, y2)

                    # Mettre à jour les poignées
                    update_handles()

                    # Mettre à jour le label d'information
                    x1, y1, x2, y2 = canvas.coords(rect_id)
                    left = min(x1, x2)
                    top = min(y1, y2)
                    width = abs(x2 - x1)
                    height = abs(y2 - y1)
                    info_label.config(text=f"Zone: {region_name} | Position: ({int(left)}, {int(top)}) | Taille: {int(width)}x{int(height)}")
                except Exception as e:
                    print(f"Erreur lors du redimensionnement: {e}")

            elif dragging:
                print("Déplacement du rectangle")
                try:
                    if rect_id is None:
                        print("Rectangle inexistant")
                        return

                    if start_x == current_x and start_y == current_y:
                        print("Création d'un nouveau rectangle")
                        # Création d'un nouveau rectangle
                        current_x = canvas_x
                        current_y = canvas_y
                        canvas.coords(rect_id, start_x, start_y, current_x, current_y)
                    else:
                        print("Déplacement du rectangle existant")
                        # Déplacement du rectangle existant
                        x1, y1, x2, y2 = canvas.coords(rect_id)
                        dx = canvas_x - start_x
                        dy = canvas_y - start_y
                        canvas.move(rect_id, dx, dy)
                        start_x = canvas_x
                        start_y = canvas_y

                        # Déplacer aussi les poignées
                        for handle_id, _ in handles:
                            canvas.move(handle_id, dx, dy)

                    # Mettre à jour le label d'information
                    x1, y1, x2, y2 = canvas.coords(rect_id)
                    left = min(x1, x2)
                    top = min(y1, y2)
                    width = abs(x2 - x1)
                    height = abs(y2 - y1)
                    info_label.config(text=f"Zone: {region_name} | Position: ({int(left)}, {int(top)}) | Taille: {int(width)}x{int(height)}")
                except Exception as e:
                    print(f"Erreur lors du déplacement: {e}")

        def on_release(event):
            nonlocal dragging, resizing, current_handle, rect_id

            print(f"Relâchement du clic à la position: ({event.x}, {event.y})")

            if dragging or resizing:
                print("Fin du déplacement ou du redimensionnement")
                try:
                    # Vérifier que le rectangle existe toujours
                    if rect_id is None:
                        print("Rectangle inexistant lors du relâchement")
                        return

                    # Vérifier que les coordonnées sont valides
                    x1, y1, x2, y2 = canvas.coords(rect_id)
                    print(f"Coordonnées finales du rectangle: ({x1}, {y1}, {x2}, {y2})")

                    # S'assurer que le rectangle a une taille minimale
                    if abs(x2 - x1) < 5 or abs(y2 - y1) < 5:
                        print("Rectangle trop petit, annulation")
                        canvas.delete(rect_id)
                        for handle_id, _ in handles:
                            canvas.delete(handle_id)
                        handles.clear()
                        rect_id = None
                        return

                    # Mettre à jour les coordonnées dans l'interface principale
                    print("Mise à jour des coordonnées dans l'interface principale")
                    update_main_interface()

                    # Réinitialiser les flags
                    dragging = False
                    resizing = False
                    current_handle = None
                except Exception as e:
                    print(f"Erreur lors du relâchement: {e}")
                    # Réinitialiser les flags en cas d'erreur
                    dragging = False
                    resizing = False
                    current_handle = None

        # Lier les événements au canvas
        canvas.bind("<ButtonPress-1>", on_press)
        canvas.bind("<B1-Motion>", on_drag)
        canvas.bind("<ButtonRelease-1>", on_release)

        # Ajouter un label d'information
        info_label = ttk.Label(
            controls_frame,
            text=f"Zone: {region_name} | Position: ({start_x}, {start_y}) | Taille: {current_x - start_x}x{current_y - start_y}",
            font=("Arial", 10)
        )
        info_label.pack(side=tk.LEFT, padx=10)

        # Ajouter un bouton pour voir l'aperçu de la zone
        ttk.Button(
            controls_frame,
            text="Voir l'aperçu de la zone",
            command=show_zone_preview
        ).pack(side=tk.LEFT, padx=10)

        # Ajouter un bouton pour appliquer les changements
        ttk.Button(
            controls_frame,
            text="Appliquer",
            command=update_main_interface,
            style="Accent.TButton"
        ).pack(side=tk.LEFT, padx=10)

        # Ajouter un bouton pour fermer la fenêtre
        ttk.Button(
            controls_frame,
            text="Fermer",
            command=preview_window.destroy
        ).pack(side=tk.RIGHT, padx=10)

    # Fonction pour charger une capture d'écran depuis un fichier
    def load_screenshot_from_file():
        nonlocal screenshot

        # Ouvrir une boîte de dialogue pour sélectionner un fichier
        filepath = filedialog.askopenfilename(
            title="Sélectionner une capture d'écran",
            filetypes=[("Images", "*.png;*.jpg;*.jpeg"), ("Tous les fichiers", "*.*")],
            initialdir="screenshots"
        )

        if not filepath:
            return  # L'utilisateur a annulé

        try:
            # Charger l'image
            screenshot = cv2.imread(filepath)

            if screenshot is None:
                messagebox.showerror("Erreur", f"Impossible de charger l'image {filepath}")
                return

            print(f"Capture d'écran chargée depuis {filepath}: {screenshot.shape}")

            # Mettre à jour l'aperçu
            schedule_preview_update()

            # Afficher l'aperçu à l'échelle 1:1
            show_fullsize_preview()

            messagebox.showinfo("Succès", f"Capture d'écran chargée depuis {filepath}")
        except Exception as e:
            print(f"Erreur lors du chargement de la capture d'écran: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du chargement de la capture d'écran: {str(e)}")

    # La fonction save_current_config a été supprimée car elle n'est plus utilisée
    # Les coordonnées sont sauvegardées automatiquement lors du changement de zone ou de catégorie

    # La fonction apply_coordinates a été supprimée car elle n'est plus utilisée
    # Les coordonnées sont sauvegardées automatiquement lors du changement de zone ou de catégorie

    # Section: Capture d'écran
    ttk.Label(left_frame, text="Capture d'écran", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))
    ttk.Button(left_frame, text="Capturer l'écran", command=capture_new_screenshot).pack(fill=tk.X, pady=5)
    ttk.Button(left_frame, text="Sauvegarder la capture", command=lambda: save_screenshot(screenshot) if screenshot is not None else None).pack(fill=tk.X, pady=5)
    ttk.Button(left_frame, text="Charger une capture", command=load_screenshot_from_file).pack(fill=tk.X, pady=5)
    ttk.Button(left_frame, text="Voir à l'échelle 1:1", command=show_fullsize_preview).pack(fill=tk.X, pady=5)

    # Section: Régions
    ttk.Label(left_frame, text="Zones à calibrer", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(20, 5))
    ttk.Label(left_frame, text="Zone actuelle:").pack(anchor=tk.W)

    # Descriptions des zones
    region_descriptions = {
        # Cartes communes individuelles
        "card_1": "Carte commune 1 (flop)",
        "card_2": "Carte commune 2 (flop)",
        "card_3": "Carte commune 3 (flop)",
        "card_4": "Carte commune 4 (turn)",
        "card_5": "Carte commune 5 (river)",

        # Cartes en main individuelles
        "carte_1m": "Votre première carte en main",
        "carte_2m": "Votre deuxième carte en main",
        "pot_total": "Montant du pot total",
        "mes_jetons": "Vos jetons",
        "montant_call": "Montant à suivre (call)",
        "montant_relance": "Montant à relancer",
        "ma_mise": "Votre mise actuelle",
        "mon_allin": "Votre all-in (texte 'All-in')",

        # Jetons des autres joueurs
        "jetons_joueur1": "Jetons du joueur 1 (position 1)",
        "jetons_joueur2": "Jetons du joueur 2 (position 2)",
        "jetons_joueur3": "Jetons du joueur 3 (position 3)",
        "jetons_joueur4": "Jetons du joueur 4 (position 4)",
        "jetons_joueur5": "Jetons du joueur 5 (position 5)",
        "jetons_joueur6": "Jetons du joueur 6 (position 6)",
        "jetons_joueur7": "Jetons du joueur 7 (position 7)",

        # Mises des joueurs
        "mise_joueur1": "Mise du joueur 1",
        "mise_joueur2": "Mise du joueur 2",
        "mise_joueur3": "Mise du joueur 3",
        "mise_joueur4": "Mise du joueur 4",
        "mise_joueur5": "Mise du joueur 5",
        "mise_joueur6": "Mise du joueur 6",
        "mise_joueur7": "Mise du joueur 7",

        # All-in des joueurs
        "allin_joueur1": "All-in du joueur 1 (texte 'All-in')",
        "allin_joueur2": "All-in du joueur 2 (texte 'All-in')",
        "allin_joueur3": "All-in du joueur 3 (texte 'All-in')",
        "allin_joueur4": "All-in du joueur 4 (texte 'All-in')",
        "allin_joueur5": "All-in du joueur 5 (texte 'All-in')",
        "allin_joueur6": "All-in du joueur 6 (texte 'All-in')",
        "allin_joueur7": "All-in du joueur 7 (texte 'All-in')",

        # Boutons des joueurs (dealer button)
        "bouton_joueur1": "Bouton dealer du joueur 1",
        "bouton_joueur2": "Bouton dealer du joueur 2",
        "bouton_joueur3": "Bouton dealer du joueur 3",
        "bouton_joueur4": "Bouton dealer du joueur 4",
        "bouton_joueur5": "Bouton dealer du joueur 5",
        "bouton_joueur6": "Bouton dealer du joueur 6",
        "bouton_joueur7": "Bouton dealer du joueur 7",
        "bouton_moi": "Mon bouton dealer",

        # Pseudos des joueurs
        "pseudo_joueur1": "Pseudo du joueur 1",
        "pseudo_joueur2": "Pseudo du joueur 2",
        "pseudo_joueur3": "Pseudo du joueur 3",
        "pseudo_joueur4": "Pseudo du joueur 4",
        "pseudo_joueur5": "Pseudo du joueur 5",
        "pseudo_joueur6": "Pseudo du joueur 6",
        "pseudo_joueur7": "Pseudo du joueur 7"
    }

    # Organiser les zones par catégories
    zone_categories = {
        "Zones principales": ["card_1", "card_2", "card_3", "card_4", "card_5", "carte_1m", "carte_2m", "pot_total", "mes_jetons", "montant_call", "montant_relance", "ma_mise", "mon_allin"],
        "Jetons des joueurs": [f"jetons_joueur{i}" for i in range(1, 8)],
        "Mises des joueurs": [f"mise_joueur{i}" for i in range(1, 8)],
        "All-in des joueurs": [f"allin_joueur{i}" for i in range(1, 8)],
        "Boutons des joueurs": [f"bouton_joueur{i}" for i in range(1, 8)] + ["bouton_moi"],
        "Pseudos des joueurs": [f"pseudo_joueur{i}" for i in range(1, 8)]
    }

    # Variable pour la catégorie sélectionnée
    current_category = tk.StringVar(value="Zones principales")

    # Combobox pour sélectionner la catégorie
    ttk.Label(left_frame, text="Catégorie:").pack(anchor=tk.W)
    category_combo = ttk.Combobox(left_frame, textvariable=current_category, values=list(zone_categories.keys()))
    category_combo.pack(fill=tk.X, pady=5)

    # Fonction pour mettre à jour la liste des zones en fonction de la catégorie
    def update_region_list(event=None):
        print("Mise à jour de la liste des zones...")
        # Sauvegarder les coordonnées de la zone actuelle avant de changer de catégorie
        current_zone = current_region.get()
        if current_zone and current_zone in config['roi']:
            print(f"Sauvegarde des coordonnées de la zone actuelle avant changement de catégorie: {current_zone}")
            try:
                # Récupérer les valeurs des entrées
                coords_changed = False
                for coord, entry in coord_entries.items():
                    value = entry.get().strip()
                    if value:  # Ignorer les champs vides
                        try:
                            int_value = int(value)
                            # Vérifier si la valeur a changé
                            if config['roi'][current_zone][coord] != int_value:
                                config['roi'][current_zone][coord] = int_value
                                coords_changed = True
                                print(f"Coordonnée {coord} de {current_zone} mise à jour: {int_value}")
                        except ValueError:
                            print(f"Valeur invalide pour {coord}: {value}")

                # Sauvegarder la configuration seulement si des coordonnées ont changé
                if coords_changed:
                    save_config(config)
                    print(f"Configuration sauvegardée pour la zone: {current_zone}")
                else:
                    print(f"Aucune modification des coordonnées pour la zone: {current_zone}")
            except ValueError:
                # Ignorer les erreurs de conversion
                print(f"Erreur lors de la sauvegarde des coordonnées de la zone: {current_zone}")

        category = current_category.get()
        if category in zone_categories:
            # Filtrer les zones par catégorie
            filtered_keys = zone_categories[category]
            # Créer la liste des valeurs avec descriptions
            filtered_values = [f"{key} - {region_descriptions.get(key, key)}" for key in filtered_keys if key in config['roi']]
            # Mettre à jour le combobox
            region_combo['values'] = filtered_values
            if filtered_values:
                region_combo.current(0)
                # Extraire la clé et mettre à jour la région actuelle
                key = filtered_values[0].split(' - ')[0]
                print(f"Changement de catégorie - Nouvelle zone sélectionnée: {key}")
                current_region.set(key)

                # Forcer la mise à jour des entrées de coordonnées immédiatement
                if key in config['roi']:
                    region = config['roi'][key]
                    print(f"Mise à jour immédiate des coordonnées pour la nouvelle zone: {key}")
                    print(f"Coordonnées à appliquer: {region}")

                    # Mettre à jour les entrées
                    for coord in coord_entries:
                        if coord in region:
                            # Effacer et réinsérer la valeur pour s'assurer qu'elle est bien mise à jour
                            coord_entries[coord].delete(0, tk.END)
                            coord_entries[coord].insert(0, str(region[coord]))

                    # Forcer le rafraîchissement de l'interface
                    root.update_idletasks()

                # Vérifier si une capture d'écran est disponible
                if screenshot is None or screenshot.size == 0:
                    print("Aucune capture d'écran disponible lors du changement de catégorie, capture d'un nouvel écran...")
                    capture_new_screenshot()
                else:
                    # Forcer la mise à jour de l'aperçu immédiatement, sans délai
                    print("Forçage immédiat de la mise à jour de l'aperçu après changement de catégorie")
                    schedule_preview_update()

                    # Puis programmer une autre mise à jour après un délai pour s'assurer que tout est bien mis à jour
                    root.after(300, update_preview)

                    # Mettre à jour la liste des zones sources
                    update_source_zones()

    # Lier la fonction au changement de catégorie
    category_combo.bind("<<ComboboxSelected>>", update_region_list)

    # Initialiser la liste des zones avec la catégorie par défaut
    root.after(100, update_region_list)

    # Combobox pour sélectionner la zone
    ttk.Label(left_frame, text="Zone actuelle:").pack(anchor=tk.W)
    region_combo = ttk.Combobox(left_frame, values=[])
    region_combo.pack(fill=tk.X, pady=5)

    # Fonction pour extraire la clé de la valeur sélectionnée
    def on_region_selected(event):
        selected = region_combo.get()
        if selected:
            # Sauvegarder les coordonnées de la zone actuelle avant de changer
            current_zone = current_region.get()
            if current_zone and current_zone in config['roi']:
                print(f"Sauvegarde des coordonnées de la zone actuelle: {current_zone}")
                try:
                    # Récupérer les valeurs des entrées
                    for coord, entry in coord_entries.items():
                        value = entry.get().strip()
                        if value:  # Ignorer les champs vides
                            config['roi'][current_zone][coord] = int(value)

                    # Sauvegarder la configuration
                    save_config(config)
                except ValueError:
                    # Ignorer les erreurs de conversion
                    print(f"Erreur lors de la sauvegarde des coordonnées de la zone: {current_zone}")

            # Extraire la clé (première partie avant le tiret)
            key = selected.split(' - ')[0]
            print(f"Zone sélectionnée via combobox: {key}")

            # Mettre à jour la variable
            current_region.set(key)

            # Forcer la mise à jour des entrées de coordonnées immédiatement
            if key in config['roi']:
                region = config['roi'][key]
                print(f"Mise à jour immédiate des coordonnées pour la nouvelle zone: {key}")
                print(f"Coordonnées à appliquer: {region}")

                # Mettre à jour les entrées
                for coord in coord_entries:
                    if coord in region:
                        # Effacer et réinsérer la valeur pour s'assurer qu'elle est bien mise à jour
                        coord_entries[coord].delete(0, tk.END)
                        coord_entries[coord].insert(0, str(region[coord]))

                # Forcer le rafraîchissement de l'interface
                root.update_idletasks()

            # Vérifier si une capture d'écran est disponible
            if screenshot is None or screenshot.size == 0:
                print("Aucune capture d'écran disponible, capture d'un nouvel écran...")
                capture_new_screenshot()
            else:
                # Forcer la mise à jour de l'aperçu immédiatement, sans délai
                print("Forçage immédiat de la mise à jour de l'aperçu après changement de zone")
                update_preview()

                # Puis programmer une autre mise à jour après un délai pour s'assurer que tout est bien mis à jour
                root.after(300, update_preview)

                # Mettre à jour la liste des zones sources
                update_source_zones()

    region_combo.bind("<<ComboboxSelected>>", on_region_selected)

    # Section: Coordonnées
    ttk.Label(left_frame, text="Coordonnées", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(20, 5))

    # Frame pour les coordonnées
    coords_frame = ttk.Frame(left_frame)
    coords_frame.pack(fill=tk.X, pady=5)

    # Créer les entrées pour les coordonnées
    coord_entries = {}
    coord_labels = {
        "left": "Position gauche",
        "top": "Position haut",
        "width": "Largeur",
        "height": "Hauteur"
    }

    for i, (coord, label) in enumerate(coord_labels.items()):
        ttk.Label(coords_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
        entry = ttk.Entry(coords_frame)
        entry.grid(row=i, column=1, sticky=tk.EW, pady=2)
        coord_entries[coord] = entry

    # Fonction pour appliquer les coordonnées
    def apply_coordinates():
        # Récupérer la zone actuelle
        current_zone = current_region.get()
        if current_zone and current_zone in config['roi']:
            print(f"Application des coordonnées pour la zone: {current_zone}")
            try:
                # Récupérer les valeurs des entrées
                coords_changed = False
                for coord, entry in coord_entries.items():
                    value = entry.get().strip()
                    if value:  # Ignorer les champs vides
                        try:
                            int_value = int(value)
                            # Vérifier si la valeur a changé
                            if config['roi'][current_zone][coord] != int_value:
                                config['roi'][current_zone][coord] = int_value
                                coords_changed = True
                                print(f"Coordonnée {coord} de {current_zone} mise à jour: {int_value}")
                        except ValueError:
                            print(f"Valeur invalide pour {coord}: {value}")

                # Mettre à jour l'aperçu
                schedule_preview_update()

                # Sauvegarder la configuration si des coordonnées ont changé
                if coords_changed:
                    save_config(config)
                    print(f"Configuration sauvegardée pour la zone: {current_zone}")
            except Exception as e:
                print(f"Erreur lors de l'application des coordonnées: {e}")
        else:
            print(f"Aucune zone sélectionnée ou zone invalide: {current_zone}")

    # Ajouter un bouton pour appliquer les coordonnées
    ttk.Button(coords_frame, text="Appliquer les coordonnées", command=apply_coordinates).grid(row=len(coord_labels), column=0, columnspan=2, pady=5)

    # Fonction pour sauvegarder la configuration actuelle
    def save_current_config():
        # Récupérer la zone actuelle
        current_zone = current_region.get()
        if current_zone and current_zone in config['roi']:
            print(f"Sauvegarde manuelle des coordonnées de la zone: {current_zone}")
            try:
                # Récupérer les valeurs des entrées
                for coord, entry in coord_entries.items():
                    value = entry.get().strip()
                    if value:  # Ignorer les champs vides
                        try:
                            int_value = int(value)
                            # Mettre à jour la valeur
                            config['roi'][current_zone][coord] = int_value
                            print(f"Coordonnée {coord} de {current_zone} mise à jour: {int_value}")
                        except ValueError:
                            print(f"Valeur invalide pour {coord}: {value}")

                # Sauvegarder la configuration
                save_config(config)
                print(f"Configuration sauvegardée manuellement pour la zone: {current_zone}")
                messagebox.showinfo("Succès", "Configuration sauvegardée avec succès")
            except Exception as e:
                print(f"Erreur lors de la sauvegarde manuelle: {e}")
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")
        else:
            print(f"Aucune zone sélectionnée ou zone invalide: {current_zone}")
            messagebox.showwarning("Attention", "Aucune zone sélectionnée ou zone invalide")

    # Ajouter un bouton de sauvegarde explicite
    ttk.Button(left_frame, text="Sauvegarder la configuration", command=save_current_config).pack(fill=tk.X, pady=5)

    # Section: Zoom
    ttk.Label(left_frame, text="Zoom de l'aperçu", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(20, 5))

    # Frame pour le zoom
    zoom_frame = ttk.Frame(left_frame)
    zoom_frame.pack(fill=tk.X, pady=5)

    # Fonction pour mettre à jour le zoom
    def update_zoom_value(val):
        # Convertir la valeur du slider (0-100) en facteur de zoom (0.5-5.0)
        zoom_value = 0.5 + (float(val) / 100.0) * 4.5
        zoom_factor.set(zoom_value)
        zoom_label.config(text=f"Niveau de zoom: {zoom_value:.1f}x")
        update_preview()

    # Étiquette pour afficher la valeur actuelle du zoom
    zoom_label = ttk.Label(zoom_frame, text=f"Niveau de zoom: {zoom_factor.get():.1f}x")
    zoom_label.pack(anchor=tk.W, pady=(0, 5))

    # Slider pour ajuster le zoom
    zoom_slider = ttk.Scale(zoom_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                           command=update_zoom_value)
    zoom_slider.set((zoom_factor.get() - 0.5) * 100.0 / 4.5)  # Convertir le facteur de zoom en valeur de slider
    zoom_slider.pack(fill=tk.X)

    # Boutons de zoom rapide
    zoom_buttons_frame = ttk.Frame(zoom_frame)
    zoom_buttons_frame.pack(fill=tk.X, pady=(5, 0))

    # Fonction pour les boutons de zoom rapide
    def set_zoom(value):
        zoom_slider.set((value - 0.5) * 100.0 / 4.5)
        update_zoom_value(zoom_slider.get())

    ttk.Button(zoom_buttons_frame, text="0.5x", command=lambda: set_zoom(0.5)).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=2)
    ttk.Button(zoom_buttons_frame, text="1x", command=lambda: set_zoom(1.0)).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=2)
    ttk.Button(zoom_buttons_frame, text="2x", command=lambda: set_zoom(2.0)).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=2)
    ttk.Button(zoom_buttons_frame, text="3x", command=lambda: set_zoom(3.0)).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=2)
    ttk.Button(zoom_buttons_frame, text="5x", command=lambda: set_zoom(5.0)).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=2)

    # Le bouton "Sauvegarder la configuration" a été supprimé car redondant
    # Les coordonnées sont sauvegardées automatiquement lors du changement de zone ou de catégorie

    # L'initialisation est maintenant gérée par update_region_list via root.after(100, update_region_list)

    # Lancer la boucle principale
    root.mainloop()

if __name__ == "__main__":
    main()
