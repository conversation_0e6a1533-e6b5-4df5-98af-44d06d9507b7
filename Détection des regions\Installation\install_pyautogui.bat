@echo off
echo ===================================================
echo Installation de pyautogui pour la capture d'ecran
echo ===================================================
echo.

echo Installation de pyautogui...
pip install pyautogui

if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors de l'installation de pyautogui.
    echo Tentative avec une version specifique...
    pip install pyautogui==0.9.53
)

echo.
echo Test de pyautogui...
python -c "import pyautogui; print(f'pyautogui installe avec succes! Version: {pyautogui.__version__}')"

if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors du test de pyautogui.
    echo Veuillez verifier l'installation manuellement.
) else (
    echo.
    echo Installation reussie!
)

echo.
pause
