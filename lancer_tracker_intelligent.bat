@echo off
echo 🎯 LANCEMENT DU TRACKER POKER INTELLIGENT
echo ==========================================
echo.

cd /d "C:\Users\<USER>\PokerAdvisor\Détection des regions"

echo 🔍 Vérification des fichiers...
if not exist "poker_tracker_intelligent.py" (
    echo ❌ Fichier poker_tracker_intelligent.py introuvable
    pause
    exit /b 1
)

if not exist "lancer_conseiller_avec_tracker.py" (
    echo ❌ Fichier lancer_conseiller_avec_tracker.py introuvable
    pause
    exit /b 1
)

echo ✅ Fichiers trouvés
echo.

echo 🚀 Lancement de l'interface graphique...
python "lancer_conseiller_avec_tracker.py"

echo.
echo 📝 Interface fermée
pause
