@echo off
echo ===================================================
echo TEST OPTIMISATION RTX 3060 Ti 6GB + 32GB RAM
echo ===================================================
echo.

echo 1. Test Python basique...
python -c "print('Python OK')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python ne fonctionne pas
    pause
    exit /b 1
)

echo.
echo 2. Test import détecteur...
python -c "from detector import Detector; print('Import OK')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème import détecteur
    pause
    exit /b 1
)

echo.
echo 3. Test création détecteur...
python -c "from detector import Detector; d = Detector(use_cuda=True); print('Détecteur créé')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème création détecteur
    pause
    exit /b 1
)

echo.
echo 4. Test détection simple...
python -c "from detector import Detector; import numpy as np; import cv2; d = Detector(use_cuda=True); img = np.ones((100,200,3), dtype=np.uint8)*255; cv2.putText(img, 'A', (50,70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0,0,0), 3); result = d.detect_text_fast(img); print('Résultat:', result)"

echo.
echo ===================================================
echo ✅ OPTIMISATION TESTÉE
echo ===================================================
echo Si tous les tests passent, l'optimisation fonctionne !
echo Votre application devrait maintenant être beaucoup plus rapide.
echo ===================================================
echo.

pause
