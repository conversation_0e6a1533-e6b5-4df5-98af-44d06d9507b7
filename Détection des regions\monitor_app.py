#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de surveillance en temps réel pour l'application Poker Advisor.
Surveille l'utilisation de la mémoire, les erreurs et la stabilité.
"""

import os
import sys
import time
import psutil
import threading
from datetime import datetime

class AppMonitor:
    """Moniteur de l'application pour détecter les problèmes de stabilité"""

    def __init__(self, process_name="python", log_file="monitor.log"):
        self.process_name = process_name
        self.log_file = log_file
        self.monitoring = False
        self.monitor_thread = None
        self.stats_history = []
        self.max_history = 100  # Garder les 100 dernières mesures

        # Seuils d'alerte (ajustés pour un système avec 32GB RAM)
        self.memory_threshold_mb = 20000  # 20 GB (pour un système 32GB)
        self.gpu_threshold_mb = 5000      # 5 GB (pour RTX 3060 Ti 8GB)
        self.cpu_threshold_percent = 80   # 80%

    def log_message(self, message):
        """Enregistre un message avec timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)

        # Écrire dans le fichier de log
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except Exception as e:
            print(f"Erreur lors de l'écriture du log: {e}")

    def get_gpu_memory(self):
        """Obtient l'utilisation de la mémoire GPU"""
        try:
            import torch
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated(0) / 1024 / 1024  # MB
                cached = torch.cuda.memory_reserved(0) / 1024 / 1024  # MB
                return {'allocated': allocated, 'cached': cached, 'total': allocated + cached}
        except ImportError:
            pass
        return {'allocated': 0, 'cached': 0, 'total': 0}

    def find_poker_processes(self):
        """Trouve les processus liés à l'application poker"""
        poker_processes = []

        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # Chercher les processus Python qui exécutent des scripts poker
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline:
                        cmdline_str = ' '.join(cmdline).lower()
                        # Mots-clés plus spécifiques pour l'application poker
                        poker_keywords = [
                            'detector', 'poker', 'advisor', 'calibration',
                            'detector_gui', 'poker_advisor', 'monitor_app',
                            'learning_system', 'multi_ocr'
                        ]
                        if any(keyword in cmdline_str for keyword in poker_keywords):
                            poker_processes.append(proc)
                            # Log pour debug
                            print(f"🔍 Processus poker détecté: PID {proc.info['pid']}, CMD: {' '.join(cmdline[:3])}...")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return poker_processes

    def get_system_stats(self):
        """Obtient les statistiques système"""
        # Statistiques générales du système
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        # Statistiques GPU
        gpu_stats = self.get_gpu_memory()

        # Statistiques des processus poker
        poker_processes = self.find_poker_processes()
        total_poker_memory = 0
        total_poker_cpu = 0

        for proc in poker_processes:
            try:
                proc_info = proc.as_dict(['memory_info', 'cpu_percent'])
                total_poker_memory += proc_info['memory_info'].rss / 1024 / 1024  # MB
                total_poker_cpu += proc_info['cpu_percent'] or 0
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return {
            'timestamp': datetime.now(),
            'system_cpu': cpu_percent,
            'system_memory_mb': memory.used / 1024 / 1024,
            'system_memory_percent': memory.percent,
            'poker_processes_count': len(poker_processes),
            'poker_memory_mb': total_poker_memory,
            'poker_cpu_percent': total_poker_cpu,
            'gpu_memory_mb': gpu_stats['total'],
            'gpu_allocated_mb': gpu_stats['allocated'],
            'gpu_cached_mb': gpu_stats['cached']
        }

    def check_alerts(self, stats):
        """Vérifie les seuils d'alerte"""
        alerts = []

        # Alerte mémoire système
        if stats['system_memory_mb'] > self.memory_threshold_mb:
            alerts.append(f"🚨 ALERTE: Mémoire système élevée: {stats['system_memory_mb']:.1f} MB")

        # Alerte CPU système
        if stats['system_cpu'] > self.cpu_threshold_percent:
            alerts.append(f"🚨 ALERTE: CPU système élevé: {stats['system_cpu']:.1f}%")

        # Alerte mémoire GPU
        if stats['gpu_memory_mb'] > self.gpu_threshold_mb:
            alerts.append(f"🚨 ALERTE: Mémoire GPU élevée: {stats['gpu_memory_mb']:.1f} MB")

        # Alerte processus poker
        if stats['poker_memory_mb'] > 1000:  # Plus de 1 GB pour les processus poker
            alerts.append(f"🚨 ALERTE: Processus poker utilisent {stats['poker_memory_mb']:.1f} MB")

        # Détecter les fuites mémoire
        if len(self.stats_history) > 10:
            # Comparer avec il y a 10 mesures
            old_stats = self.stats_history[-10]
            memory_growth = stats['poker_memory_mb'] - old_stats['poker_memory_mb']
            gpu_growth = stats['gpu_memory_mb'] - old_stats['gpu_memory_mb']

            if memory_growth > 100:  # Plus de 100 MB de croissance
                alerts.append(f"⚠️ FUITE MÉMOIRE: +{memory_growth:.1f} MB en 10 mesures")

            if gpu_growth > 50:  # Plus de 50 MB de croissance GPU
                alerts.append(f"⚠️ FUITE GPU: +{gpu_growth:.1f} MB en 10 mesures")

        return alerts

    def monitor_loop(self):
        """Boucle principale de surveillance"""
        self.log_message("🚀 Démarrage de la surveillance")

        while self.monitoring:
            try:
                # Obtenir les statistiques
                stats = self.get_system_stats()

                # Ajouter à l'historique
                self.stats_history.append(stats)
                if len(self.stats_history) > self.max_history:
                    self.stats_history.pop(0)

                # Vérifier les alertes
                alerts = self.check_alerts(stats)

                # Afficher les statistiques
                self.log_message(
                    f"📊 CPU: {stats['system_cpu']:.1f}% | "
                    f"RAM: {stats['system_memory_mb']:.0f}MB ({stats['system_memory_percent']:.1f}%) | "
                    f"GPU: {stats['gpu_memory_mb']:.0f}MB | "
                    f"Poker: {stats['poker_processes_count']} proc, {stats['poker_memory_mb']:.0f}MB"
                )

                # Afficher les alertes
                for alert in alerts:
                    self.log_message(alert)

                # Attendre avant la prochaine mesure
                time.sleep(5)  # Mesure toutes les 5 secondes

            except Exception as e:
                self.log_message(f"❌ Erreur dans la boucle de surveillance: {e}")
                time.sleep(10)  # Attendre plus longtemps en cas d'erreur

    def start_monitoring(self):
        """Démarre la surveillance"""
        if self.monitoring:
            self.log_message("⚠️ La surveillance est déjà en cours")
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.log_message("✅ Surveillance démarrée")

    def stop_monitoring(self):
        """Arrête la surveillance"""
        if not self.monitoring:
            self.log_message("⚠️ La surveillance n'est pas en cours")
            return

        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        self.log_message("🛑 Surveillance arrêtée")

    def get_summary(self):
        """Obtient un résumé des statistiques"""
        if not self.stats_history:
            return "Aucune donnée disponible"

        recent_stats = self.stats_history[-10:] if len(self.stats_history) >= 10 else self.stats_history

        avg_cpu = sum(s['system_cpu'] for s in recent_stats) / len(recent_stats)
        avg_memory = sum(s['poker_memory_mb'] for s in recent_stats) / len(recent_stats)
        avg_gpu = sum(s['gpu_memory_mb'] for s in recent_stats) / len(recent_stats)

        return (
            f"📈 RÉSUMÉ (dernières {len(recent_stats)} mesures):\n"
            f"   CPU moyen: {avg_cpu:.1f}%\n"
            f"   Mémoire poker moyenne: {avg_memory:.1f} MB\n"
            f"   Mémoire GPU moyenne: {avg_gpu:.1f} MB"
        )

def main():
    """Fonction principale"""
    print("🔍 Moniteur de l'application Poker Advisor")
    print("=" * 50)

    monitor = AppMonitor()

    try:
        monitor.start_monitoring()

        print("Surveillance en cours... Appuyez sur Ctrl+C pour arrêter")
        print("Commandes disponibles:")
        print("  's' + Entrée: Afficher le résumé")
        print("  'q' + Entrée: Quitter")

        while True:
            try:
                user_input = input().strip().lower()
                if user_input == 'q':
                    break
                elif user_input == 's':
                    print(monitor.get_summary())
            except EOFError:
                # Pas d'entrée disponible, continuer
                time.sleep(1)

    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")

    finally:
        monitor.stop_monitoring()
        print("👋 Surveillance terminée")

if __name__ == "__main__":
    main()
