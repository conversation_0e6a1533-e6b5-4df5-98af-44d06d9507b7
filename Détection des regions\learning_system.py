#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Système d'apprentissage en temps réel pour la détection de cartes
================================================================

Ce module collecte et analyse les corrections manuelles pour améliorer
automatiquement la précision de détection des cartes au fil du temps.

Fonctionnalités :
1. Collecte des corrections manuelles avec contexte
2. Analyse des patterns d'erreurs
3. Création de règles de correction adaptatives
4. Statistiques de précision en temps réel
5. Amélioration continue des seuils de détection

Auteur: Augment Agent
Date: 2025-01-25
"""

import os
import json
import time
import cv2
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter
import hashlib

class LearningSystem:
    """Système d'apprentissage en temps réel pour la détection de cartes"""
    
    def __init__(self, data_dir="learning_data"):
        """
        Initialise le système d'apprentissage
        
        Args:
            data_dir (str): Répertoire pour stocker les données d'apprentissage
        """
        self.data_dir = data_dir
        self.corrections_file = os.path.join(data_dir, "corrections.json")
        self.stats_file = os.path.join(data_dir, "statistics.json")
        self.rules_file = os.path.join(data_dir, "adaptive_rules.json")
        
        # Créer le répertoire s'il n'existe pas
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(os.path.join(data_dir, "images"), exist_ok=True)
        
        # Charger les données existantes
        self.corrections_history = self.load_corrections()
        self.statistics = self.load_statistics()
        self.adaptive_rules = self.load_adaptive_rules()
        
        # Compteurs pour les sessions
        self.session_corrections = 0
        self.session_detections = 0
        self.session_start = time.time()
        
        print(f"✅ Système d'apprentissage initialisé")
        print(f"📊 Corrections historiques: {len(self.corrections_history)}")
        print(f"📈 Règles adaptatives: {len(self.adaptive_rules)}")
    
    def record_correction(self, region_name, image_crop, detected_value, corrected_value, 
                         detected_confidence=0.0, context_info=None):
        """
        Enregistre une correction manuelle pour l'apprentissage
        
        Args:
            region_name (str): Nom de la région (ex: "card_1", "hand_card_1")
            image_crop (numpy.ndarray): Image de la région corrigée
            detected_value (str): Valeur détectée automatiquement
            corrected_value (str): Valeur corrigée manuellement ("" pour "pas de cartes")
            detected_confidence (float): Confiance de la détection automatique
            context_info (dict): Informations contextuelles supplémentaires
        """
        try:
            # Créer un identifiant unique pour cette correction
            timestamp = datetime.now().isoformat()
            correction_id = hashlib.md5(f"{region_name}_{timestamp}".encode()).hexdigest()[:8]
            
            # Sauvegarder l'image de la région
            image_filename = f"correction_{correction_id}_{region_name}.jpg"
            image_path = os.path.join(self.data_dir, "images", image_filename)
            cv2.imwrite(image_path, image_crop)
            
            # Créer l'enregistrement de correction
            correction_record = {
                "id": correction_id,
                "timestamp": timestamp,
                "region_name": region_name,
                "detected_value": detected_value,
                "corrected_value": corrected_value,
                "detected_confidence": detected_confidence,
                "image_path": image_path,
                "image_shape": image_crop.shape if image_crop is not None else None,
                "context": context_info or {},
                "correction_type": self._classify_correction_type(detected_value, corrected_value)
            }
            
            # Ajouter à l'historique
            self.corrections_history.append(correction_record)
            self.session_corrections += 1
            
            # Sauvegarder immédiatement
            self.save_corrections()
            
            # Mettre à jour les statistiques
            self._update_statistics(correction_record)
            
            # Mettre à jour les règles adaptatives
            self._update_adaptive_rules(correction_record)
            
            print(f"📝 Correction enregistrée: {region_name} '{detected_value}' → '{corrected_value}'")
            
            return correction_id
            
        except Exception as e:
            print(f"❌ Erreur lors de l'enregistrement de la correction: {e}")
            return None
    
    def _classify_correction_type(self, detected, corrected):
        """Classifie le type de correction"""
        if not detected and corrected:
            return "false_negative"  # Carte manquée
        elif detected and not corrected:
            return "false_positive"  # Fausse détection
        elif detected and corrected and detected != corrected:
            return "misidentification"  # Mauvaise identification
        else:
            return "unknown"
    
    def _update_statistics(self, correction_record):
        """Met à jour les statistiques globales"""
        region = correction_record["region_name"]
        detected = correction_record["detected_value"]
        corrected = correction_record["corrected_value"]
        correction_type = correction_record["correction_type"]
        
        # Initialiser les stats pour cette région si nécessaire
        if region not in self.statistics:
            self.statistics[region] = {
                "total_corrections": 0,
                "correction_types": defaultdict(int),
                "common_errors": defaultdict(int),
                "accuracy_trend": []
            }
        
        # Mettre à jour les compteurs
        self.statistics[region]["total_corrections"] += 1
        self.statistics[region]["correction_types"][correction_type] += 1
        
        # Enregistrer les erreurs communes
        if detected and corrected and detected != corrected:
            error_pattern = f"{detected}→{corrected}"
            self.statistics[region]["common_errors"][error_pattern] += 1
        
        # Sauvegarder les statistiques
        self.save_statistics()
    
    def _update_adaptive_rules(self, correction_record):
        """Met à jour les règles adaptatives basées sur les corrections"""
        detected = correction_record["detected_value"]
        corrected = correction_record["corrected_value"]
        confidence = correction_record["detected_confidence"]
        region = correction_record["region_name"]
        
        # Créer des règles pour les erreurs communes
        if detected and corrected and detected != corrected:
            rule_key = f"{detected}→{corrected}"
            
            if rule_key not in self.adaptive_rules:
                self.adaptive_rules[rule_key] = {
                    "pattern": detected,
                    "correction": corrected,
                    "occurrences": 0,
                    "confidence_threshold": 1.0,
                    "regions": set(),
                    "created": datetime.now().isoformat()
                }
            
            # Mettre à jour la règle
            rule = self.adaptive_rules[rule_key]
            rule["occurrences"] += 1
            rule["regions"].add(region)
            rule["confidence_threshold"] = min(rule["confidence_threshold"], confidence + 0.1)
            rule["last_updated"] = datetime.now().isoformat()
            
            # Convertir le set en liste pour la sérialisation JSON
            rule["regions"] = list(rule["regions"])
        
        # Sauvegarder les règles
        self.save_adaptive_rules()
    
    def get_suggested_correction(self, detected_value, confidence, region_name):
        """
        Suggère une correction basée sur l'apprentissage
        
        Args:
            detected_value (str): Valeur détectée
            confidence (float): Confiance de la détection
            region_name (str): Nom de la région
            
        Returns:
            str or None: Correction suggérée, ou None si aucune suggestion
        """
        # Chercher des règles applicables
        for rule_key, rule in self.adaptive_rules.items():
            if (rule["pattern"] == detected_value and 
                confidence < rule["confidence_threshold"] and
                (region_name in rule["regions"] or rule["occurrences"] >= 3)):
                
                return rule["correction"]
        
        return None
    
    def get_learning_statistics(self):
        """Retourne les statistiques d'apprentissage"""
        total_corrections = len(self.corrections_history)
        session_time = time.time() - self.session_start
        
        # Calculer les erreurs les plus communes
        all_errors = defaultdict(int)
        for region_stats in self.statistics.values():
            for error, count in region_stats["common_errors"].items():
                all_errors[error] += count
        
        most_common_errors = dict(Counter(all_errors).most_common(5))
        
        return {
            "total_corrections": total_corrections,
            "session_corrections": self.session_corrections,
            "session_detections": self.session_detections,
            "session_time_minutes": session_time / 60,
            "adaptive_rules_count": len(self.adaptive_rules),
            "most_common_errors": most_common_errors,
            "regions_with_corrections": list(self.statistics.keys())
        }
    
    def load_corrections(self):
        """Charge l'historique des corrections"""
        try:
            if os.path.exists(self.corrections_file):
                with open(self.corrections_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ Erreur lors du chargement des corrections: {e}")
        return []
    
    def save_corrections(self):
        """Sauvegarde l'historique des corrections"""
        try:
            with open(self.corrections_file, 'w', encoding='utf-8') as f:
                json.dump(self.corrections_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde des corrections: {e}")
    
    def load_statistics(self):
        """Charge les statistiques"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Convertir les defaultdict
                    for region in data.values():
                        if "correction_types" in region:
                            region["correction_types"] = defaultdict(int, region["correction_types"])
                        if "common_errors" in region:
                            region["common_errors"] = defaultdict(int, region["common_errors"])
                    return data
        except Exception as e:
            print(f"⚠️ Erreur lors du chargement des statistiques: {e}")
        return {}
    
    def save_statistics(self):
        """Sauvegarde les statistiques"""
        try:
            # Convertir les defaultdict en dict pour la sérialisation
            stats_to_save = {}
            for region, data in self.statistics.items():
                stats_to_save[region] = {
                    "total_corrections": data["total_corrections"],
                    "correction_types": dict(data["correction_types"]),
                    "common_errors": dict(data["common_errors"]),
                    "accuracy_trend": data.get("accuracy_trend", [])
                }
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_to_save, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde des statistiques: {e}")
    
    def load_adaptive_rules(self):
        """Charge les règles adaptatives"""
        try:
            if os.path.exists(self.rules_file):
                with open(self.rules_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ Erreur lors du chargement des règles: {e}")
        return {}
    
    def save_adaptive_rules(self):
        """Sauvegarde les règles adaptatives"""
        try:
            with open(self.rules_file, 'w', encoding='utf-8') as f:
                json.dump(self.adaptive_rules, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde des règles: {e}")
