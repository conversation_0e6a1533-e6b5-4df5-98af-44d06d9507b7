#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier le bon fonctionnement du système de surveillance.
"""

import os
import sys
import time
from monitor_app import AppMonitor

def test_surveillance():
    """Test du système de surveillance"""
    print("🔍 Test du système de surveillance")
    print("=" * 50)
    
    # Créer une instance du moniteur
    monitor = AppMonitor()
    
    try:
        # Démarrer la surveillance
        print("🚀 Démarrage de la surveillance...")
        monitor.start_monitoring()
        
        # Attendre quelques secondes pour collecter des données
        print("⏳ Collecte de données pendant 15 secondes...")
        time.sleep(15)
        
        # Afficher un résumé
        print("\n📊 Résumé des statistiques:")
        print(monitor.get_summary())
        
        # Afficher les dernières statistiques
        if monitor.stats_history:
            latest = monitor.stats_history[-1]
            print(f"\n📈 Dernières statistiques:")
            print(f"   CPU: {latest['system_cpu']:.1f}%")
            print(f"   RAM: {latest['system_memory_mb']:.0f}MB ({latest['system_memory_percent']:.1f}%)")
            print(f"   GPU: {latest['gpu_memory_mb']:.0f}MB")
            print(f"   Processus poker: {latest['poker_processes_count']} ({latest['poker_memory_mb']:.0f}MB)")
            
            # Vérifier les alertes
            alerts = monitor.check_alerts(latest)
            if alerts:
                print(f"\n🚨 Alertes actives:")
                for alert in alerts:
                    print(f"   {alert}")
            else:
                print(f"\n✅ Aucune alerte")
        
        print(f"\n✅ Test terminé avec succès")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrompu par l'utilisateur")
    
    except Exception as e:
        print(f"\n❌ Erreur pendant le test: {e}")
    
    finally:
        # Arrêter la surveillance
        print(f"\n🛑 Arrêt de la surveillance...")
        monitor.stop_monitoring()
        print(f"👋 Test terminé")

if __name__ == "__main__":
    test_surveillance()
