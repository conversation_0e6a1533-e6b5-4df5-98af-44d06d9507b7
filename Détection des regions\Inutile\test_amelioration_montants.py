#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'amélioration des montants
==================================

Ce script teste que la nouvelle fonction process_amount_text
améliore la détection de "10,8 BB" au lieu de "8".

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
from detector import Detector

def test_amelioration_montants():
    """Teste l'amélioration de la détection de montants"""
    print("🧪 TEST DE L'AMÉLIORATION DES MONTANTS")
    print("=" * 50)

    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))

    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

    # Cas de test pour process_amount_text
    test_cases = [
        {
            "input": ["28,5 BB"],
            "expected": "28,5",
            "description": "Cas problématique: 28,5 BB"
        },
        {
            "input": ["28,5"],
            "expected": "28,5",
            "description": "Cas problématique: 28,5 sans unité"
        },
        {
            "input": ["8"],
            "expected": "8",
            "description": "OCR partiel: seulement 8"
        },
        {
            "input": ["10,8 BB"],
            "expected": "10,8",
            "description": "Montant avec unité"
        },
        {
            "input": ["42.5 BB"],
            "expected": "42.5",
            "description": "Montant avec point"
        },
        {
            "input": ["5"],
            "expected": "5",
            "description": "Partie décimale seule"
        }
    ]

    print("\n🔍 TESTS DE LA FONCTION process_amount_text:")
    print("-" * 40)

    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['description']}")
        print(f"   Entrée: {test_case['input']}")
        print(f"   Attendu: '{test_case['expected']}'")

        try:
            result = detector.process_amount_text(test_case['input'])
            print(f"   Résultat: '{result}'")

            if result == test_case['expected']:
                print(f"   ✅ SUCCÈS")
                success_count += 1
            else:
                print(f"   ❌ ÉCHEC")

        except Exception as e:
            print(f"   ❌ ERREUR: {e}")

    print(f"\n📊 RÉSULTATS: {success_count}/{len(test_cases)} tests réussis")

    if success_count >= len(test_cases) // 2:
        print("✅ L'amélioration fonctionne!")
        return True
    else:
        print("❌ L'amélioration a des problèmes")
        return False

if __name__ == "__main__":
    success = test_amelioration_montants()

    if success:
        print("\n🎉 AMÉLIORATION RÉUSSIE!")
        print("La fonction process_amount_text est améliorée.")
        print("Maintenant, testez votre application:")
        print("1. Lancez lancer_detector_cuda_advisor.bat")
        print("2. Capturez l'écran avec une région de montant")
        print("3. Vous devriez voir '10,8' au lieu de '8'")
    else:
        print("\n❌ AMÉLIORATION ÉCHOUÉE")
        print("Il y a encore des problèmes.")

    input("\nAppuyez sur Entrée pour fermer...")
