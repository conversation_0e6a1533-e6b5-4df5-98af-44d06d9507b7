#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'analyse avec corrections
=================================

Ce script teste spécifiquement pourquoi l'analyse
ne fonctionne pas avec les corrections.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def test_analysis_with_corrections():
    """Test de l'analyse avec corrections"""
    
    print("🔍 Test de l'analyse avec corrections")
    print("=" * 60)
    
    # Créer une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection réalistes
    test_results = {
        "card_1": {"text": "Q", "colors": ["black", "white"], "confidence": 0.85},
        "card_2": {"text": "A", "colors": ["red", "white"], "confidence": 0.82},
        "carte_1m": {"text": "J", "colors": ["blue", "white"], "confidence": 0.90},
        "carte_2m": {"text": "7", "colors": ["green", "white"], "confidence": 0.88},
    }
    
    print("📊 Résultats de détection simulés:")
    for region, data in test_results.items():
        print(f"  {region}: '{data['text']}' {data['colors']}")
    
    # Test 1: Analyse SANS corrections
    print("\n🔍 Test 1: Analyse SANS corrections")
    print("-" * 50)
    
    data_without = advisor.extract_poker_data(test_results)
    print(f"Board SANS corrections: '{data_without.get('board_cards_text', 'N/A')}'")
    print(f"Main SANS corrections: '{data_without.get('hand_cards_text', 'N/A')}'")
    print(f"Régions détectées: {data_without.get('detected_regions', [])}")
    
    # Test 2: Appliquer des corrections
    print("\n🔧 Test 2: Application de corrections")
    print("-" * 50)
    
    # Correction normale
    success1 = advisor.set_manual_correction("card_1", "K", "Pique")
    print(f"Correction card_1 Q→K: {success1}")
    
    # Correction "Pas de cartes"
    success2 = advisor.set_manual_correction("card_2", "", "")
    print(f"Correction card_2 A→Pas de cartes: {success2}")
    
    # Correction main
    success3 = advisor.set_manual_correction("carte_1m", "A", "Cœur")
    print(f"Correction carte_1m J→A: {success3}")
    
    corrections = advisor.get_manual_corrections()
    print(f"Corrections actives: {corrections}")
    
    # Test 3: Analyse AVEC corrections
    print("\n🔍 Test 3: Analyse AVEC corrections")
    print("-" * 50)
    
    data_with = advisor.extract_poker_data(test_results)
    print(f"Board AVEC corrections: '{data_with.get('board_cards_text', 'N/A')}'")
    print(f"Main AVEC corrections: '{data_with.get('hand_cards_text', 'N/A')}'")
    print(f"Régions détectées: {data_with.get('detected_regions', [])}")
    print(f"Corrections manuelles: {data_with.get('manual_corrections', [])}")
    
    # Test 4: Analyse complète
    print("\n📈 Test 4: Analyse complète")
    print("-" * 50)
    
    analysis, formatted = advisor.analyze_detection_results(test_results)
    
    print(f"Analysis board_cards_text: '{analysis.get('board_cards_text', 'N/A')}'")
    print(f"Analysis hand_cards_text: '{analysis.get('hand_cards_text', 'N/A')}'")
    
    print(f"\nTexte formaté (premiers 300 caractères):")
    print("=" * 50)
    print(formatted[:300] + "..." if len(formatted) > 300 else formatted)
    print("=" * 50)
    
    # Test 5: Comparaison détaillée
    print("\n📊 Test 5: Comparaison détaillée")
    print("-" * 50)
    
    print("SANS corrections:")
    print(f"  Board: '{data_without.get('board_cards_text', '')}'")
    print(f"  Main: '{data_without.get('hand_cards_text', '')}'")
    
    print("\nAVEC corrections:")
    print(f"  Board: '{data_with.get('board_cards_text', '')}'")
    print(f"  Main: '{data_with.get('hand_cards_text', '')}'")
    
    print("\nAnalyse finale:")
    print(f"  Board: '{analysis.get('board_cards_text', '')}'")
    print(f"  Main: '{analysis.get('hand_cards_text', '')}'")
    
    # Test 6: Vérification des cartes individuelles
    print("\n🃏 Test 6: Vérification des cartes individuelles")
    print("-" * 50)
    
    # Vérifier chaque région individuellement
    for region_name in ["card_1", "card_2", "carte_1m", "carte_2m"]:
        if region_name in advisor.manual_corrections:
            correction = advisor.manual_corrections[region_name]
            value = correction.get("value", "")
            suit = correction.get("suit", "")
            if value:
                print(f"  {region_name}: Correction → {value} de {suit}")
            else:
                print(f"  {region_name}: Correction → Pas de cartes")
        elif region_name in test_results:
            region_data = test_results[region_name]
            text = region_data.get("text", "")
            colors = region_data.get("colors", [])
            print(f"  {region_name}: Détection → {text} {colors}")
        else:
            print(f"  {region_name}: Aucune donnée")
    
    print("\n✅ Test de l'analyse avec corrections terminé!")
    
    # Diagnostic final
    board_has_cards = data_with.get('board_cards_text', '') not in ['', 'Non détectées']
    hand_has_cards = data_with.get('hand_cards_text', '') not in ['', 'Non détectées']
    
    if board_has_cards or hand_has_cards:
        print("\n🎉 DIAGNOSTIC: Les corrections FONCTIONNENT dans l'extraction !")
    else:
        print("\n⚠️ DIAGNOSTIC: Problème dans l'extraction des données avec corrections")
    
    return True

if __name__ == "__main__":
    test_analysis_with_corrections()
