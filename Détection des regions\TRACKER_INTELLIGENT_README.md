# 🎯 TRACKER POKER INTELLIGENT

## 📋 Description

Le **Tracker Poker Intelligent** est un système avancé qui analyse vos fichiers d'historique Winamax pour créer des profils détaillés de vos adversaires et améliorer considérablement votre jeu de poker.

## 🚀 Fonctionnalités

### 📊 **Analyse des Adversaires**
- **VPIP/PFR/3bet** : Statistiques poker classiques
- **Facteur d'agressivité** : Mesure du style de jeu
- **Classification automatique** : Tight/Loose, Passive/Agressif
- **Profils détaillés** : Style de jeu avec niveau de confiance

### 🎭 **Intelligence Adaptative**
- **Recommandations personnalisées** contre chaque adversaire
- **Analyse de table** : Style général et dynamiques
- **Détection du tilt** : Changements de comportement
- **Alertes en temps réel** : Joueurs dangereux

### 📱 **HUD (Heads-Up Display)**
- **Stats en temps réel** : VPIP/PFR/AF pour chaque joueur
- **Codes couleur** : Identification rapide du style
- **Niveau de confiance** : Fiabilité des données

### 💾 **Base de Données Persistante**
- **Stockage SQLite** : Données sauvegardées entre sessions
- **Historique complet** : Accumulation des statistiques
- **Reconnaissance automatique** : Joueurs récurrents

## 📁 Fichiers du Système

```
📦 Tracker Intelligent
├── 🎯 poker_tracker_intelligent.py          # Module principal
├── 🔗 poker_advisor_tracker_integration.py  # Intégration avec conseiller
├── 🧪 test_tracker_intelligent.py           # Tests complets
├── 🚀 lancer_conseiller_avec_tracker.py     # Interface graphique
├── 💾 poker_players.db                      # Base de données (créée auto)
└── 📄 TRACKER_INTELLIGENT_README.md         # Ce fichier
```

## 🛠️ Installation et Configuration

### 1. **Vérification des Prérequis**
```bash
# Vérifier Python (3.7+ requis)
python --version

# Les modules suivants sont inclus dans Python standard :
# - sqlite3, json, re, os, time, datetime, collections, dataclasses, typing, threading, pathlib
```

### 2. **Test du Système**
```bash
# Lancer les tests complets
python test_tracker_intelligent.py
```

### 3. **Lancement de l'Interface**
```bash
# Interface graphique complète
python lancer_conseiller_avec_tracker.py
```

## 🎮 Utilisation

### **Interface Graphique**

1. **Lancez l'interface** :
   ```bash
   python lancer_conseiller_avec_tracker.py
   ```

2. **Mise à jour de la base** :
   - Cliquez sur "🔄 Mettre à jour la base"
   - Le système scanne automatiquement vos fichiers Winamax

3. **Analyse de table** :
   - Entrez les noms des joueurs (séparés par des virgules)
   - Cliquez sur "🎯 Analyser la table"
   - Obtenez des recommandations personnalisées

### **Utilisation Programmatique**

```python
from poker_tracker_intelligent import IntelligentTracker

# Créer le tracker
tracker = IntelligentTracker()

# Scanner les fichiers d'historique
players = tracker.scan_history_files()

# Analyser un joueur spécifique
profile = tracker.get_player_profile("NomDuJoueur")
print(f"Style: {profile['style']['type']}")
print(f"VPIP: {profile['stats'].vpip:.1f}%")

# Analyser une table
table_analysis = tracker.get_table_intelligence(["Joueur1", "Joueur2", "Joueur3"])
print(f"Style de table: {table_analysis['table_style']}")
```

## 📊 Interprétation des Résultats

### **Types de Joueurs**

| Style | VPIP | PFR | Description | Couleur |
|-------|------|-----|-------------|---------|
| **Tight-Passive (Nit)** | <15% | <10% | Très sélectif, peu agressif | 🟢 Vert |
| **Tight-Aggressive (TAG)** | <15% | >10% | Sélectif mais agressif | 🔵 Bleu |
| **Loose-Passive (Calling Station)** | 15-25% | <15% | Joue beaucoup, peu agressif | 🟠 Orange |
| **Loose-Aggressive (LAG)** | 15-25% | >15% | Joue beaucoup et agressif | 🔴 Rouge |
| **Maniac** | >25% | Variable | Joue presque tout | 🟣 Violet |

### **Recommandations par Type**

#### 🟢 **Contre Tight-Passive**
- 🎯 Volez ses blinds fréquemment
- 💰 Value bet thin
- 🚫 Évitez de bluffer
- ⚡ Soyez agressif en position

#### 🔵 **Contre Tight-Aggressive**
- ⚠️ Respectez ses relances
- 🎭 Bluffez avec parcimonie
- 💪 Jouez en position
- 🎯 Exploitez sa tightness preflop

#### 🟠 **Contre Loose-Passive**
- 💰 Value bet large
- 🚫 Ne bluffez jamais
- ⚡ Isolez-le avec des mains moyennes
- 🎯 Exploitez sa passivité postflop

#### 🔴 **Contre Loose-Aggressive**
- 🛡️ Jouez plus tight
- 💪 Utilisez son agressivité contre lui
- 🎭 Piégez avec vos nuts
- ⚠️ Attention aux 3-bets light

## 🔧 Intégration avec Votre Conseiller

### **Méthode Simple**
```python
from poker_advisor_tracker_integration import create_enhanced_advisor

# Créer un conseiller amélioré
advisor = create_enhanced_advisor()

# Définir la table
advisor.set_current_table(["Joueur1", "Joueur2", "Joueur3"])

# Obtenir une recommandation améliorée
recommendation = advisor.get_enhanced_recommendation(
    hand_cards=["As", "Kh"],
    board_cards=["Qd", "Jc", "Ts"],
    pot_size=1000
)

print(f"Action recommandée: {recommendation['final_recommendation']}")
```

### **Intégration avec Conseiller Existant**
```python
from poker_advisor_tracker_integration import integrate_tracker_with_existing_advisor

# Intégrer avec votre conseiller existant
enhanced_advisor = integrate_tracker_with_existing_advisor(your_existing_advisor)

# Maintenant votre conseiller a accès au tracker
table_analysis = enhanced_advisor.get_table_intelligence(current_players)
```

## 📈 Données HUD

Le système fournit des données HUD prêtes à l'affichage :

```python
hud_data = tracker.get_hud_data("NomJoueur")
# Retourne :
{
    "name": "NomJoueur",
    "vpip": "25",      # VPIP en %
    "pfr": "18",       # PFR en %
    "af": "2.1",       # Facteur d'agressivité
    "hands": 150,      # Nombre de mains observées
    "style": "LAG",    # Type de joueur
    "color": "#F44336", # Couleur pour l'affichage
    "confidence": 0.8   # Niveau de confiance (0-1)
}
```

## 🚨 Alertes et Indicateurs

### **Alertes Automatiques**
- 🔥 **Joueur très agressif** : AF > 4
- 🎯 **Joueur très loose** : VPIP > 40%
- ⚠️ **Changement de comportement** : Détection de tilt

### **Niveaux de Confiance**
- **90%+** : 100+ mains observées
- **70%+** : 50+ mains observées
- **50%+** : 25+ mains observées
- **30%** : 10-24 mains observées
- **10%** : <10 mains observées

## 🔍 Dépannage

### **Problèmes Courants**

1. **"Aucun fichier d'historique trouvé"**
   - Vérifiez le chemin : `C:\Users\<USER>\PokerAdvisor\accounts\Tomz-666\history`
   - Assurez-vous d'avoir joué des mains sur Winamax

2. **"Erreur de parsing"**
   - Certains fichiers peuvent être corrompus
   - Le système continue avec les autres fichiers

3. **"VPIP/PFR à 0%"**
   - Normal si peu de mains jouées
   - Le système s'améliore avec plus de données

### **Logs et Debug**
```python
# Activer les logs détaillés
import logging
logging.basicConfig(level=logging.DEBUG)

# Tester un fichier spécifique
from poker_tracker_intelligent import WinamaxParser
parser = WinamaxParser()
hands = parser.parse_file("chemin/vers/fichier.txt")
```

## 🎯 Conseils d'Utilisation

1. **Laissez le système apprendre** : Plus vous jouez, plus il devient précis
2. **Mettez à jour régulièrement** : Scannez après chaque session
3. **Observez les tendances** : Les changements de style peuvent indiquer le tilt
4. **Utilisez les alertes** : Elles signalent les joueurs dangereux
5. **Adaptez votre jeu** : Suivez les recommandations personnalisées

## 📞 Support

Pour toute question ou problème :
1. Lancez d'abord `test_tracker_intelligent.py`
2. Vérifiez les logs dans la console
3. Consultez ce README pour les solutions courantes

---

**🎉 Bonne chance aux tables ! Le tracker intelligent vous donne un avantage considérable sur vos adversaires.**
