#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur CUDA optimisé pour l'application de détection.
"""

import os
import sys

# Forcer CUDA avant tout import
from force_cuda_config import force_cuda_environment, optimize_cuda_memory

def main():
    """Lance l'application avec CUDA forcé"""
    print("🔥 LANCEMENT AVEC CUDA FORCÉ")
    print("=" * 60)
    
    # Configurer CUDA
    force_cuda_environment()
    optimize_cuda_memory()
    
    print("\n🚀 Lancement de l'application...")
    
    # Changer vers le répertoire des régions
    os.chdir("Détection des regions")
    
    # Lancer l'application
    import subprocess
    process = subprocess.Popen([sys.executable, "detector_gui.py"])
    
    try:
        process.wait()
        print("\n✅ Application fermée proprement")
    except KeyboardInterrupt:
        print("\n⚠️ Interruption par l'utilisateur")
        process.terminate()
        process.wait()
    
    print("\n🧹 Nettoyage final CUDA...")
    optimize_cuda_memory()
    
    input("\nAppuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
