#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'analyse des tirages et améliorations possibles
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight

def test_situation_utilisateur():
    """Test de votre situation : 2♠ 7♦ avec 8♥ 5♥ 2♥ Q♥"""
    print("🧪 TEST DE VOTRE SITUATION")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Votre situation exacte
    hand_cards = ["2s", "7d"]  # 2♠ 7♦
    board_cards = ["8h", "5h", "2h", "Qh"]  # 8♥ 5♥ 2♥ Q♥
    
    print("📊 SITUATION:")
    print(f"   Main : {' '.join(hand_cards)} (2♠ 7♦)")
    print(f"   Board : {' '.join(board_cards)} (8♥ 5♥ 2♥ Q♥)")
    print(f"   Cartes à venir : {5 - len(board_cards)} (river)")
    
    # Analyser les tirages et améliorations
    draws_analysis = advisor.analyze_draws_and_improvements(board_cards, hand_cards)
    
    print(f"\n🎯 ANALYSE DES TIRAGES:")
    print(f"   Main actuelle : {draws_analysis['current_hand']}")
    print(f"   Cartes à venir : {draws_analysis['cards_to_come']}")
    print(f"   Total outs : {draws_analysis['total_outs']}")
    
    if 'improvement_probability' in draws_analysis:
        print(f"   Probabilité d'amélioration : {draws_analysis['improvement_probability']}")
    
    print(f"\n🔍 AMÉLIORATIONS POSSIBLES:")
    if draws_analysis['improvements']:
        for improvement in draws_analysis['improvements']:
            print(f"   • {improvement['description']}")
    else:
        print("   Aucune amélioration détectée")
    
    return draws_analysis

def test_autres_situations():
    """Test d'autres situations pour vérifier la logique"""
    print("\n🧪 TEST D'AUTRES SITUATIONS")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    test_cases = [
        {
            "name": "Tirage couleur (4 cœurs)",
            "hand": ["Ah", "Kh"],
            "board": ["5h", "2h", "Qc"],
            "expected": "Tirage couleur"
        },
        {
            "name": "Tirage quinte ouvert",
            "hand": ["6s", "7d"],
            "board": ["5h", "8h", "2c"],
            "expected": "Tirage quinte"
        },
        {
            "name": "Paire peut devenir brelan",
            "hand": ["As", "Ad"],
            "board": ["5h", "2h", "Qc"],
            "expected": "Brelan de A"
        },
        {
            "name": "Overcards",
            "hand": ["As", "Kd"],
            "board": ["5h", "7h", "9c"],
            "expected": "Overcards"
        },
        {
            "name": "Main finale (river)",
            "hand": ["As", "Kd"],
            "board": ["5h", "7h", "9c", "2s", "Qh"],
            "expected": "Main finale"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 {test_case['name']}:")
        print(f"   Main : {' '.join(test_case['hand'])}")
        print(f"   Board : {' '.join(test_case['board'])}")
        
        draws_analysis = advisor.analyze_draws_and_improvements(test_case['board'], test_case['hand'])
        
        print(f"   Main actuelle : {draws_analysis['current_hand']}")
        print(f"   Cartes à venir : {draws_analysis['cards_to_come']}")
        print(f"   Total outs : {draws_analysis['total_outs']}")
        
        if draws_analysis['improvements']:
            print(f"   Améliorations :")
            for improvement in draws_analysis['improvements']:
                print(f"      • {improvement['description']}")
        else:
            print(f"   Améliorations : Aucune")

def test_analyse_complete():
    """Test de l'analyse complète avec le conseiller"""
    print("\n🧪 TEST DE L'ANALYSE COMPLÈTE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler une détection complète
    simulated_results = {
        # Votre situation
        "carte_1m": {"text": "2s", "colors": ["black"]},
        "carte_2m": {"text": "7d", "colors": ["red"]},
        "carte_1b": {"text": "8h", "colors": ["red"]},
        "carte_2b": {"text": "5h", "colors": ["red"]},
        "carte_3b": {"text": "2h", "colors": ["red"]},
        "carte_4b": {"text": "Qh", "colors": ["red"]},
        
        # Jetons et pot
        "mes_jetons": {"text": "1000", "colors": []},
        "pot_total": {"text": "100", "colors": ["white"]},
    }
    
    print("📊 ANALYSE COMPLÈTE:")
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"   Main actuelle : {analysis['hand_strength']}")
    print(f"   Équité : {analysis['equity'][0]}-{analysis['equity'][1]}%")
    print(f"   Action recommandée : {analysis['recommended_action']}")
    
    # Vérifier si l'analyse des tirages est incluse
    if 'draws_analysis' in analysis:
        draws = analysis['draws_analysis']
        print(f"   Tirages détectés : {draws['total_outs']} outs")
        if 'improvement_probability' in draws:
            print(f"   Probabilité d'amélioration : {draws['improvement_probability']}")
    
    print(f"\n📋 AFFICHAGE FORMATÉ:")
    print("=" * 60)
    print(formatted_analysis)

def test_fonctions_individuelles():
    """Test des fonctions individuelles d'analyse"""
    print("\n🧪 TEST DES FONCTIONS INDIVIDUELLES")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Test tirage couleur
    all_cards = [("A", "h"), ("K", "h"), ("5", "h"), ("2", "h"), ("Q", "c")]
    flush_analysis = advisor._analyze_flush_draws(all_cards)
    print(f"🎨 Tirage couleur : {flush_analysis['description']} ({flush_analysis['outs']} outs)")
    
    # Test tirage quinte
    all_cards = [("6", "s"), ("7", "d"), ("5", "h"), ("8", "h"), ("2", "c")]
    straight_analysis = advisor._analyze_straight_draws(all_cards)
    print(f"🎯 Tirage quinte : {straight_analysis['description']} ({straight_analysis['outs']} outs)")
    
    # Test amélioration paires
    all_cards = [("A", "s"), ("A", "d"), ("5", "h"), ("2", "h"), ("Q", "c")]
    hand_cards = [("A", "s"), ("A", "d")]
    pair_analysis = advisor._analyze_pair_improvements(all_cards, hand_cards)
    print(f"👥 Amélioration paires : {pair_analysis['description']} ({pair_analysis['outs']} outs)")
    
    # Test overcards
    board_cards = [("5", "h"), ("7", "h"), ("9", "c")]
    hand_cards = [("A", "s"), ("K", "d")]
    overcard_analysis = advisor._analyze_overcards(board_cards, hand_cards)
    print(f"⬆️ Overcards : {overcard_analysis['description']} ({overcard_analysis['outs']} outs)")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS D'ANALYSE DES TIRAGES")
    print("=" * 60)
    
    # Test principal : votre situation
    result = test_situation_utilisateur()
    
    # Tests supplémentaires
    test_autres_situations()
    
    # Test analyse complète
    test_analyse_complete()
    
    # Test fonctions individuelles
    test_fonctions_individuelles()
    
    print("\n✅ TESTS TERMINÉS")
    
    # Résumé
    print(f"\n📋 RÉSUMÉ POUR VOTRE SITUATION:")
    print(f"   Main actuelle : {result['current_hand']}")
    print(f"   Total outs : {result['total_outs']}")
    if 'improvement_probability' in result:
        print(f"   Probabilité d'amélioration : {result['improvement_probability']}")
    print(f"   Améliorations possibles : {len(result['improvements'])}")
