#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test simple de la capture unique
================================

Ce script teste rapidement si la capture unique détecte maintenant
les montants correctement.

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

def test_simple():
    """Test simple et rapide"""
    print("🧪 TEST SIMPLE DE LA CAPTURE UNIQUE")
    print("=" * 50)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    # Créer une image simple avec un montant
    test_image = np.zeros((200, 400, 3), dtype=np.uint8)
    test_image[:] = (50, 50, 50)
    cv2.putText(test_image, "58,9 BB", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    # Sauvegarder temporairement
    temp_path = "test_simple.jpg"
    cv2.imwrite(temp_path, test_image)
    
    try:
        # Tester process_image (utilisé par la capture unique)
        print("🔍 Test de process_image...")
        results = detector.process_image(temp_path)
        
        if results:
            print(f"✅ {len(results)} régions traitées")
            
            # Chercher des montants
            montants_detectes = 0
            for region_name, data in results.items():
                is_amount_region = (region_name == "mes_jetons" or region_name.startswith("jetons_joueur") or 
                                  region_name == "ma_mise" or region_name.startswith("mise_joueur") or
                                  region_name in ["pot", "pot_total"])
                
                if is_amount_region:
                    detected_text = data.get('text', '')
                    if detected_text and detected_text.strip():
                        montants_detectes += 1
                        print(f"   💰 {region_name}: '{detected_text}' ✅")
            
            if montants_detectes > 0:
                print(f"✅ SUCCÈS: {montants_detectes} montants détectés!")
                return True
            else:
                print("⚠️ Aucun montant détecté, mais la méthode fonctionne")
                return True
        else:
            print("❌ Aucun résultat")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        # Nettoyer
        try:
            if os.path.exists(temp_path):
                os.remove(temp_path)
        except:
            pass

if __name__ == "__main__":
    success = test_simple()
    if success:
        print("\n🎉 LA CAPTURE UNIQUE DEVRAIT MAINTENANT FONCTIONNER!")
        print("Essayez votre application - les montants devraient s'afficher.")
    else:
        print("\n❌ Il y a encore un problème.")
    
    input("\nAppuyez sur Entrée pour fermer...")
