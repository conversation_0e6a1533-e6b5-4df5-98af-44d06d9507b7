#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour simuler un crash d'application et vérifier 
que le système de surveillance fonctionne correctement.
"""

import os
import sys
import time
import random
from monitor_app import AppMonitor

def simulate_application_crash():
    """Simule une application qui plante après un certain temps"""
    print("🚀 Simulation d'une application avec surveillance")
    print("=" * 60)
    
    # Créer une instance du moniteur
    monitor = AppMonitor()
    
    try:
        # Démarrer la surveillance
        print("🔍 Démarrage de la surveillance...")
        monitor.start_monitoring()
        
        # Simuler une application qui fonctionne pendant un moment
        print("⚙️ Application en cours d'exécution...")
        
        # Simuler différents scénarios
        scenarios = [
            "normal_execution",
            "memory_leak",
            "cpu_spike", 
            "sudden_crash"
        ]
        
        scenario = random.choice(scenarios)
        print(f"📋 Scénario sélectionné: {scenario}")
        
        if scenario == "normal_execution":
            print("✅ Exécution normale pendant 20 secondes...")
            time.sleep(20)
            print("✅ Application terminée normalement")
            
        elif scenario == "memory_leak":
            print("⚠️ Simulation d'une fuite mémoire...")
            # Simuler une fuite mémoire en créant des objets
            memory_hog = []
            for i in range(10):
                memory_hog.append([0] * 1000000)  # 1M d'entiers
                print(f"💾 Allocation mémoire #{i+1}")
                time.sleep(2)
            print("❌ Application terminée avec fuite mémoire")
            
        elif scenario == "cpu_spike":
            print("⚠️ Simulation d'un pic CPU...")
            start_time = time.time()
            while time.time() - start_time < 15:
                # Calcul intensif pour simuler un pic CPU
                sum(i*i for i in range(10000))
            print("❌ Application terminée après pic CPU")
            
        elif scenario == "sudden_crash":
            print("💥 Simulation d'un crash soudain...")
            time.sleep(5)
            print("💥 CRASH SIMULÉ!")
            # Simuler un crash en levant une exception
            raise Exception("Crash simulé pour test")
            
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")
        
    except Exception as e:
        print(f"\n💥 ERREUR: {e}")
        print("📊 Affichage des statistiques avant crash...")
        
        # Afficher les statistiques avant de quitter
        if monitor.stats_history:
            latest = monitor.stats_history[-1]
            print(f"   CPU: {latest['system_cpu']:.1f}%")
            print(f"   RAM: {latest['system_memory_mb']:.0f}MB")
            print(f"   GPU: {latest['gpu_memory_mb']:.0f}MB")
            print(f"   Processus poker: {latest['poker_processes_count']}")
    
    finally:
        # Arrêter la surveillance
        print("\n🛑 Arrêt de la surveillance...")
        monitor.stop_monitoring()
        
        # Afficher un résumé final
        print("\n📈 RÉSUMÉ FINAL:")
        print(monitor.get_summary())
        
        print("\n👋 Test de crash terminé")

if __name__ == "__main__":
    simulate_application_crash()
