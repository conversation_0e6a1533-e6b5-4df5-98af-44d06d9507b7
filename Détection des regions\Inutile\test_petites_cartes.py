#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'amélioration pour les petites cartes (cartes en main)
"""

import cv2
import numpy as np

def test_detection_petites_cartes():
    """Test de la détection améliorée pour les petites cartes"""
    print("🔍 TEST DÉTECTION PETITES CARTES (CARTES EN MAIN)")
    print("=" * 60)
    
    try:
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector(use_cuda=True)
        
        # Test 1: Petite carte simulée (comme carte en main)
        print("🎯 Test 1: Petite carte (50x70 pixels)")
        
        # Créer une petite image de carte
        small_card = np.ones((50, 70, 3), dtype=np.uint8) * 255  # Fond blanc
        cv2.putText(small_card, "A", (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        print(f"   Taille originale: {small_card.shape[:2]}")
        
        # Test AVANT amélioration (comme carte du board)
        print("   🔍 Détection normale (board):")
        result_normal = detector.detect_text_simple(small_card, is_hand_card=False)
        print(f"     Résultat: '{result_normal}'")
        
        # Test APRÈS amélioration (comme carte en main)
        print("   🚀 Détection optimisée (main):")
        result_optimized = detector.detect_text_simple(small_card, is_hand_card=True)
        print(f"     Résultat: '{result_optimized}'")
        
        # Comparer les résultats
        if result_optimized and not result_normal:
            print("   ✅ AMÉLIORATION: Carte détectée avec optimisation !")
        elif result_optimized == result_normal and result_optimized:
            print("   ✅ STABLE: Même résultat avec les deux méthodes")
        elif result_normal and not result_optimized:
            print("   ⚠️ RÉGRESSION: Méthode normale meilleure")
        else:
            print("   ❌ ÉCHEC: Aucune méthode n'a détecté la carte")
        
        # Test 2: Très petite carte (comme vraie carte en main)
        print("\n🎯 Test 2: Très petite carte (30x40 pixels)")
        
        tiny_card = np.ones((30, 40, 3), dtype=np.uint8) * 255
        cv2.putText(tiny_card, "K", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
        
        print(f"   Taille originale: {tiny_card.shape[:2]}")
        
        result_tiny_normal = detector.detect_text_simple(tiny_card, is_hand_card=False)
        result_tiny_optimized = detector.detect_text_simple(tiny_card, is_hand_card=True)
        
        print(f"   Normal: '{result_tiny_normal}'")
        print(f"   Optimisé: '{result_tiny_optimized}'")
        
        # Test 3: Carte avec couleur
        print("\n🎯 Test 3: Petite carte rouge")
        
        red_card = np.ones((45, 60, 3), dtype=np.uint8) * 255
        cv2.putText(red_card, "Q", (15, 35), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)  # Rouge
        
        # Tester texte ET couleurs
        text_result = detector.detect_text_simple(red_card, is_hand_card=True)
        colors_result = detector.detect_colors_fast(red_card)
        
        print(f"   Texte détecté: '{text_result}'")
        print(f"   Couleurs détectées: {colors_result}")
        
        # Vérifier la cohérence
        if text_result and colors_result:
            print("   ✅ SUCCÈS: Texte ET couleurs détectés !")
            if 'red' in colors_result:
                print("   ✅ Couleur rouge correctement identifiée")
        elif text_result and not colors_result:
            print("   ⚠️ PARTIEL: Texte détecté mais pas de couleurs")
        elif not text_result and colors_result:
            print("   ⚠️ PROBLÈME: Couleurs détectées mais pas de texte")
            print("   💡 C'est exactement votre problème !")
        else:
            print("   ❌ ÉCHEC: Ni texte ni couleurs détectés")
        
        # Test 4: Simulation du problème utilisateur
        print("\n🎯 Test 4: Simulation problème utilisateur")
        print("   (Couleurs détectées mais pas de texte)")
        
        # Créer une carte difficile à lire
        difficult_card = np.ones((35, 50, 3), dtype=np.uint8) * 240  # Fond gris clair
        cv2.putText(difficult_card, "J", (12, 28), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (50, 50, 50), 1)  # Gris foncé
        
        # Ajouter du bruit pour rendre la détection difficile
        noise = np.random.randint(0, 30, difficult_card.shape, dtype=np.uint8)
        difficult_card = cv2.add(difficult_card, noise)
        
        text_difficult = detector.detect_text_simple(difficult_card, is_hand_card=True)
        colors_difficult = detector.detect_colors_fast(difficult_card)
        
        print(f"   Texte (carte difficile): '{text_difficult}'")
        print(f"   Couleurs (carte difficile): {colors_difficult}")
        
        if not text_difficult and colors_difficult:
            print("   🎯 PROBLÈME REPRODUIT: Couleurs sans texte")
            print("   💡 Solutions possibles:")
            print("     - Seuils de confiance plus bas")
            print("     - Agrandissement plus important")
            print("     - Prétraitement plus agressif")
        
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES AMÉLIORATIONS:")
        print("✅ Agrandissement 3x pour cartes en main")
        print("✅ Seuils de confiance adaptatifs (50% main, 60% board)")
        print("✅ Prétraitement plus agressif (clipLimit=3.0)")
        print("✅ Logs détaillés pour diagnostic")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_seuils_confiance():
    """Test des nouveaux seuils de confiance"""
    print("\n🎯 TEST SEUILS DE CONFIANCE")
    print("=" * 40)
    
    print("📊 Nouveaux seuils implémentés:")
    print("   Cartes en main: 50% (au lieu de 80%)")
    print("   Cartes du board: 60% (au lieu de 80%)")
    print("   Fallback faible confiance: 30%")
    
    print("\n💡 Avantages:")
    print("   ✅ Plus de textes détectés sur petites cartes")
    print("   ✅ Moins de 'couleurs sans texte'")
    print("   ✅ Meilleure détection des cartes difficiles")
    print("   ✅ Tri par confiance pour prendre le meilleur")

def main():
    """Fonction principale"""
    print("🚀 TEST COMPLET AMÉLIORATION PETITES CARTES")
    print("=" * 60)
    
    # Test 1: Détection améliorée
    success = test_detection_petites_cartes()
    
    # Test 2: Seuils de confiance
    test_seuils_confiance()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES AMÉLIORATIONS IMPLÉMENTÉES")
    print("=" * 60)
    
    if success:
        print("✅ Agrandissement 3x pour cartes en main")
        print("✅ Seuils de confiance adaptatifs")
        print("✅ Prétraitement optimisé")
        print("✅ Gestion des cas difficiles")
        print("\n🎉 VOS CARTES EN MAIN DEVRAIENT MAINTENANT ÊTRE MIEUX DÉTECTÉES !")
        print("\n💡 Testez avec votre application:")
        print("   1. Lancez une détection")
        print("   2. Regardez les logs pour 'carte en main (petite)'")
        print("   3. Vérifiez si le texte est maintenant détecté")
    else:
        print("❌ Problème lors des tests")
        print("💡 Vérifiez l'installation du détecteur")

if __name__ == "__main__":
    main()
