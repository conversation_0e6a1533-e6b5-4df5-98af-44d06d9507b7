@echo off
echo ===================================================
echo CORRECTION DU PROBLÈME DE DÉTECTION
echo ===================================================
echo.

echo 1. Vérification de Python...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python ne répond pas correctement
    echo 💡 Redémarrez votre terminal ou votre PC
    pause
    exit /b 1
)

echo.
echo 2. Test d'import basique...
python -c "print('✅ Python fonctionne')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème avec l'exécution Python
    pause
    exit /b 1
)

echo.
echo 3. Test des imports essentiels...
python -c "import cv2; import numpy; print('✅ OpenCV et NumPy OK')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème avec OpenCV ou NumPy
    echo 💡 Réinstallez: pip install opencv-python numpy
    pause
    exit /b 1
)

echo.
echo 4. Test EasyOCR...
python -c "import easyocr; print('✅ EasyOCR disponible')"
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ EasyOCR non disponible, installation...
    pip install easyocr
)

echo.
echo 5. Test PaddleOCR...
python -c "from paddleocr import PaddleOCR; print('✅ PaddleOCR disponible')"
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ PaddleOCR non disponible
    echo 💡 C'est normal si vous avez désinstallé PaddlePaddle
)

echo.
echo 6. Test du détecteur...
python -c "from detector import Detector; d = Detector(use_cuda=False); print('✅ Détecteur initialisé')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème avec le détecteur
    echo 💡 Vérifiez les erreurs ci-dessus
    pause
    exit /b 1
)

echo.
echo ===================================================
echo ✅ DIAGNOSTIC TERMINÉ
echo ===================================================
echo Votre détecteur devrait maintenant fonctionner.
echo Si le problème persiste, redémarrez votre terminal.
echo ===================================================
echo.

pause
