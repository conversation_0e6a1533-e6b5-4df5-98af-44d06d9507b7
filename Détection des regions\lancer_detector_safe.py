#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur sécurisé pour detector_gui avec gestion d'erreurs renforcée.
"""

import sys
import os
import traceback
import psutil
from datetime import datetime

def monitor_memory():
    """Surveille l'utilisation mémoire"""
    try:
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024  # MB
    except:
        return 0

def check_prerequisites():
    """Vérifications préliminaires"""
    print("🔍 Vérifications préliminaires...")
    
    # Vérifier le fichier de calibration
    if not os.path.exists("calibration_config.json"):
        print("❌ Fichier calibration_config.json manquant!")
        print("Le fichier a été créé automatiquement.")
        return True
    
    print("✅ Fichier de calibration trouvé")
    
    # Vérifier les modules essentiels
    try:
        import cv2
        import numpy as np
        from PyQt5.QtWidgets import QApplication
        print("✅ Modules essentiels disponibles")
        return True
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        return False

def safe_launch():
    """Lance l'application de manière sécurisée"""
    print("🛡️ LANCEUR SÉCURISÉ DETECTOR GUI")
    print("=" * 50)
    
    # Vérifications préliminaires
    if not check_prerequisites():
        print("❌ Prérequis non satisfaits")
        return False
    
    # Surveiller la mémoire
    mem_start = monitor_memory()
    print(f"💾 Mémoire initiale: {mem_start:.1f} MB")
    
    try:
        print("🚀 Lancement de detector_gui...")
        
        # Import et lancement avec gestion d'erreurs
        try:
            from detector_gui import DetectorGUI
            from PyQt5.QtWidgets import QApplication
        except ImportError as e:
            print(f"❌ Erreur d'import: {e}")
            return False
        
        # Créer l'application Qt
        try:
            app = QApplication(sys.argv)
        except Exception as e:
            print(f"❌ Erreur création QApplication: {e}")
            return False
        
        # Créer la fenêtre principale
        try:
            print("🔧 Création de la fenêtre principale...")
            window = DetectorGUI()
            print("✅ Fenêtre créée avec succès")
        except Exception as e:
            print(f"❌ Erreur création DetectorGUI: {e}")
            traceback.print_exc()
            return False
        
        # Afficher la fenêtre
        try:
            window.show()
            print("✅ Application lancée avec succès")
        except Exception as e:
            print(f"❌ Erreur affichage fenêtre: {e}")
            return False
        
        print("\n📋 CONSEILS D'UTILISATION:")
        print("   - Testez d'abord avec quelques régions seulement")
        print("   - Surveillez l'utilisation mémoire")
        print("   - En cas de problème, fermez et relancez")
        print("   - Calibrez vos régions avant la première utilisation")
        
        # Lancer la boucle d'événements
        try:
            result = app.exec_()
            print("✅ Application fermée proprement")
            return True
        except Exception as e:
            print(f"❌ Erreur durant l'exécution: {e}")
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Erreur générale lors du lancement: {e}")
        traceback.print_exc()
        
        mem_end = monitor_memory()
        print(f"💾 Mémoire finale: {mem_end:.1f} MB")
        if mem_start > 0:
            print(f"💾 Différence: {mem_end - mem_start:.1f} MB")
        
        return False
    
    finally:
        # Nettoyage final
        try:
            import gc
            gc.collect()
        except:
            pass

def show_help():
    """Affiche l'aide"""
    print("\n🆘 AIDE ET DÉPANNAGE")
    print("=" * 30)
    print("Si l'application se ferme:")
    print("   1. Vérifiez debug_crash.log")
    print("   2. Réduisez le nombre de régions actives")
    print("   3. Désactivez temporairement le conseiller poker")
    print("   4. Vérifiez l'espace disque disponible")
    print("\nFichiers de diagnostic:")
    print("   - debug_crash.log : Logs de crash")
    print("   - monitor.log : Surveillance système")
    print("   - calibration_config.json : Configuration des régions")
    print("\nCommandes utiles:")
    print("   - python diagnostic_crash_detection.py")
    print("   - python test_version_securisee.py")

def main():
    """Fonction principale"""
    print("🚀 DÉMARRAGE DU LANCEUR SÉCURISÉ")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        success = safe_launch()
        
        if success:
            print("\n🎉 SESSION TERMINÉE AVEC SUCCÈS")
        else:
            print("\n❌ PROBLÈME LORS DU LANCEMENT")
            show_help()
    
    except KeyboardInterrupt:
        print("\n⚠️ Interruption par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur critique: {e}")
        traceback.print_exc()
        show_help()
    
    print("\n👋 Merci d'avoir utilisé le lanceur sécurisé!")
    input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
