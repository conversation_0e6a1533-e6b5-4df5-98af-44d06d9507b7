#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'affichage du montant recommandé dans l'analyse détaillée
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight

def test_extraction_montant():
    """Test de l'extraction du montant recommandé"""
    print("🧪 TEST DE L'EXTRACTION DU MONTANT")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    test_cases = [
        {"action": "raise 2.5 BB", "bet_to_call": 0, "expected": 2.5},
        {"action": "RAISE 5BB", "bet_to_call": 0, "expected": 5.0},
        {"action": "raise 10", "bet_to_call": 0, "expected": 10.0},
        {"action": "call 3 BB", "bet_to_call": 3, "expected": 3.0},
        {"action": "call", "bet_to_call": 5, "expected": 5.0},
        {"action": "all-in", "bet_to_call": 0, "expected": 0},
        {"action": "fold", "bet_to_call": 2, "expected": 0},
    ]
    
    for case in test_cases:
        action = case["action"]
        bet_to_call = case["bet_to_call"]
        expected = case["expected"]
        
        result = advisor.extract_recommended_amount(action, bet_to_call)
        
        print(f"🔍 Action: '{action}'")
        print(f"   Bet to call: {bet_to_call}")
        print(f"   Résultat: {result}")
        print(f"   Attendu: {expected}")
        print(f"   Status: {'✅ OK' if result == expected else '❌ ERREUR'}")
        print()

def test_formatage_montant():
    """Test du formatage du montant pour l'affichage"""
    print("🧪 TEST DU FORMATAGE DU MONTANT")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    test_cases = [
        {"amount": 2.5, "action": "raise 2.5 BB", "bet_to_call": 0, "expected": "3 BB (relance)"},
        {"amount": 5.0, "action": "RAISE 5BB", "bet_to_call": 0, "expected": "5 BB (relance)"},
        {"amount": 3.0, "action": "call 3 BB", "bet_to_call": 3, "expected": "3 BB (suivre)"},
        {"amount": 0, "action": "call", "bet_to_call": 5, "expected": "5 BB (suivre)"},
        {"amount": 0, "action": "all-in", "bet_to_call": 0, "expected": "All-in (tout le stack)"},
        {"amount": 0, "action": "fold", "bet_to_call": 2, "expected": "0 BB (se coucher)"},
    ]
    
    for case in test_cases:
        amount = case["amount"]
        action = case["action"]
        bet_to_call = case["bet_to_call"]
        expected = case["expected"]
        
        result = advisor.format_recommended_amount(amount, action, bet_to_call)
        
        print(f"🔍 Action: '{action}'")
        print(f"   Montant: {amount}")
        print(f"   Bet to call: {bet_to_call}")
        print(f"   Résultat: '{result}'")
        print(f"   Attendu: '{expected}'")
        print(f"   Status: {'✅ OK' if result == expected else '❌ ERREUR'}")
        print()

def test_situation_complete():
    """Test d'une situation complète avec affichage"""
    print("🧪 TEST SITUATION COMPLÈTE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler une situation avec relance recommandée
    simulated_results = {
        # Main forte
        "carte_1m": {"text": "As", "colors": ["red"]},
        "carte_2m": {"text": "Kd", "colors": ["black"]},
        
        # Stack important
        "mes_jetons": {"text": "500", "colors": []},
        
        # Adversaire avec mise
        "jetons_joueur1": {"text": "300", "colors": ["white"]},
        "mise_joueur1": {"text": "10", "colors": ["white"]},
        
        # Pot
        "pot_total": {"text": "25", "colors": ["white"]},
    }
    
    print("📊 SITUATION: AK en main, 500 BB de stack, adversaire mise 10 BB")
    
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"   Main: {analysis['hand_strength']}")
    print(f"   Action: {analysis['recommended_action']}")
    print(f"   Montant extrait: {analysis.get('recommended_amount', 'Non trouvé')}")
    
    # Vérifier l'affichage formaté
    print(f"\n📋 AFFICHAGE FORMATÉ:")
    print("=" * 60)
    lines = formatted_analysis.split('\n')
    for line in lines:
        if "Montant recommandé" in line:
            print(f"   {line}")
            break
    else:
        print("   ❌ Ligne 'Montant recommandé' non trouvée")
    
    # Afficher quelques lignes de contexte
    print(f"\n📄 EXTRAIT DE L'ANALYSE:")
    lines = formatted_analysis.split('\n')
    for i, line in enumerate(lines):
        if "Analyse détaillée" in line:
            for j in range(max(0, i), min(len(lines), i+6)):
                print(f"   {lines[j]}")
            break

def test_cas_specifique_utilisateur():
    """Test du cas spécifique de l'utilisateur"""
    print("\n🧪 TEST CAS SPÉCIFIQUE UTILISATEUR")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler la situation de l'utilisateur
    simulated_results = {
        # Paire de 2
        "carte_1m": {"text": "2s", "colors": ["black"]},
        "carte_2m": {"text": "7d", "colors": ["red"]},
        
        # Board avec tirage couleur
        "carte_1b": {"text": "8h", "colors": ["red"]},
        "carte_2b": {"text": "5h", "colors": ["red"]},
        "carte_3b": {"text": "2h", "colors": ["red"]},
        "carte_4b": {"text": "Qh", "colors": ["red"]},
        
        # Stack important
        "mes_jetons": {"text": "1000", "colors": []},
        
        # Pot
        "pot_total": {"text": "50", "colors": ["white"]},
    }
    
    print("📊 SITUATION: Votre cas (2♠ 7♦ avec 8♥ 5♥ 2♥ Q♥), 1000 BB de stack")
    
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"   Main: {analysis['hand_strength']}")
    print(f"   Action: {analysis['recommended_action']}")
    print(f"   Montant extrait: {analysis.get('recommended_amount', 'Non trouvé')}")
    
    # Extraire et afficher la ligne du montant recommandé
    lines = formatted_analysis.split('\n')
    for line in lines:
        if "Montant recommandé" in line:
            print(f"   Affichage: {line.strip()}")
            
            # Vérifier que ce n'est plus "0 BB"
            if "0 BB" in line and "se coucher" not in line:
                print(f"   ❌ PROBLÈME: Affiche encore 0 BB")
            else:
                print(f"   ✅ CORRECT: Affiche le bon montant")
            break
    else:
        print("   ❌ Ligne 'Montant recommandé' non trouvée")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS D'AFFICHAGE DU MONTANT")
    print("=" * 60)
    
    # Test extraction
    test_extraction_montant()
    
    # Test formatage
    test_formatage_montant()
    
    # Test situation complète
    test_situation_complete()
    
    # Test cas utilisateur
    test_cas_specifique_utilisateur()
    
    print("\n✅ TESTS TERMINÉS")
    print("\n📋 RÉSUMÉ:")
    print("   L'analyse détaillée doit maintenant afficher le montant recommandé")
    print("   Au lieu de 'Mise à suivre: 0 BB', on doit voir 'Montant recommandé: X BB (relance)'")
