@echo off
echo ===================================================
echo RESTAURATION DE PADDLEOCR AVEC CUDA
echo ===================================================
echo.

echo 1. Désinstallation des versions existantes...
pip uninstall paddlepaddle paddlepaddle-gpu -y

echo.
echo 2. Installation de PaddlePaddle GPU...
pip install paddlepaddle-gpu

echo.
echo 3. Vérification de l'installation...
python -c "import paddle; print(f'✅ PaddlePaddle installé'); print(f'CUDA compilé: {paddle.device.is_compiled_with_cuda()}')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème avec PaddlePaddle
    echo Tentative d'installation version spécifique...
    pip install paddlepaddle-gpu==2.6.0
)

echo.
echo 4. Test de PaddleOCR...
python -c "from paddleocr import PaddleOCR; print('✅ PaddleOCR importé avec succès')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème avec PaddleOCR
    echo Installation de PaddleOCR...
    pip install paddleocr
)

echo.
echo 5. Test du détecteur restauré...
python -c "from detector import Detector; d = Detector(use_cuda=True); print('✅ Détecteur avec CUDA initialisé')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Problème avec le détecteur CUDA
    echo Test en mode CPU...
    python -c "from detector import Detector; d = Detector(use_cuda=False); print('✅ Détecteur CPU initialisé')"
)

echo.
echo ===================================================
echo ✅ RESTAURATION TERMINÉE
echo ===================================================
echo Votre détecteur PaddleOCR est maintenant restauré
echo avec la configuration originale qui fonctionnait.
echo ===================================================
echo.

pause
