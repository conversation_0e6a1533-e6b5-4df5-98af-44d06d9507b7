#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test simple de la logique avancée pour identifier le problème de crash.
"""

def test_logique_avancee_simple():
    """Test simple de la logique avancée"""
    print("🧪 TEST SIMPLE DE LA LOGIQUE AVANCÉE")
    print("=" * 50)
    
    try:
        # Test d'import
        print("1. Test d'import...")
        from poker_advisor_integration import poker_integration
        print("✅ Import réussi")
        
        # Test simple avec des cartes basiques
        print("\n2. Test avec des cartes simples...")
        hand_values = ["As", "Roi"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["Dame", "Valet", "10"]
        board_suits = ["Trèfle", "Carreau", "Cœur"]
        
        print(f"   Main: {hand_values} de {hand_suits}")
        print(f"   Board: {board_values} de {board_suits}")
        
        result = poker_integration.evaluate_hand_advanced(
            hand_values, hand_suits, board_values, board_suits
        )
        
        print(f"✅ Résultat: {result['hand_description']}")
        print(f"✅ Équité: {result['equity']:.1f}%")
        print(f"✅ Action: {result['recommendations']['action']}")
        
        # Test avec des cartes vides (cas problématique potentiel)
        print("\n3. Test avec des cartes vides...")
        result_empty = poker_integration.evaluate_hand_advanced(
            [], [], [], []
        )
        
        print(f"✅ Résultat cartes vides: {result_empty['hand_description']}")
        
        # Test avec une seule carte
        print("\n4. Test avec une seule carte...")
        result_one = poker_integration.evaluate_hand_advanced(
            ["As"], ["Cœur"], [], []
        )
        
        print(f"✅ Résultat une carte: {result_one['hand_description']}")
        
        print("\n🎉 TOUS LES TESTS SIMPLES RÉUSSIS !")
        print("✅ La logique avancée fonctionne correctement")
        print("✅ Le problème ne vient pas de la logique avancée elle-même")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR dans le test simple: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_detector():
    """Test d'intégration avec le détecteur"""
    print("\n🔧 TEST D'INTÉGRATION AVEC LE DÉTECTEUR")
    print("=" * 50)
    
    try:
        # Simuler l'appel depuis detector_gui.py
        print("1. Simulation de l'appel depuis detector_gui...")
        
        # Importer comme dans detector_gui.py
        from poker_advisor_integration import poker_integration
        
        # Simuler des cartes détectées
        hand_cards = ["As de Cœur", "Roi de Pique"]
        board_cards = ["Dame de Trèfle", "Valet de Carreau", "10 de Cœur"]
        
        print(f"   hand_cards: {hand_cards}")
        print(f"   board_cards: {board_cards}")
        
        # Conversion comme dans detector_gui.py
        hand_values = []
        hand_suits = []
        for card in hand_cards:
            if " de " in card:
                parts = card.split(" de ")
                hand_values.append(parts[0])
                hand_suits.append(parts[1])
        
        board_values = []
        board_suits = []
        for card in board_cards:
            if " de " in card:
                parts = card.split(" de ")
                board_values.append(parts[0])
                board_suits.append(parts[1])
        
        print(f"   Conversion: hand_values={hand_values}, hand_suits={hand_suits}")
        print(f"   Conversion: board_values={board_values}, board_suits={board_suits}")
        
        # Appel de la logique avancée
        analysis_result = poker_integration.evaluate_hand_advanced(
            hand_values, hand_suits, board_values, board_suits
        )
        
        print(f"✅ Analyse réussie: {analysis_result['hand_description']}")
        
        # Extraction des résultats comme dans detector_gui.py
        probability = analysis_result['equity']
        action = analysis_result['recommendations']['action']
        reason = analysis_result['recommendations']['reason']
        
        print(f"✅ Probabilité: {probability:.1f}%")
        print(f"✅ Action: {action}")
        print(f"✅ Raison: {reason}")
        
        print("\n🎉 TEST D'INTÉGRATION RÉUSSI !")
        print("✅ L'intégration avec le détecteur fonctionne")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR dans le test d'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC DE LA LOGIQUE AVANCÉE")
    print("=" * 60)
    
    # Test 1: Logique avancée simple
    success1 = test_logique_avancee_simple()
    
    # Test 2: Intégration avec le détecteur
    success2 = test_integration_detector()
    
    if success1 and success2:
        print("\n✅ DIAGNOSTIC COMPLET RÉUSSI !")
        print("=" * 40)
        print("✅ La logique avancée fonctionne parfaitement")
        print("✅ L'intégration avec le détecteur est correcte")
        print("✅ Le problème de crash ne vient PAS de la logique avancée")
        print("\n🔍 PISTES À EXPLORER:")
        print("   - Problème dans la capture d'écran")
        print("   - Problème dans la détection OCR")
        print("   - Problème dans l'interface graphique")
        print("   - Problème de mémoire/performance")
    else:
        print("\n❌ PROBLÈME DÉTECTÉ DANS LA LOGIQUE AVANCÉE")
        print("Consultez les erreurs ci-dessus pour les corrections")

if __name__ == "__main__":
    main()
