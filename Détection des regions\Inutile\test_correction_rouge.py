#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la correction des fausses détections de rouge
"""

import cv2
import numpy as np

def test_correction_rouge():
    """Test de la correction des fausses détections de rouge"""
    print("🔍 TEST CORRECTION FAUSSES DÉTECTIONS ROUGE")
    print("=" * 50)
    
    try:
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector(use_cuda=True)
        
        # Créer une image de test simulant une carte noire (Pique/Trèfle)
        # Blanc + Noir (pas de rouge)
        test_image = np.ones((100, 80, 3), dtype=np.uint8) * 255  # Fond blanc
        
        # Ajouter du texte noir (simuler une carte noire)
        cv2.putText(test_image, "A", (25, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 4)
        
        print("🎯 Test 1: Carte noire normale (A de Pique)")
        
        # Détecter les couleurs
        colors = detector.detect_colors_fast(test_image)
        print(f"   Couleurs détectées: {colors}")
        
        # Simuler le traitement avec fausse détection de rouge
        colors_avec_faux_rouge = ['red', 'white', 'black']
        print(f"   Simulation fausse détection: {colors_avec_faux_rouge}")
        
        # Tester la correction
        text_corrige, colors_corrigees = detector.process_card_region_fast(
            test_image, "A", colors_avec_faux_rouge, "carte_test"
        )
        
        print(f"   Après correction:")
        print(f"     Texte: '{text_corrige}'")
        print(f"     Couleurs: {colors_corrigees}")
        
        # Vérifier que le rouge a été retiré
        if 'red' not in colors_corrigees:
            print("   ✅ SUCCÈS: Rouge correctement retiré !")
        else:
            print("   ❌ ÉCHEC: Rouge toujours présent")
        
        # Test 2: Vraie carte rouge
        print("\n🎯 Test 2: Vraie carte rouge (A de Cœur)")
        
        # Créer une image avec du rouge réel
        test_image_rouge = np.ones((100, 80, 3), dtype=np.uint8) * 255  # Fond blanc
        cv2.putText(test_image_rouge, "A", (25, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 4)  # Texte rouge
        
        colors_vraie_rouge = ['red', 'white']  # Pas de noir
        print(f"   Couleurs vraie carte rouge: {colors_vraie_rouge}")
        
        text_corrige2, colors_corrigees2 = detector.process_card_region_fast(
            test_image_rouge, "A", colors_vraie_rouge, "carte_rouge_test"
        )
        
        print(f"   Après traitement:")
        print(f"     Texte: '{text_corrige2}'")
        print(f"     Couleurs: {colors_corrigees2}")
        
        # Vérifier que le rouge est conservé
        if 'red' in colors_corrigees2:
            print("   ✅ SUCCÈS: Rouge conservé pour vraie carte rouge !")
        else:
            print("   ❌ ÉCHEC: Rouge retiré à tort")
        
        # Test 3: Seuils de détection
        print("\n🎯 Test 3: Nouveaux seuils de détection")
        
        # Tester avec une image avec très peu de rouge (bruit)
        test_image_bruit = np.ones((100, 80, 3), dtype=np.uint8) * 255
        cv2.putText(test_image_bruit, "K", (25, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 4)
        
        # Ajouter quelques pixels rouges (bruit)
        test_image_bruit[10:15, 10:15] = [0, 0, 255]  # Petit carré rouge
        
        colors_avec_bruit = detector.detect_colors_fast(test_image_bruit)
        print(f"   Couleurs avec bruit rouge: {colors_avec_bruit}")
        
        # Avec les nouveaux seuils (8% pour rouge), le bruit ne devrait pas être détecté
        if 'red' not in colors_avec_bruit:
            print("   ✅ SUCCÈS: Bruit rouge ignoré grâce aux nouveaux seuils !")
        else:
            print("   ⚠️ Bruit rouge détecté - Seuils peut-être encore trop bas")
        
        print("\n" + "=" * 50)
        print("✅ TESTS TERMINÉS")
        print("💡 La correction devrait maintenant éviter les fausses détections de rouge")
        print("   sur vos cartes en main noires (Pique/Trèfle)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    success = test_correction_rouge()
    
    if success:
        print("\n🎉 CORRECTION IMPLÉMENTÉE AVEC SUCCÈS !")
        print("📋 Changements apportés:")
        print("   1. Seuil rouge augmenté: 2% → 8%")
        print("   2. Seuil noir augmenté: 2% → 3%")
        print("   3. Seuil blanc ajouté: 10%")
        print("   4. Détection rouge+blanc+noir → Correction automatique")
        print("\n💡 Testez maintenant avec vos cartes en main !")
    else:
        print("\n❌ Problème lors de l'implémentation")
        print("💡 Vérifiez que le détecteur fonctionne correctement")

if __name__ == "__main__":
    main()
