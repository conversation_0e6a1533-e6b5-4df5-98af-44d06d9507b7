#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour le module multi-OCR de Poker Advisor
=======================================================

Ce script teste le module multi-OCR en détectant des cartes dans des images de test.
Il compare les résultats de PaddleOCR et EasyOCR pour évaluer la précision.

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import sys
import cv2
import numpy as np
import time
import argparse
from pathlib import Path

# Ajouter le répertoire parent au chemin de recherche des modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importer les modules nécessaires
try:
    from multi_ocr_detector import MultiOCRDetector
    MULTI_OCR_AVAILABLE = True
except ImportError:
    print("⚠️ Module multi_ocr_detector non disponible. Veuillez l'installer d'abord.")
    MULTI_OCR_AVAILABLE = False

try:
    from paddleocr import PaddleOCR
    PADDLE_OCR_AVAILABLE = True
except ImportError:
    print("⚠️ Module paddleocr non disponible. Veuillez l'installer d'abord.")
    PADDLE_OCR_AVAILABLE = False

def create_test_images():
    """Crée des images de test pour chaque carte"""
    print("Création des images de test...")
    
    # Créer le dossier de test s'il n'existe pas
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    # Liste des valeurs de cartes
    card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
    
    # Liste des couleurs (BGR)
    colors = {
        'hearts': (0, 0, 255),    # Rouge
        'diamonds': (255, 0, 0),  # Bleu
        'clubs': (0, 128, 0),     # Vert
        'spades': (0, 0, 0)       # Noir
    }
    
    # Créer une image pour chaque combinaison carte/couleur
    for value in card_values:
        for suit, color in colors.items():
            # Créer une image blanche
            img = np.ones((200, 150, 3), dtype=np.uint8) * 255
            
            # Ajouter la valeur de la carte en haut à gauche
            cv2.putText(img, value, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 2, color, 3)
            
            # Ajouter le symbole de la couleur au centre
            symbol = {
                'hearts': '♥',
                'diamonds': '♦',
                'clubs': '♣',
                'spades': '♠'
            }[suit]
            cv2.putText(img, symbol, (60, 120), cv2.FONT_HERSHEY_SIMPLEX, 3, color, 3)
            
            # Sauvegarder l'image
            filename = f"{value.lower()}_{suit}.png"
            cv2.imwrite(str(test_dir / filename), img)
            print(f"Image créée: {filename}")
    
    # Créer des images spéciales pour les cas problématiques
    # J rouge avec différentes nuances
    for i, shade in enumerate([(0, 0, 255), (0, 0, 200), (0, 0, 150)]):
        img = np.ones((200, 150, 3), dtype=np.uint8) * 255
        cv2.putText(img, "J", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 2, shade, 3)
        cv2.putText(img, "♥", (60, 120), cv2.FONT_HERSHEY_SIMPLEX, 3, shade, 3)
        filename = f"j_hearts_shade{i+1}.png"
        cv2.imwrite(str(test_dir / filename), img)
        print(f"Image créée: {filename}")
    
    # J avec différentes polices
    fonts = [cv2.FONT_HERSHEY_SIMPLEX, cv2.FONT_HERSHEY_PLAIN, cv2.FONT_HERSHEY_DUPLEX, 
             cv2.FONT_HERSHEY_COMPLEX, cv2.FONT_HERSHEY_TRIPLEX, cv2.FONT_HERSHEY_SCRIPT_SIMPLEX]
    
    for i, font in enumerate(fonts):
        img = np.ones((200, 150, 3), dtype=np.uint8) * 255
        cv2.putText(img, "J", (10, 50), font, 2, (0, 0, 0), 3)
        filename = f"j_font{i+1}.png"
        cv2.imwrite(str(test_dir / filename), img)
        print(f"Image créée: {filename}")
    
    # Images avec du bruit
    for value in ['J', 'Q']:
        img = np.ones((200, 150, 3), dtype=np.uint8) * 255
        cv2.putText(img, value, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        
        # Ajouter du bruit
        noise = np.random.randint(0, 50, img.shape, dtype=np.uint8)
        img = cv2.add(img, noise)
        
        filename = f"{value.lower()}_noisy.png"
        cv2.imwrite(str(test_dir / filename), img)
        print(f"Image créée: {filename}")
    
    print(f"Images de test créées dans le dossier: {test_dir}")
    return test_dir

def test_paddle_ocr(image_path):
    """Teste la détection avec PaddleOCR"""
    if not PADDLE_OCR_AVAILABLE:
        return "N/A", 0
    
    try:
        # Initialiser PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang='fr', use_gpu=True, show_log=False)
        
        # Charger l'image
        image = cv2.imread(image_path)
        
        # Mesurer le temps de détection
        start_time = time.time()
        result = ocr.ocr(image, cls=True)
        detection_time = time.time() - start_time
        
        # Extraire le texte
        text = ""
        if result and len(result) > 0 and result[0]:
            for line in result[0]:
                text += line[1][0] + " "
            text = text.strip()
        
        return text, detection_time
    except Exception as e:
        print(f"❌ Erreur lors du test PaddleOCR: {e}")
        return "ERROR", 0

def test_multi_ocr(image_path):
    """Teste la détection avec le module multi-OCR"""
    if not MULTI_OCR_AVAILABLE:
        return "N/A", 0
    
    try:
        # Initialiser le détecteur multi-OCR
        detector = MultiOCRDetector()
        
        # Charger l'image
        image = cv2.imread(image_path)
        
        # Mesurer le temps de détection
        start_time = time.time()
        result = detector.detect_card(image)
        detection_time = time.time() - start_time
        
        return result, detection_time
    except Exception as e:
        print(f"❌ Erreur lors du test multi-OCR: {e}")
        return "ERROR", 0

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Test du module multi-OCR pour Poker Advisor")
    parser.add_argument("--create-images", action="store_true", help="Créer des images de test")
    parser.add_argument("--test-dir", type=str, default="test_images", help="Dossier contenant les images de test")
    args = parser.parse_args()
    
    # Créer des images de test si demandé
    if args.create_images:
        test_dir = create_test_images()
    else:
        test_dir = Path(args.test_dir)
        if not test_dir.exists():
            print(f"⚠️ Le dossier {test_dir} n'existe pas. Création des images de test...")
            test_dir = create_test_images()
    
    # Vérifier si les modules sont disponibles
    if not PADDLE_OCR_AVAILABLE and not MULTI_OCR_AVAILABLE:
        print("❌ Aucun module OCR n'est disponible. Veuillez installer PaddleOCR et/ou multi-OCR.")
        return
    
    # Tester chaque image
    results = []
    
    print("\nTest des images...")
    for image_file in sorted(test_dir.glob("*.png")):
        print(f"\nTest de l'image: {image_file.name}")
        
        # Extraire la valeur attendue du nom de fichier
        expected_value = image_file.stem.split('_')[0].upper()
        if expected_value == '10':
            expected_value = '10'
        else:
            expected_value = expected_value[0].upper()
        
        # Tester avec PaddleOCR
        paddle_result, paddle_time = test_paddle_ocr(str(image_file))
        
        # Tester avec multi-OCR
        multi_result, multi_time = test_multi_ocr(str(image_file))
        
        # Afficher les résultats
        print(f"Valeur attendue: {expected_value}")
        print(f"PaddleOCR: {paddle_result} (temps: {paddle_time:.2f}s)")
        print(f"Multi-OCR: {multi_result} (temps: {multi_time:.2f}s)")
        
        # Évaluer les résultats
        paddle_correct = expected_value in paddle_result.upper()
        multi_correct = expected_value in multi_result.upper()
        
        if paddle_correct and multi_correct:
            print("✅ Les deux méthodes ont correctement détecté la carte")
        elif paddle_correct:
            print("⚠️ Seul PaddleOCR a correctement détecté la carte")
        elif multi_correct:
            print("⚠️ Seul multi-OCR a correctement détecté la carte")
        else:
            print("❌ Aucune méthode n'a correctement détecté la carte")
        
        # Stocker les résultats
        results.append({
            "image": image_file.name,
            "expected": expected_value,
            "paddle_result": paddle_result,
            "paddle_time": paddle_time,
            "paddle_correct": paddle_correct,
            "multi_result": multi_result,
            "multi_time": multi_time,
            "multi_correct": multi_correct
        })
    
    # Afficher le résumé
    print("\n" + "=" * 80)
    print(" Résumé des tests ".center(80, "="))
    print("=" * 80)
    
    total = len(results)
    paddle_correct = sum(1 for r in results if r["paddle_correct"])
    multi_correct = sum(1 for r in results if r["multi_correct"])
    
    print(f"Total des images testées: {total}")
    print(f"PaddleOCR: {paddle_correct}/{total} correct ({paddle_correct/total*100:.1f}%)")
    print(f"Multi-OCR: {multi_correct}/{total} correct ({multi_correct/total*100:.1f}%)")
    
    # Afficher les temps moyens
    paddle_avg_time = sum(r["paddle_time"] for r in results) / total
    multi_avg_time = sum(r["multi_time"] for r in results) / total
    
    print(f"Temps moyen PaddleOCR: {paddle_avg_time:.2f}s")
    print(f"Temps moyen multi-OCR: {multi_avg_time:.2f}s")
    
    # Afficher les cas où multi-OCR est meilleur
    better_with_multi = [r for r in results if r["multi_correct"] and not r["paddle_correct"]]
    if better_with_multi:
        print("\nCas où multi-OCR est meilleur:")
        for r in better_with_multi:
            print(f"- {r['image']}: attendu={r['expected']}, PaddleOCR={r['paddle_result']}, multi-OCR={r['multi_result']}")
    
    # Afficher les cas où PaddleOCR est meilleur
    better_with_paddle = [r for r in results if r["paddle_correct"] and not r["multi_correct"]]
    if better_with_paddle:
        print("\nCas où PaddleOCR est meilleur:")
        for r in better_with_paddle:
            print(f"- {r['image']}: attendu={r['expected']}, PaddleOCR={r['paddle_result']}, multi-OCR={r['multi_result']}")
    
    print("\nTest terminé!")

if __name__ == "__main__":
    main()
