#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test simple de capture d'écran
==============================

Ce script teste la capture d'écran et la détection de base
pour identifier les problèmes qui font planter l'application.

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

# Fonction de capture d'écran (copie de detector_gui.py)
try:
    import mss
    SCREEN_CAPTURE_AVAILABLE = True
    print("✅ Module mss importé avec succès")
except ImportError:
    SCREEN_CAPTURE_AVAILABLE = False
    print("❌ Module mss non disponible")

def capture_screen(region=None):
    """Capture l'écran ou une région spécifique"""
    try:
        print("🔍 Tentative de capture d'écran avec mss...")

        with mss.mss() as sct:
            # Si aucune région n'est spécifiée, capturer tout l'écran
            if region is None:
                region = sct.monitors[1]  # Le moniteur principal
                print(f"🔍 Capture du moniteur principal: {region}")

            # Capturer l'écran
            sct_img = sct.grab(region)

            # Convertir en tableau numpy
            img = np.array(sct_img)

            # Convertir de BGRA à BGR (supprimer le canal alpha)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

            print(f"✅ Capture d'écran réussie: {img.shape}")
            return img

    except Exception as e:
        import traceback
        print(f"❌ Erreur lors de la capture d'écran: {e}")
        print(f"❌ Détails de l'erreur: {traceback.format_exc()}")

        # En cas d'erreur, retourner une image noire de taille 1920x1080
        print("⚠️ Retour d'une image noire de secours")
        return np.zeros((1080, 1920, 3), dtype=np.uint8)

def test_capture_and_detection():
    """Teste la capture d'écran et la détection de base"""
    print("🧪 TEST DE CAPTURE ET DÉTECTION SIMPLE")
    print("=" * 50)
    
    if not SCREEN_CAPTURE_AVAILABLE:
        print("❌ Capture d'écran non disponible")
        return
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return
    
    # Initialiser le détecteur
    try:
        print("🔧 Initialisation du détecteur...")
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return
    
    # Test 1: Capture d'écran simple
    print("\n🔍 TEST 1: Capture d'écran simple")
    try:
        frame = capture_screen()
        if frame is not None:
            print(f"✅ Capture réussie: {frame.shape}")
        else:
            print("❌ Capture a retourné None")
            return
    except Exception as e:
        print(f"❌ Erreur lors de la capture: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return
    
    # Test 2: Détection avec mode séquentiel (pas de parallélisme)
    print("\n🔍 TEST 2: Détection séquentielle (sans parallélisme)")
    try:
        results = detector.process_image_direct(frame, fast_mode=True, parallel=False)
        if results is not None:
            print(f"✅ Détection réussie: {len(results)} régions traitées")
            
            # Afficher quelques résultats
            count = 0
            for region_name, data in results.items():
                if count < 3:  # Afficher seulement les 3 premières
                    text = data.get('text', 'Aucun')
                    colors = data.get('colors', [])
                    print(f"   {region_name}: texte='{text}', couleurs={colors}")
                    count += 1
                    
        else:
            print("❌ Détection a retourné None")
            return
    except Exception as e:
        print(f"❌ Erreur lors de la détection séquentielle: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return
    
    # Test 3: Détection avec parallélisme
    print("\n🔍 TEST 3: Détection parallèle")
    try:
        results = detector.process_image_direct(frame, fast_mode=True, parallel=True)
        if results is not None:
            print(f"✅ Détection parallèle réussie: {len(results)} régions traitées")
        else:
            print("❌ Détection parallèle a retourné None")
            return
    except Exception as e:
        print(f"❌ Erreur lors de la détection parallèle: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        print("⚠️ Le parallélisme semble poser problème")
        return
    
    # Test 4: Test spécifique de la région mes_jetons
    print("\n🔍 TEST 4: Test spécifique de la région 'mes_jetons'")
    try:
        if 'mes_jetons' in results:
            mes_jetons_data = results['mes_jetons']
            text = mes_jetons_data.get('text', 'Aucun')
            colors = mes_jetons_data.get('colors', [])
            print(f"✅ Région 'mes_jetons' trouvée:")
            print(f"   Texte: '{text}'")
            print(f"   Couleurs: {colors}")
            
            if text and text != 'Aucun' and text.strip():
                print("✅ Texte détecté avec succès!")
            else:
                print("⚠️ Aucun texte détecté dans mes_jetons")
        else:
            print("⚠️ Région 'mes_jetons' non trouvée dans les résultats")
    except Exception as e:
        print(f"❌ Erreur lors du test de mes_jetons: {e}")
    
    print("\n" + "=" * 50)
    print("✅ TOUS LES TESTS TERMINÉS AVEC SUCCÈS!")
    print("L'application ne devrait plus planter lors de la capture.")

if __name__ == "__main__":
    test_capture_and_detection()
    input("\nAppuyez sur Entrée pour fermer...")
