#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST DU TRACKER POKER INTELLIGENT
====================================

Script de test pour vérifier le fonctionnement du tracker intelligent.
"""

import os
import sys
from poker_tracker_intelligent import IntelligentTracker, quick_scan_and_analyze

def test_tracker_basic():
    """Test basique du tracker"""
    print("🧪 Test basique du tracker")
    print("-" * 30)
    
    try:
        # Créer le tracker
        tracker = IntelligentTracker()
        print("✅ Tracker créé avec succès")
        
        # Vérifier le chemin d'historique
        if os.path.exists(tracker.history_path):
            print(f"✅ Dossier d'historique trouvé: {tracker.history_path}")
            
            # Lister les fichiers
            files = [f for f in os.listdir(tracker.history_path) 
                    if f.endswith('.txt') and not f.endswith('_summary.txt')]
            print(f"📄 {len(files)} fichiers d'historique trouvés")
            
            if files:
                print("📋 Exemples de fichiers:")
                for file in files[:3]:
                    print(f"  • {file}")
            
        else:
            print(f"❌ Dossier d'historique introuvable: {tracker.history_path}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_parsing():
    """Test du parsing des fichiers"""
    print("\n🧪 Test du parsing")
    print("-" * 30)
    
    try:
        tracker = IntelligentTracker()
        
        # Trouver un fichier de test
        if not os.path.exists(tracker.history_path):
            print("❌ Pas de fichiers à parser")
            return False
            
        files = [f for f in os.listdir(tracker.history_path) 
                if f.endswith('.txt') and not f.endswith('_summary.txt')]
        
        if not files:
            print("❌ Aucun fichier d'historique trouvé")
            return False
            
        # Parser le premier fichier
        test_file = os.path.join(tracker.history_path, files[0])
        print(f"📄 Test parsing: {files[0]}")
        
        hands = tracker.parser.parse_file(test_file)
        print(f"✅ {len(hands)} mains parsées")
        
        if hands:
            # Afficher des infos sur la première main
            first_hand = hands[0]
            print(f"📋 Première main:")
            print(f"  • ID: {first_hand.hand_id}")
            print(f"  • Tournoi: {first_hand.tournament}")
            print(f"  • Joueurs: {len(first_hand.players)}")
            print(f"  • Héros: {first_hand.hero}")
            print(f"  • Cartes héros: {first_hand.hero_cards}")
            print(f"  • Board: {first_hand.board}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur parsing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_analysis():
    """Test de l'analyse complète"""
    print("\n🧪 Test de l'analyse complète")
    print("-" * 30)
    
    try:
        # Utiliser la fonction de scan rapide
        summary = quick_scan_and_analyze()
        
        print(f"✅ Analyse terminée")
        print(f"👥 {summary['total_players']} joueurs analysés")
        
        if summary['top_players']:
            print("\n🏆 Top 3 joueurs:")
            for i, player in enumerate(summary['top_players'][:3], 1):
                print(f"{i}. {player['name']}: {player['hands']} mains, "
                      f"VPIP: {player['vpip']:.1f}%, PFR: {player['pfr']:.1f}%")
        
        if summary['most_aggressive']:
            agg = summary['most_aggressive']
            print(f"\n🔥 Plus agressif: {agg['name']} (AF: {agg['aggression_factor']:.2f})")
            
        if summary['most_passive']:
            pas = summary['most_passive']
            print(f"🛡️ Plus passif: {pas['name']} (AF: {pas['aggression_factor']:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_player_profile():
    """Test du profiling d'un joueur"""
    print("\n🧪 Test du profiling joueur")
    print("-" * 30)
    
    try:
        tracker = IntelligentTracker()
        
        # Scanner d'abord
        players = tracker.scan_history_files()
        
        if not players:
            print("❌ Aucun joueur trouvé")
            return False
            
        # Prendre le joueur avec le plus de mains
        top_player = max(players.values(), key=lambda x: x.hands_played)
        player_name = top_player.name
        
        print(f"🎭 Analyse de: {player_name}")
        
        # Obtenir le profil complet
        profile = tracker.get_player_profile(player_name)
        
        if profile:
            stats = profile['stats']
            style = profile['style']
            
            print(f"📊 Statistiques:")
            print(f"  • Mains: {stats.hands_played}")
            print(f"  • VPIP: {stats.vpip:.1f}%")
            print(f"  • PFR: {stats.pfr:.1f}%")
            print(f"  • AF: {stats.aggression_factor:.2f}")
            
            print(f"\n🎭 Style: {style['type']}")
            print(f"📝 Description: {style['description']}")
            print(f"🎯 Confiance: {style['confidence']*100:.0f}%")
            
            print(f"\n💡 Recommandations:")
            for rec in profile['recommendations'][:3]:
                print(f"  • {rec}")
                
            # Test HUD data
            hud_data = tracker.get_hud_data(player_name)
            print(f"\n📱 Données HUD:")
            print(f"  • VPIP/PFR/AF: {hud_data['vpip']}/{hud_data['pfr']}/{hud_data['af']}")
            print(f"  • Style: {hud_data['style']}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur profiling: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_intelligence():
    """Test de l'intelligence de table"""
    print("\n🧪 Test de l'intelligence de table")
    print("-" * 30)
    
    try:
        tracker = IntelligentTracker()
        
        # Scanner d'abord
        players = tracker.scan_history_files()
        
        if not players:
            print("❌ Aucun joueur trouvé")
            return False
            
        # Simuler une table avec les 4 premiers joueurs
        player_names = list(players.keys())[:4]
        print(f"🎲 Table simulée avec: {', '.join(player_names)}")
        
        # Analyser la table
        table_analysis = tracker.get_table_intelligence(player_names)
        
        print(f"🎯 Style de table: {table_analysis['table_style']}")
        
        if table_analysis['recommendations']:
            print(f"\n💡 Recommandations table:")
            for rec in table_analysis['recommendations']:
                print(f"  • {rec}")
        
        if table_analysis['alerts']:
            print(f"\n⚠️ Alertes:")
            for alert in table_analysis['alerts']:
                print(f"  • {alert}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur intelligence table: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🎯 TEST COMPLET DU TRACKER POKER INTELLIGENT")
    print("=" * 60)
    
    tests = [
        ("Test basique", test_tracker_basic),
        ("Test parsing", test_parsing),
        ("Test analyse complète", test_full_analysis),
        ("Test profiling joueur", test_player_profile),
        ("Test intelligence table", test_table_intelligence)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: SUCCÈS")
            else:
                print(f"❌ {test_name}: ÉCHEC")
                
        except Exception as e:
            print(f"💥 {test_name}: ERREUR - {e}")
            results.append((test_name, False))
    
    # Résumé final
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES TESTS")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Résultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés! Le tracker est prêt à l'emploi.")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez la configuration.")

if __name__ == "__main__":
    main()
