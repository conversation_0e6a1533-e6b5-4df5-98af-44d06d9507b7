@echo off
echo ===================================================
echo TEST RAPIDE CUDA POUR POKER ADVISOR
echo ===================================================
echo.

echo Verification de CUDA...
python -c "import torch; print(f'✅ CUDA disponible: {torch.cuda.is_available()}'); print(f'✅ Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"CPU\"}')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PyTorch n'est pas installe ou CUDA n'est pas disponible.
    pause
    exit /b 1
)

echo.
echo Verification de EasyOCR...
python -c "import easyocr; print('✅ EasyOCR disponible')"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ EasyOCR n'est pas installe.
    pause
    exit /b 1
)

echo.
echo Test du détecteur CUDA optimisé...
python detector_cuda_optimized.py
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur lors du test du détecteur.
    pause
    exit /b 1
)

echo.
echo ===================================================
echo ✅ TOUS LES TESTS PASSÉS !
echo ===================================================
echo Votre application poker est prête à utiliser CUDA
echo Performance attendue: ~43 images/seconde
echo ===================================================
echo.

pause
