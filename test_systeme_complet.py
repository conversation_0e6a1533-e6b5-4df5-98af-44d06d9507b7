#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test du système complet avec toutes les fonctionnalités avancées
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

def test_modules_individuels():
    """Test de chaque module individuellement"""
    print("🧪 TEST DES MODULES INDIVIDUELS")
    print("=" * 60)
    
    # Test Monte Carlo
    try:
        from monte_carlo_simulator import MonteCarloSimulator
        simulator = MonteCarloSimulator()
        
        result = simulator.simulate_equity(
            hero_hand=['As', 'Kd'],
            villain_ranges=[['AA'], ['KK']],
            num_simulations=1000
        )
        
        if "error" not in result:
            print(f"✅ Monte Carlo: {result['equity']:.1f}% équité")
        else:
            print(f"❌ Monte Carlo: {result['error']}")
    except Exception as e:
        print(f"❌ Monte Carlo: Erreur d'import - {e}")
    
    # Test Range Analyzer
    try:
        from range_analyzer import RangeAnalyzer
        analyzer = RangeAnalyzer()
        
        result = analyzer.analyze_range_vs_range("UTG_tight", "BTN_tight")
        
        if "error" not in result:
            print(f"✅ Range Analyzer: {result['average_equity']:.1f}% équité moyenne")
        else:
            print(f"❌ Range Analyzer: {result['error']}")
    except Exception as e:
        print(f"❌ Range Analyzer: Erreur d'import - {e}")
    
    # Test GTO Solver
    try:
        from gto_solver import GTOSolver
        solver = GTOSolver()
        
        situation = {
            "position": "BTN",
            "stack_size": 100,
            "pot_size": 15,
            "action_to_call": 5
        }
        
        result = solver.solve_situation(situation)
        
        if "error" not in result:
            main_action = max(result["mixed_strategy"].items(), key=lambda x: x[1])
            print(f"✅ GTO Solver: {main_action[0]} ({main_action[1]*100:.0f}%)")
        else:
            print(f"❌ GTO Solver: {result['error']}")
    except Exception as e:
        print(f"❌ GTO Solver: Erreur d'import - {e}")
    
    # Test Variance Calculator
    try:
        from variance_calculator import VarianceCalculator
        calculator = VarianceCalculator()
        
        result = calculator.calculate_variance_metrics(
            game_type="cash_6max",
            winrate=5.0,
            hands_played=1000
        )
        
        if "error" not in result:
            print(f"✅ Variance Calculator: {result['expected_result']:.0f} BB attendu")
        else:
            print(f"❌ Variance Calculator: {result['error']}")
    except Exception as e:
        print(f"❌ Variance Calculator: Erreur d'import - {e}")
    
    # Test Session Analyzer
    try:
        from session_analyzer import SessionAnalyzer
        analyzer = SessionAnalyzer()
        
        test_hands = [
            {"position": "BTN", "vpip": True, "pfr": True, "result": 5},
            {"position": "UTG", "vpip": False, "pfr": False, "result": -2},
        ]
        
        session_data = {"hands": test_hands, "duration": 60}
        result = analyzer.analyze_session(session_data)
        
        if "error" not in result:
            print(f"✅ Session Analyzer: Score {result['performance_score']}/100")
        else:
            print(f"❌ Session Analyzer: {result['error']}")
    except Exception as e:
        print(f"❌ Session Analyzer: Erreur d'import - {e}")

def test_integration_complete():
    """Test de l'intégration complète dans le conseiller"""
    print("\n🧪 TEST D'INTÉGRATION COMPLÈTE")
    print("=" * 60)
    
    try:
        from poker_advisor_light import PokerAdvisorLight
        
        advisor = PokerAdvisorLight()
        
        print(f"Modules avancés disponibles: {advisor.advanced_features_enabled}")
        
        if advisor.advanced_features_enabled:
            print("✅ Tous les modules avancés sont chargés")
            
            # Test avec une situation complète
            simulated_results = {
                # Main premium
                "carte_1m": {"text": "As", "colors": ["red"]},
                "carte_2m": {"text": "Kd", "colors": ["black"]},
                
                # Board
                "carte_1b": {"text": "Ah", "colors": ["red"]},
                "carte_2b": {"text": "5h", "colors": ["red"]},
                "carte_3b": {"text": "2c", "colors": ["black"]},
                
                # Stack et adversaires
                "mes_jetons": {"text": "500", "colors": []},
                "jetons_joueur1": {"text": "300", "colors": ["white"]},
                "mise_joueur1": {"text": "20", "colors": ["white"]},
                "pot_total": {"text": "50", "colors": ["white"]},
            }
            
            print("\n📊 Analyse avec fonctionnalités avancées...")
            analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
            
            # Vérifier que l'analyse avancée est présente
            if "advanced_analysis" in analysis:
                print("✅ Analyse avancée générée")
                
                adv_analysis = analysis["advanced_analysis"]
                
                # Vérifier chaque composant
                components = ["monte_carlo", "range_analysis", "gto_solution", "variance_metrics"]
                for component in components:
                    if component in adv_analysis and "error" not in adv_analysis[component]:
                        print(f"   ✅ {component}: OK")
                    else:
                        print(f"   ⚠️ {component}: Non disponible ou erreur")
                
                # Vérifier les insights et recommandations
                if adv_analysis.get("insights"):
                    print(f"   ✅ Insights: {len(adv_analysis['insights'])} générés")
                
                if adv_analysis.get("recommendations"):
                    print(f"   ✅ Recommandations: {len(adv_analysis['recommendations'])} générées")
            else:
                print("❌ Analyse avancée non générée")
            
            # Vérifier l'affichage formaté
            if "🚀 **ANALYSE AVANCÉE** 🚀" in formatted_analysis:
                print("✅ Affichage avancé intégré dans le conseiller")
                
                # Compter les sections avancées
                sections = ["Monte Carlo", "Range vs Range", "Solution GTO", "Analyse de Variance"]
                sections_found = sum(1 for section in sections if section in formatted_analysis)
                print(f"   📊 Sections avancées affichées: {sections_found}/{len(sections)}")
            else:
                print("⚠️ Affichage avancé non intégré")
            
            # Afficher un extrait de l'analyse
            print(f"\n📋 EXTRAIT DE L'ANALYSE AVANCÉE:")
            lines = formatted_analysis.split('\n')
            in_advanced_section = False
            line_count = 0
            
            for line in lines:
                if "🚀 **ANALYSE AVANCÉE** 🚀" in line:
                    in_advanced_section = True
                    print(f"   {line}")
                    line_count += 1
                elif in_advanced_section and line_count < 10:
                    print(f"   {line}")
                    line_count += 1
                    if line.strip() == "" and line_count > 5:
                        break
        else:
            print("❌ Modules avancés non disponibles")
            print("   Fonctionnalités de base uniquement")
    
    except Exception as e:
        print(f"❌ Erreur lors du test d'intégration: {e}")

def test_performance():
    """Test de performance du système complet"""
    print("\n🧪 TEST DE PERFORMANCE")
    print("=" * 60)
    
    try:
        from poker_advisor_light import PokerAdvisorLight
        import time
        
        advisor = PokerAdvisorLight()
        
        if not advisor.advanced_features_enabled:
            print("⚠️ Modules avancés non disponibles - test de performance limité")
            return
        
        # Situation de test
        simulated_results = {
            "carte_1m": {"text": "As", "colors": ["red"]},
            "carte_2m": {"text": "Kd", "colors": ["black"]},
            "mes_jetons": {"text": "1000", "colors": []},
            "pot_total": {"text": "100", "colors": ["white"]},
        }
        
        # Test de performance
        start_time = time.time()
        
        for i in range(5):  # 5 analyses
            analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 5
        
        print(f"⏱️ Temps moyen par analyse: {avg_time:.2f}s")
        
        if avg_time < 1.0:
            print("✅ Performance excellente (< 1s)")
        elif avg_time < 3.0:
            print("✅ Performance acceptable (< 3s)")
        else:
            print("⚠️ Performance lente (> 3s)")
        
        # Test de cache
        start_time = time.time()
        analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
        cache_time = time.time() - start_time
        
        print(f"⚡ Temps avec cache: {cache_time:.3f}s")
        
        if cache_time < avg_time / 2:
            print("✅ Cache efficace")
        else:
            print("⚠️ Cache peu efficace")
    
    except Exception as e:
        print(f"❌ Erreur lors du test de performance: {e}")

def test_robustesse():
    """Test de robustesse avec des données invalides"""
    print("\n🧪 TEST DE ROBUSTESSE")
    print("=" * 60)
    
    try:
        from poker_advisor_light import PokerAdvisorLight
        
        advisor = PokerAdvisorLight()
        
        # Test avec données vides
        empty_results = {}
        analysis, formatted_analysis = advisor.analyze_detection_results(empty_results)
        print("✅ Gestion des données vides: OK")
        
        # Test avec données partielles
        partial_results = {
            "carte_1m": {"text": "As", "colors": ["red"]},
            # Manque carte_2m
        }
        analysis, formatted_analysis = advisor.analyze_detection_results(partial_results)
        print("✅ Gestion des données partielles: OK")
        
        # Test avec données corrompues
        corrupted_results = {
            "carte_1m": {"text": "InvalidCard", "colors": ["invalid"]},
            "carte_2m": None,
            "mes_jetons": {"text": "not_a_number", "colors": []},
        }
        analysis, formatted_analysis = advisor.analyze_detection_results(corrupted_results)
        print("✅ Gestion des données corrompues: OK")
        
        print("✅ Système robuste - gère tous les cas d'erreur")
    
    except Exception as e:
        print(f"❌ Erreur lors du test de robustesse: {e}")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS DU SYSTÈME COMPLET")
    print("=" * 60)
    
    # Tests individuels
    test_modules_individuels()
    
    # Test d'intégration
    test_integration_complete()
    
    # Test de performance
    test_performance()
    
    # Test de robustesse
    test_robustesse()
    
    print("\n✅ TESTS TERMINÉS")
    print("=" * 60)
    print("🎯 Le système complet avec toutes les fonctionnalités avancées est prêt !")
    print("📊 Monte Carlo, Range vs Range, GTO Solver, Variance Calculator, Session Analyzer")
    print("🎮 Intégré visuellement dans le conseiller poker")
    print("⚡ Optimisé pour les performances et la robustesse")
