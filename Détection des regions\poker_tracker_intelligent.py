#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 TRACKER POKER INTELLIGENT
============================

Système de tracking avancé qui analyse les fichiers d'historique Winamax
pour créer des profils détaillés des adversaires et améliorer l'intelligence
du conseiller poker.

Fonctionnalités :
- 📊 Analyse des fichiers d'historique Winamax
- 🎭 Profiling automatique des adversaires (VPIP/PFR/3bet/etc.)
- 🧠 Intelligence adaptative basée sur les patterns de jeu
- 📈 Base de données persistante des joueurs
- 🎯 Recommandations personnalisées selon l'historique
- 🔥 Détection du tilt et changements de comportement
- 📱 HUD (Heads-Up Display) en temps réel

Auteur: Assistant IA pour PokerAdvisor
Date: 2025-01-29
"""

import os
import re
import json
import sqlite3
import time
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any
import threading
from pathlib import Path

@dataclass
class PlayerStats:
    """Statistiques détaillées d'un joueur"""
    name: str
    hands_played: int = 0
    vpip: float = 0.0  # Voluntary Put In Pot
    pfr: float = 0.0   # Pre-Flop Raise
    three_bet: float = 0.0
    fold_to_three_bet: float = 0.0
    cbet: float = 0.0  # Continuation Bet
    fold_to_cbet: float = 0.0
    aggression_factor: float = 0.0
    wtsd: float = 0.0  # Went To ShowDown
    w_sd: float = 0.0  # Won at ShowDown

    # Statistiques par position
    position_stats: Dict[str, Dict] = None

    # Tendances récentes
    recent_hands: List[Dict] = None
    tilt_indicators: Dict[str, float] = None

    # Métadonnées
    last_seen: str = ""
    total_winnings: float = 0.0
    session_count: int = 0

    def __post_init__(self):
        if self.position_stats is None:
            self.position_stats = {}
        if self.recent_hands is None:
            self.recent_hands = []
        if self.tilt_indicators is None:
            self.tilt_indicators = {
                "aggression_spike": 0.0,
                "loose_play": 0.0,
                "erratic_betting": 0.0
            }

@dataclass
class HandData:
    """Données d'une main de poker"""
    hand_id: str
    timestamp: str
    tournament: str
    level: str
    blinds: Tuple[int, int, int]  # ante, sb, bb
    table: str
    button_seat: int

    # Joueurs et positions
    players: Dict[int, str]  # seat -> name
    stacks: Dict[str, int]   # name -> stack

    # Actions de la main
    hero: str
    hero_cards: List[str]
    board: List[str]

    # Actions par street
    preflop_actions: List[Dict]
    flop_actions: List[Dict]
    turn_actions: List[Dict]
    river_actions: List[Dict]

    # Résultats
    pot_size: int
    winners: Dict[str, int]  # name -> amount won
    showdown: Dict[str, List[str]]  # name -> cards shown

class WinamaxParser:
    """Parser pour les fichiers d'historique Winamax"""

    def __init__(self):
        self.hand_pattern = re.compile(
            r'Winamax Poker - Tournament "([^"]+)" buyIn: ([^-]+) - HandId: #([^-]+)-(\d+)-(\d+) - '
            r'Holdem no limit \(([^)]+)\) - ([^U]+UTC)'
        )
        self.table_pattern = re.compile(r"Table: '([^']+)' \d+-max \(real money\) Seat #(\d+) is the button")
        self.seat_pattern = re.compile(r"Seat (\d+): ([^(]+) \((\d+)\)")
        self.dealt_pattern = re.compile(r"Dealt to ([^[]+) \[([^\]]+)\]")
        self.action_pattern = re.compile(r"([^:]+): (folds|calls|bets|raises|checks|shows|collected)")

    def parse_file(self, file_path: str) -> List[HandData]:
        """Parse un fichier d'historique Winamax"""
        hands = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Diviser en mains individuelles
            hand_texts = content.split('\n\n\n')

            for hand_text in hand_texts:
                if not hand_text.strip():
                    continue

                try:
                    hand_data = self._parse_single_hand(hand_text)
                    if hand_data:
                        hands.append(hand_data)
                except Exception as e:
                    print(f"⚠️ Erreur parsing main: {e}")
                    continue

        except Exception as e:
            print(f"❌ Erreur lecture fichier {file_path}: {e}")

        return hands

    def _parse_single_hand(self, hand_text: str) -> Optional[HandData]:
        """Parse une main individuelle"""
        lines = hand_text.strip().split('\n')
        if not lines:
            return None

        # Parse header
        header_match = self.hand_pattern.match(lines[0])
        if not header_match:
            return None

        tournament = header_match.group(1)
        hand_id = f"{header_match.group(3)}-{header_match.group(4)}-{header_match.group(5)}"
        blinds_str = header_match.group(6)
        timestamp = header_match.group(7).strip()

        # Parse blinds (ante/sb/bb)
        blinds_parts = blinds_str.split('/')
        if len(blinds_parts) == 3:
            ante, sb, bb = map(int, blinds_parts)
        else:
            ante, sb, bb = 0, int(blinds_parts[0]), int(blinds_parts[1])

        # Parse table info
        table_match = self.table_pattern.match(lines[1])
        if not table_match:
            return None

        table = table_match.group(1)
        button_seat = int(table_match.group(2))

        # Parse seats and stacks
        players = {}
        stacks = {}
        line_idx = 2

        while line_idx < len(lines) and lines[line_idx].startswith('Seat '):
            seat_match = self.seat_pattern.match(lines[line_idx])
            if seat_match:
                seat = int(seat_match.group(1))
                name = seat_match.group(2).strip()
                stack = int(seat_match.group(3))
                players[seat] = name
                stacks[name] = stack
            line_idx += 1

        # Parse dealt cards
        hero = ""
        hero_cards = []

        while line_idx < len(lines):
            if lines[line_idx].startswith('Dealt to '):
                dealt_match = self.dealt_pattern.match(lines[line_idx])
                if dealt_match:
                    hero = dealt_match.group(1).strip()
                    hero_cards = dealt_match.group(2).split()
                break
            line_idx += 1

        # Parse actions et board
        preflop_actions = []
        flop_actions = []
        turn_actions = []
        river_actions = []
        board = []

        current_street = "preflop"

        for line in lines[line_idx:]:
            line = line.strip()

            if line.startswith('*** FLOP ***'):
                current_street = "flop"
                board_match = re.search(r'\[([^\]]+)\]', line)
                if board_match:
                    board.extend(board_match.group(1).split())
            elif line.startswith('*** TURN ***'):
                current_street = "turn"
                board_match = re.search(r'\[([^\]]+)\]', line)
                if board_match:
                    board.extend(board_match.group(1).split())
            elif line.startswith('*** RIVER ***'):
                current_street = "river"
                board_match = re.search(r'\[([^\]]+)\]', line)
                if board_match:
                    board.extend(board_match.group(1).split())
            elif line.startswith('*** SUMMARY ***'):
                break
            elif ':' in line and any(action in line for action in ['folds', 'calls', 'bets', 'raises', 'checks']):
                action_data = self._parse_action(line)
                if action_data:
                    if current_street == "preflop":
                        preflop_actions.append(action_data)
                    elif current_street == "flop":
                        flop_actions.append(action_data)
                    elif current_street == "turn":
                        turn_actions.append(action_data)
                    elif current_street == "river":
                        river_actions.append(action_data)

        # Parse pot size et winners (simplifié pour l'instant)
        pot_size = 0
        winners = {}
        showdown = {}

        return HandData(
            hand_id=hand_id,
            timestamp=timestamp,
            tournament=tournament,
            level="",  # À parser si nécessaire
            blinds=(ante, sb, bb),
            table=table,
            button_seat=button_seat,
            players=players,
            stacks=stacks,
            hero=hero,
            hero_cards=hero_cards,
            board=board,
            preflop_actions=preflop_actions,
            flop_actions=flop_actions,
            turn_actions=turn_actions,
            river_actions=river_actions,
            pot_size=pot_size,
            winners=winners,
            showdown=showdown
        )

    def _parse_action(self, line: str) -> Optional[Dict]:
        """Parse une action de joueur"""
        try:
            if ':' not in line:
                return None

            player_part, action_part = line.split(':', 1)
            player = player_part.strip()
            action_part = action_part.strip()

            action_data = {
                'player': player,
                'action': '',
                'amount': 0
            }

            if 'folds' in action_part:
                action_data['action'] = 'fold'
            elif 'checks' in action_part:
                action_data['action'] = 'check'
            elif 'calls' in action_part:
                action_data['action'] = 'call'
                amount_match = re.search(r'calls (\d+)', action_part)
                if amount_match:
                    action_data['amount'] = int(amount_match.group(1))
            elif 'bets' in action_part:
                action_data['action'] = 'bet'
                amount_match = re.search(r'bets (\d+)', action_part)
                if amount_match:
                    action_data['amount'] = int(amount_match.group(1))
            elif 'raises' in action_part:
                action_data['action'] = 'raise'
                amount_match = re.search(r'raises \d+ to (\d+)', action_part)
                if amount_match:
                    action_data['amount'] = int(amount_match.group(1))
            else:
                return None

            return action_data

        except Exception as e:
            print(f"⚠️ Erreur parsing action '{line}': {e}")
            return None

class PlayerDatabase:
    """Base de données SQLite pour stocker les statistiques des joueurs"""

    def __init__(self, db_path: str = "poker_players.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialise la base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Table des joueurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS players (
                name TEXT PRIMARY KEY,
                hands_played INTEGER DEFAULT 0,
                vpip REAL DEFAULT 0.0,
                pfr REAL DEFAULT 0.0,
                three_bet REAL DEFAULT 0.0,
                fold_to_three_bet REAL DEFAULT 0.0,
                cbet REAL DEFAULT 0.0,
                fold_to_cbet REAL DEFAULT 0.0,
                aggression_factor REAL DEFAULT 0.0,
                wtsd REAL DEFAULT 0.0,
                w_sd REAL DEFAULT 0.0,
                last_seen TEXT,
                total_winnings REAL DEFAULT 0.0,
                session_count INTEGER DEFAULT 0,
                position_stats TEXT,
                tilt_indicators TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Table des mains
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hands (
                hand_id TEXT PRIMARY KEY,
                timestamp TEXT,
                tournament TEXT,
                hero TEXT,
                players TEXT,
                actions TEXT,
                board TEXT,
                pot_size INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Table des actions individuelles
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS player_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hand_id TEXT,
                player_name TEXT,
                position TEXT,
                street TEXT,
                action TEXT,
                amount INTEGER,
                stack_size INTEGER,
                pot_size INTEGER,
                timestamp TEXT,
                FOREIGN KEY (hand_id) REFERENCES hands (hand_id)
            )
        ''')

        conn.commit()
        conn.close()

    def save_player_stats(self, stats: PlayerStats):
        """Sauvegarde les statistiques d'un joueur"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO players (
                name, hands_played, vpip, pfr, three_bet, fold_to_three_bet,
                cbet, fold_to_cbet, aggression_factor, wtsd, w_sd,
                last_seen, total_winnings, session_count,
                position_stats, tilt_indicators, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            stats.name, stats.hands_played, stats.vpip, stats.pfr,
            stats.three_bet, stats.fold_to_three_bet, stats.cbet,
            stats.fold_to_cbet, stats.aggression_factor, stats.wtsd,
            stats.w_sd, stats.last_seen, stats.total_winnings,
            stats.session_count, json.dumps(stats.position_stats),
            json.dumps(stats.tilt_indicators)
        ))

        conn.commit()
        conn.close()

    def get_player_stats(self, name: str) -> Optional[PlayerStats]:
        """Récupère les statistiques d'un joueur"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM players WHERE name = ?', (name,))
        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        # Reconstruction de l'objet PlayerStats
        position_stats = json.loads(row[14]) if row[14] else {}
        tilt_indicators = json.loads(row[15]) if row[15] else {}

        return PlayerStats(
            name=row[0],
            hands_played=row[1],
            vpip=row[2],
            pfr=row[3],
            three_bet=row[4],
            fold_to_three_bet=row[5],
            cbet=row[6],
            fold_to_cbet=row[7],
            aggression_factor=row[8],
            wtsd=row[9],
            w_sd=row[10],
            last_seen=row[11],
            total_winnings=row[12],
            session_count=row[13],
            position_stats=position_stats,
            tilt_indicators=tilt_indicators
        )

    def get_all_players(self) -> List[PlayerStats]:
        """Récupère tous les joueurs"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM players ORDER BY hands_played DESC')
        rows = cursor.fetchall()
        conn.close()

        players = []
        for row in rows:
            position_stats = json.loads(row[14]) if row[14] else {}
            tilt_indicators = json.loads(row[15]) if row[15] else {}

            players.append(PlayerStats(
                name=row[0],
                hands_played=row[1],
                vpip=row[2],
                pfr=row[3],
                three_bet=row[4],
                fold_to_three_bet=row[5],
                cbet=row[6],
                fold_to_cbet=row[7],
                aggression_factor=row[8],
                wtsd=row[9],
                w_sd=row[10],
                last_seen=row[11],
                total_winnings=row[12],
                session_count=row[13],
                position_stats=position_stats,
                tilt_indicators=tilt_indicators
            ))

        return players

class StatsCalculator:
    """Calculateur de statistiques poker"""

    def __init__(self):
        self.position_map = {
            1: "SB", 2: "BB", 3: "UTG", 4: "MP", 5: "CO", 6: "BTN"
        }

    def calculate_player_stats(self, hands: List[HandData], player_name: str) -> PlayerStats:
        """Calcule les statistiques d'un joueur à partir des mains"""
        stats = PlayerStats(name=player_name)

        # Compteurs pour les calculs
        hands_played = 0
        vpip_hands = 0
        pfr_hands = 0
        three_bet_hands = 0
        faced_three_bet = 0
        folded_to_three_bet = 0
        cbet_opportunities = 0
        cbet_made = 0
        faced_cbet = 0
        folded_to_cbet = 0

        aggressive_actions = 0
        passive_actions = 0
        showdowns = 0
        showdowns_won = 0

        total_winnings = 0

        for hand in hands:
            if player_name not in hand.stacks:
                continue

            hands_played += 1

            # Analyser les actions preflop
            player_actions = [a for a in hand.preflop_actions if a['player'] == player_name]

            # VPIP: a mis de l'argent volontairement preflop
            if any(a['action'] in ['call', 'bet', 'raise'] for a in player_actions):
                vpip_hands += 1

            # PFR: a relancé preflop
            if any(a['action'] == 'raise' for a in player_actions):
                pfr_hands += 1

            # Analyser toutes les actions pour l'agressivité
            all_actions = (hand.preflop_actions + hand.flop_actions +
                          hand.turn_actions + hand.river_actions)
            player_all_actions = [a for a in all_actions if a['player'] == player_name]

            for action in player_all_actions:
                if action['action'] in ['bet', 'raise']:
                    aggressive_actions += 1
                elif action['action'] in ['call', 'check']:
                    passive_actions += 1

            # Winnings
            if player_name in hand.winners:
                total_winnings += hand.winners[player_name]

        # Calcul des pourcentages
        if hands_played > 0:
            stats.hands_played = hands_played
            stats.vpip = (vpip_hands / hands_played) * 100
            stats.pfr = (pfr_hands / hands_played) * 100
            stats.total_winnings = total_winnings

            if aggressive_actions + passive_actions > 0:
                stats.aggression_factor = aggressive_actions / max(passive_actions, 1)

        stats.last_seen = datetime.now().isoformat()

        return stats

class IntelligentTracker:
    """Tracker intelligent principal"""

    def __init__(self, history_path: str = None, db_path: str = "poker_players.db"):
        self.history_path = history_path or r"C:\Users\<USER>\PokerAdvisor\accounts\Tomz-666\history"
        self.parser = WinamaxParser()
        self.database = PlayerDatabase(db_path)
        self.calculator = StatsCalculator()
        self.players_cache = {}
        self.last_scan = None

        print(f"🎯 Tracker intelligent initialisé")
        print(f"📁 Dossier historique: {self.history_path}")
        print(f"💾 Base de données: {db_path}")

    def scan_history_files(self, force_rescan: bool = False) -> Dict[str, PlayerStats]:
        """Scanne tous les fichiers d'historique et met à jour les stats"""
        if not os.path.exists(self.history_path):
            print(f"❌ Dossier d'historique introuvable: {self.history_path}")
            return {}

        print(f"🔍 Scan des fichiers d'historique...")

        # Trouver tous les fichiers .txt
        history_files = []
        for file in os.listdir(self.history_path):
            if file.endswith('.txt') and not file.endswith('_summary.txt'):
                history_files.append(os.path.join(self.history_path, file))

        print(f"📄 {len(history_files)} fichiers trouvés")

        # Parser tous les fichiers
        all_hands = []
        for file_path in history_files:
            try:
                hands = self.parser.parse_file(file_path)
                all_hands.extend(hands)
                print(f"✅ {os.path.basename(file_path)}: {len(hands)} mains")
            except Exception as e:
                print(f"❌ Erreur fichier {os.path.basename(file_path)}: {e}")

        print(f"🎯 Total: {len(all_hands)} mains analysées")

        # Extraire tous les noms de joueurs
        all_players = set()
        for hand in all_hands:
            all_players.update(hand.stacks.keys())

        print(f"👥 {len(all_players)} joueurs uniques détectés")

        # Calculer les stats pour chaque joueur
        updated_players = {}
        for player_name in all_players:
            if player_name.strip():  # Ignorer les noms vides
                try:
                    stats = self.calculator.calculate_player_stats(all_hands, player_name)
                    if stats.hands_played > 0:  # Seulement si le joueur a joué des mains
                        self.database.save_player_stats(stats)
                        updated_players[player_name] = stats
                        self.players_cache[player_name] = stats
                except Exception as e:
                    print(f"⚠️ Erreur calcul stats pour {player_name}: {e}")

        self.last_scan = datetime.now()
        print(f"✅ Scan terminé: {len(updated_players)} joueurs mis à jour")

        return updated_players

    def get_player_profile(self, player_name: str) -> Optional[Dict]:
        """Récupère le profil complet d'un joueur avec analyse"""
        stats = self.database.get_player_stats(player_name)
        if not stats:
            return None

        # Analyser le style de jeu
        style = self._analyze_playing_style(stats)

        # Détecter les tendances
        tendencies = self._analyze_tendencies(stats)

        # Recommandations contre ce joueur
        recommendations = self._generate_counter_strategy(stats, style)

        return {
            "stats": stats,
            "style": style,
            "tendencies": tendencies,
            "recommendations": recommendations,
            "last_updated": stats.last_seen
        }

    def _analyze_playing_style(self, stats: PlayerStats) -> Dict[str, Any]:
        """Analyse le style de jeu d'un joueur"""
        style = {
            "type": "Unknown",
            "description": "",
            "color": "#888888",
            "confidence": 0.0
        }

        if stats.hands_played < 10:
            style["type"] = "Échantillon insuffisant"
            style["description"] = f"Seulement {stats.hands_played} mains observées"
            style["confidence"] = 0.1
            return style

        # Classification basée sur VPIP et PFR
        vpip = stats.vpip
        pfr = stats.pfr
        aggression = stats.aggression_factor

        # Déterminer le type de joueur
        if vpip < 15:
            if pfr < 10:
                style["type"] = "Tight-Passive (Nit)"
                style["description"] = "Joue très peu de mains, rarement agressif"
                style["color"] = "#4CAF50"  # Vert
            else:
                style["type"] = "Tight-Aggressive (TAG)"
                style["description"] = "Joue peu de mains mais agressivement"
                style["color"] = "#2196F3"  # Bleu
        elif vpip < 25:
            if pfr < 15:
                style["type"] = "Loose-Passive (Calling Station)"
                style["description"] = "Joue beaucoup de mains passivement"
                style["color"] = "#FF9800"  # Orange
            else:
                style["type"] = "Loose-Aggressive (LAG)"
                style["description"] = "Joue beaucoup de mains agressivement"
                style["color"] = "#F44336"  # Rouge
        else:
            style["type"] = "Maniac"
            style["description"] = "Joue presque toutes les mains très agressivement"
            style["color"] = "#9C27B0"  # Violet

        # Calculer la confiance basée sur le nombre de mains
        if stats.hands_played >= 100:
            style["confidence"] = 0.9
        elif stats.hands_played >= 50:
            style["confidence"] = 0.7
        elif stats.hands_played >= 25:
            style["confidence"] = 0.5
        else:
            style["confidence"] = 0.3

        return style

    def _analyze_tendencies(self, stats: PlayerStats) -> List[Dict]:
        """Analyse les tendances spécifiques d'un joueur"""
        tendencies = []

        # Tendance VPIP
        if stats.vpip > 30:
            tendencies.append({
                "type": "loose_preflop",
                "description": f"Joue beaucoup de mains preflop ({stats.vpip:.1f}%)",
                "severity": "high" if stats.vpip > 40 else "medium"
            })
        elif stats.vpip < 15:
            tendencies.append({
                "type": "tight_preflop",
                "description": f"Très sélectif preflop ({stats.vpip:.1f}%)",
                "severity": "high" if stats.vpip < 10 else "medium"
            })

        # Tendance agressivité
        if stats.aggression_factor > 3:
            tendencies.append({
                "type": "very_aggressive",
                "description": f"Très agressif (AF: {stats.aggression_factor:.1f})",
                "severity": "high"
            })
        elif stats.aggression_factor < 1:
            tendencies.append({
                "type": "passive",
                "description": f"Joueur passif (AF: {stats.aggression_factor:.1f})",
                "severity": "medium"
            })

        # Écart VPIP/PFR
        vpip_pfr_gap = stats.vpip - stats.pfr
        if vpip_pfr_gap > 15:
            tendencies.append({
                "type": "calling_station",
                "description": f"Suit souvent sans relancer (Gap: {vpip_pfr_gap:.1f}%)",
                "severity": "medium"
            })

        return tendencies

    def _generate_counter_strategy(self, stats: PlayerStats, style: Dict) -> List[str]:
        """Génère des recommandations pour jouer contre ce joueur"""
        recommendations = []

        style_type = style["type"]

        if "Tight-Passive" in style_type:
            recommendations.extend([
                "🎯 Volez ses blinds fréquemment",
                "💰 Value bet thin contre lui",
                "🚫 Évitez de bluffer, il suit souvent",
                "⚡ Soyez agressif en position"
            ])

        elif "Tight-Aggressive" in style_type:
            recommendations.extend([
                "⚠️ Respectez ses relances",
                "🎭 Bluffez avec parcimonie",
                "💪 Jouez en position contre lui",
                "🎯 Exploitez sa tightness preflop"
            ])

        elif "Loose-Passive" in style_type:
            recommendations.extend([
                "💰 Value bet large avec vos bonnes mains",
                "🚫 Ne bluffez jamais",
                "⚡ Isolez-le avec des mains moyennes",
                "🎯 Exploitez sa passivité postflop"
            ])

        elif "Loose-Aggressive" in style_type:
            recommendations.extend([
                "🛡️ Jouez plus tight contre lui",
                "💪 Utilisez sa propre agressivité contre lui",
                "🎭 Piégez avec vos nuts",
                "⚠️ Attention aux 3-bets light"
            ])

        elif "Maniac" in style_type:
            recommendations.extend([
                "🛡️ Jouez très tight et patient",
                "💰 Laissez-le se pendre tout seul",
                "🎭 Piégez constamment",
                "⚠️ Ne bluffez jamais"
            ])

        # Recommandations basées sur les stats spécifiques
        if stats.aggression_factor > 3:
            recommendations.append("🔥 Attention: joueur très agressif!")

        if stats.vpip > 35:
            recommendations.append("🎯 Exploitez sa looseness preflop")

        return recommendations

    def get_table_intelligence(self, current_players: List[str]) -> Dict[str, Any]:
        """Analyse intelligente de la table actuelle"""
        table_analysis = {
            "players": {},
            "table_style": "Unknown",
            "recommendations": [],
            "alerts": []
        }

        if not current_players:
            return table_analysis

        # Analyser chaque joueur présent
        known_players = 0
        total_vpip = 0
        total_aggression = 0

        for player_name in current_players:
            profile = self.get_player_profile(player_name)
            if profile:
                table_analysis["players"][player_name] = profile
                known_players += 1
                total_vpip += profile["stats"].vpip
                total_aggression += profile["stats"].aggression_factor
            else:
                # Joueur inconnu
                table_analysis["players"][player_name] = {
                    "stats": None,
                    "style": {"type": "Inconnu", "color": "#888888"},
                    "recommendations": ["🔍 Observez ce joueur attentivement"]
                }

        # Analyser le style général de la table
        if known_players > 0:
            avg_vpip = total_vpip / known_players
            avg_aggression = total_aggression / known_players

            if avg_vpip < 20 and avg_aggression < 2:
                table_analysis["table_style"] = "Tight-Passive"
                table_analysis["recommendations"].extend([
                    "🎯 Table serrée: volez les blinds fréquemment",
                    "💰 Value bet thin",
                    "⚡ Soyez plus agressif que d'habitude"
                ])
            elif avg_vpip > 30 and avg_aggression > 3:
                table_analysis["table_style"] = "Loose-Aggressive"
                table_analysis["recommendations"].extend([
                    "🛡️ Table agressive: jouez plus tight",
                    "🎭 Piégez avec vos bonnes mains",
                    "⚠️ Attention aux 3-bets light"
                ])
            else:
                table_analysis["table_style"] = "Mixte"
                table_analysis["recommendations"].append(
                    "🎯 Table mixte: adaptez-vous à chaque adversaire"
                )

        # Alertes spéciales
        for player_name, profile in table_analysis["players"].items():
            if profile.get("stats"):
                stats = profile["stats"]
                if stats.aggression_factor > 4:
                    table_analysis["alerts"].append(
                        f"🔥 {player_name}: Joueur très agressif (AF: {stats.aggression_factor:.1f})"
                    )
                if stats.vpip > 40:
                    table_analysis["alerts"].append(
                        f"🎯 {player_name}: Très loose ({stats.vpip:.1f}% VPIP)"
                    )

        return table_analysis

    def get_hud_data(self, player_name: str) -> Dict[str, Any]:
        """Données HUD pour affichage en temps réel"""
        stats = self.database.get_player_stats(player_name)
        if not stats:
            return {
                "name": player_name,
                "vpip": "?",
                "pfr": "?",
                "af": "?",
                "hands": 0,
                "style": "Inconnu",
                "color": "#888888"
            }

        style = self._analyze_playing_style(stats)

        return {
            "name": player_name,
            "vpip": f"{stats.vpip:.0f}",
            "pfr": f"{stats.pfr:.0f}",
            "af": f"{stats.aggression_factor:.1f}",
            "hands": stats.hands_played,
            "style": style["type"],
            "color": style["color"],
            "confidence": style["confidence"]
        }

    def export_player_report(self, player_name: str, output_file: str = None) -> str:
        """Exporte un rapport détaillé d'un joueur"""
        profile = self.get_player_profile(player_name)
        if not profile:
            return f"Aucune donnée pour {player_name}"

        stats = profile["stats"]
        style = profile["style"]

        report = f"""
🎯 RAPPORT JOUEUR: {player_name}
{'='*50}

📊 STATISTIQUES GÉNÉRALES:
• Mains jouées: {stats.hands_played}
• VPIP: {stats.vpip:.1f}%
• PFR: {stats.pfr:.1f}%
• Facteur d'agressivité: {stats.aggression_factor:.2f}
• Gains totaux: {stats.total_winnings:+.0f}

🎭 STYLE DE JEU:
• Type: {style['type']}
• Description: {style['description']}
• Confiance: {style['confidence']*100:.0f}%

🎯 RECOMMANDATIONS:
"""
        for rec in profile["recommendations"]:
            report += f"• {rec}\n"

        if profile["tendencies"]:
            report += "\n⚠️ TENDANCES DÉTECTÉES:\n"
            for tendency in profile["tendencies"]:
                report += f"• {tendency['description']}\n"

        report += f"\n📅 Dernière mise à jour: {stats.last_seen}\n"

        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"✅ Rapport sauvegardé: {output_file}")
            except Exception as e:
                print(f"❌ Erreur sauvegarde: {e}")

        return report

# Fonctions utilitaires pour l'intégration
def create_tracker_instance(history_path: str = None) -> IntelligentTracker:
    """Crée une instance du tracker intelligent"""
    return IntelligentTracker(history_path)

def quick_scan_and_analyze(history_path: str = None) -> Dict[str, Any]:
    """Scan rapide et analyse des joueurs"""
    tracker = create_tracker_instance(history_path)
    players = tracker.scan_history_files()

    # Résumé des résultats
    summary = {
        "total_players": len(players),
        "top_players": [],
        "most_aggressive": None,
        "most_passive": None,
        "scan_time": tracker.last_scan.isoformat() if tracker.last_scan else None
    }

    if players:
        # Top joueurs par nombre de mains
        sorted_players = sorted(players.values(), key=lambda x: x.hands_played, reverse=True)
        summary["top_players"] = [
            {"name": p.name, "hands": p.hands_played, "vpip": p.vpip, "pfr": p.pfr}
            for p in sorted_players[:5]
        ]

        # Plus agressif
        most_aggressive = max(players.values(), key=lambda x: x.aggression_factor)
        summary["most_aggressive"] = {
            "name": most_aggressive.name,
            "aggression_factor": most_aggressive.aggression_factor
        }

        # Plus passif
        most_passive = min(players.values(), key=lambda x: x.aggression_factor)
        summary["most_passive"] = {
            "name": most_passive.name,
            "aggression_factor": most_passive.aggression_factor
        }

    return summary

if __name__ == "__main__":
    # Test du tracker
    print("🎯 Test du Tracker Poker Intelligent")
    print("="*50)

    try:
        # Créer le tracker
        tracker = IntelligentTracker()

        # Scanner les fichiers
        print("\n🔍 Scan des fichiers d'historique...")
        players = tracker.scan_history_files()

        if players:
            print(f"\n✅ {len(players)} joueurs analysés")

            # Afficher le top 3
            sorted_players = sorted(players.values(), key=lambda x: x.hands_played, reverse=True)
            print("\n🏆 TOP 3 JOUEURS (par nombre de mains):")
            for i, player in enumerate(sorted_players[:3], 1):
                style = tracker._analyze_playing_style(player)
                print(f"{i}. {player.name}: {player.hands_played} mains, "
                      f"VPIP: {player.vpip:.1f}%, Style: {style['type']}")

            # Test d'analyse d'un joueur
            if sorted_players:
                test_player = sorted_players[0].name
                print(f"\n🎭 Analyse détaillée de {test_player}:")
                profile = tracker.get_player_profile(test_player)
                if profile:
                    print(f"Style: {profile['style']['type']}")
                    print(f"Recommandations:")
                    for rec in profile['recommendations'][:3]:
                        print(f"  • {rec}")
        else:
            print("❌ Aucun joueur trouvé")

    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
