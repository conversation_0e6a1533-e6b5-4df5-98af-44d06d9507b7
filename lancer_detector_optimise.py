#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur optimisé pour l'application de détection avec logique avancée.
"""

import os
import sys
import subprocess

def main():
    """Lance l'application de détection optimisée"""
    print("🚀 LANCEMENT DE L'APPLICATION OPTIMISÉE")
    print("=" * 50)
    
    # Vérifier le répertoire de travail
    current_dir = os.getcwd()
    print(f"📁 Répertoire actuel: {current_dir}")
    
    # Chemin vers le script detector_gui.py
    detector_path = os.path.join("Détection des regions", "detector_gui.py")
    
    if not os.path.exists(detector_path):
        print(f"❌ ERREUR: Fichier non trouvé: {detector_path}")
        return
    
    print(f"✅ Script trouvé: {detector_path}")
    
    # Vérifier la configuration
    config_path = os.path.join("Calibration", "config", "poker_advisor_config.json")
    if os.path.exists(config_path):
        print(f"✅ Configuration trouvée: {config_path}")
    else:
        print(f"⚠️ Configuration non trouvée: {config_path}")
    
    print("\n🎯 FONCTIONNALITÉS ACTIVÉES:")
    print("✅ Logique avancée de poker (obligatoire)")
    print("✅ Détection correcte de toutes les combinaisons")
    print("✅ Équités réalistes basées sur les statistiques")
    print("✅ Recommandations intelligentes")
    print("✅ Plus de fausses détections de quintes impossibles")
    print("✅ Optimisations de performance (logs debug désactivés)")
    
    print("\n🚀 Lancement de l'application...")
    
    try:
        # Changer vers le répertoire des régions
        os.chdir("Détection des regions")
        
        # Lancer l'application
        subprocess.run([sys.executable, "detector_gui.py"], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors du lancement: {e}")
    except KeyboardInterrupt:
        print("\n⚠️ Application interrompue par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
    
    print("\n✅ Application fermée")

if __name__ == "__main__":
    main()
