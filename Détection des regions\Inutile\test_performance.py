#!/usr/bin/env python3
"""
Script de test de performance pour comparer les différents modes de détection.

Ce script teste :
1. Mode normal vs mode rapide
2. Utilisation CPU vs GPU (si disponible)
3. Temps de traitement par région
4. Précision de détection
"""

import time
import cv2
import numpy as np
import os
import json
from detector import Detector

def create_test_image():
    """Crée une image de test avec des cartes simulées"""
    # Créer une image de test de 1920x1080 (résolution courante)
    img = np.ones((1080, 1920, 3), dtype=np.uint8) * 50  # Fond gris foncé
    
    # Simuler des cartes à différentes positions
    card_positions = [
        (794, 581, 118, 89),   # card_1
        (937, 579, 132, 95),   # card_2
        (1097, 579, 129, 95),  # card_3
        (1251, 579, 132, 95),  # card_4
        (1405, 579, 134, 99),  # card_5
    ]
    
    # Dessiner des rectangles blancs pour simuler des cartes
    for i, (x, y, w, h) in enumerate(card_positions):
        # Fond de carte blanc
        cv2.rectangle(img, (x, y), (x+w, y+h), (255, 255, 255), -1)
        
        # Bordure noire
        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 0), 2)
        
        # Simuler une valeur de carte
        card_values = ['A', 'K', 'Q', 'J', '10']
        value = card_values[i]
        
        # Texte noir sur fond blanc
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 2.0
        thickness = 3
        
        # Calculer la position du texte pour le centrer
        text_size = cv2.getTextSize(value, font, font_scale, thickness)[0]
        text_x = x + (w - text_size[0]) // 2
        text_y = y + (h + text_size[1]) // 2
        
        cv2.putText(img, value, (text_x, text_y), font, font_scale, (0, 0, 0), thickness)
    
    return img

def benchmark_detection(detector, image, mode_name, fast_mode=False, num_runs=5):
    """Effectue un benchmark de détection"""
    print(f"\n🔍 Test de performance - {mode_name}")
    print("-" * 40)
    
    times = []
    results_list = []
    
    for i in range(num_runs):
        print(f"   Run {i+1}/{num_runs}...", end=" ")
        
        start_time = time.time()
        
        if hasattr(detector, 'process_image_direct'):
            # Utiliser la nouvelle méthode directe
            results = detector.process_image_direct(image, fast_mode=fast_mode)
        else:
            # Sauvegarder temporairement l'image
            temp_path = "temp_benchmark.jpg"
            cv2.imwrite(temp_path, image)
            results = detector.process_image(temp_path)
            if os.path.exists(temp_path):
                os.remove(temp_path)
        
        end_time = time.time()
        duration = end_time - start_time
        times.append(duration)
        results_list.append(results)
        
        print(f"{duration:.3f}s")
    
    # Calculer les statistiques
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"\n📊 Résultats pour {mode_name}:")
    print(f"   Temps moyen: {avg_time:.3f}s")
    print(f"   Temps minimum: {min_time:.3f}s")
    print(f"   Temps maximum: {max_time:.3f}s")
    print(f"   Écart-type: {np.std(times):.3f}s")
    
    # Analyser la cohérence des résultats
    if results_list:
        first_result = results_list[0]
        consistent_results = all(r == first_result for r in results_list)
        print(f"   Cohérence des résultats: {'✅ Oui' if consistent_results else '❌ Non'}")
        
        # Compter les détections
        total_detections = sum(1 for r in first_result.values() if r.get('text', '').strip())
        print(f"   Détections réussies: {total_detections}")
    
    return {
        'mode': mode_name,
        'avg_time': avg_time,
        'min_time': min_time,
        'max_time': max_time,
        'std_time': np.std(times),
        'results': results_list[0] if results_list else {},
        'consistent': consistent_results if results_list else False
    }

def test_cuda_availability():
    """Teste la disponibilité de CUDA"""
    print("🔍 Test de disponibilité CUDA")
    print("-" * 30)
    
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0)
            memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ PyTorch CUDA disponible")
            print(f"   Nombre de GPUs: {device_count}")
            print(f"   GPU principal: {device_name}")
            print(f"   Mémoire GPU: {memory:.1f} GB")
        else:
            print("❌ PyTorch CUDA non disponible")
    except ImportError:
        print("⚠️ PyTorch non installé")
    
    try:
        import paddle
        paddle_cuda = paddle.device.is_compiled_with_cuda()
        if paddle_cuda:
            print(f"✅ PaddlePaddle CUDA disponible")
            if paddle.device.cuda.device_count() > 0:
                print(f"   Nombre de GPUs PaddlePaddle: {paddle.device.cuda.device_count()}")
        else:
            print("❌ PaddlePaddle CUDA non disponible")
    except ImportError:
        print("⚠️ PaddlePaddle non installé")

def main():
    """Fonction principale de test"""
    print("🚀 Test de performance du système de détection")
    print("=" * 50)
    
    # Tester la disponibilité CUDA
    test_cuda_availability()
    
    # Créer une image de test
    print("\n🖼️ Création de l'image de test...")
    test_image = create_test_image()
    
    # Sauvegarder l'image de test pour inspection
    cv2.imwrite("test_image.jpg", test_image)
    print("   Image de test sauvegardée: test_image.jpg")
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Fichier de configuration non trouvé: {config_path}")
        return
    
    print(f"✅ Configuration chargée: {config_path}")
    
    # Tests de performance
    benchmarks = []
    
    try:
        # Test 1: Mode normal sans CUDA
        print("\n" + "="*50)
        detector_normal = Detector(config_path, use_cuda=False)
        benchmark1 = benchmark_detection(detector_normal, test_image, "Mode normal (CPU)", fast_mode=False)
        benchmarks.append(benchmark1)
        
        # Test 2: Mode rapide sans CUDA
        print("\n" + "="*50)
        benchmark2 = benchmark_detection(detector_normal, test_image, "Mode rapide (CPU)", fast_mode=True)
        benchmarks.append(benchmark2)
        
        # Test 3: Mode normal avec CUDA (si disponible)
        try:
            print("\n" + "="*50)
            detector_cuda = Detector(config_path, use_cuda=True)
            benchmark3 = benchmark_detection(detector_cuda, test_image, "Mode normal (CUDA)", fast_mode=False)
            benchmarks.append(benchmark3)
            
            # Test 4: Mode rapide avec CUDA
            print("\n" + "="*50)
            benchmark4 = benchmark_detection(detector_cuda, test_image, "Mode rapide (CUDA)", fast_mode=True)
            benchmarks.append(benchmark4)
            
        except Exception as e:
            print(f"\n⚠️ Tests CUDA ignorés: {e}")
    
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        return
    
    # Résumé des performances
    print("\n" + "="*50)
    print("📈 RÉSUMÉ DES PERFORMANCES")
    print("="*50)
    
    if benchmarks:
        # Trier par temps moyen
        benchmarks.sort(key=lambda x: x['avg_time'])
        
        print(f"{'Mode':<20} {'Temps moyen':<12} {'Amélioration':<12} {'Cohérence'}")
        print("-" * 60)
        
        baseline_time = benchmarks[-1]['avg_time']  # Le plus lent comme référence
        
        for i, benchmark in enumerate(benchmarks):
            improvement = (baseline_time - benchmark['avg_time']) / baseline_time * 100
            improvement_str = f"+{improvement:.1f}%" if improvement > 0 else f"{improvement:.1f}%"
            if i == len(benchmarks) - 1:  # Le plus lent
                improvement_str = "Référence"
            
            consistent_str = "✅" if benchmark['consistent'] else "❌"
            
            print(f"{benchmark['mode']:<20} {benchmark['avg_time']:.3f}s{'':<6} {improvement_str:<12} {consistent_str}")
        
        # Recommandations
        print("\n🎯 RECOMMANDATIONS:")
        fastest = benchmarks[0]
        print(f"   Mode le plus rapide: {fastest['mode']}")
        print(f"   Gain de performance: {(baseline_time - fastest['avg_time']) / baseline_time * 100:.1f}%")
        
        if fastest['mode'].endswith("(CUDA)"):
            print("   ✅ CUDA améliore significativement les performances")
        
        if "rapide" in fastest['mode']:
            print("   ✅ Le mode rapide est recommandé pour la détection en temps réel")
    
    # Sauvegarder les résultats
    results_file = "performance_results.json"
    with open(results_file, 'w') as f:
        json.dump(benchmarks, f, indent=2)
    print(f"\n💾 Résultats sauvegardés dans: {results_file}")

if __name__ == "__main__":
    main()
    input("\nAppuyez sur Entrée pour fermer...")
