#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour mettre à jour le module multi_ocr_detector.py
========================================================

Ce script met à jour le module multi_ocr_detector.py pour utiliser
le nouvel analyseur de forme du J.

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import sys
import re

def update_multi_ocr_detector():
    """Met à jour le module multi_ocr_detector.py pour utiliser le nouvel analyseur de forme du J"""
    print("Mise à jour du module multi_ocr_detector.py...")
    
    # Vérifier si le fichier multi_ocr_detector.py existe
    if not os.path.exists("multi_ocr_detector.py"):
        print("❌ Le fichier multi_ocr_detector.py n'existe pas.")
        return False
    
    # Vérifier si le fichier j_shape_analyzer.py existe
    if not os.path.exists("j_shape_analyzer.py"):
        print("❌ Le fichier j_shape_analyzer.py n'existe pas.")
        return False
    
    # Lire le contenu du fichier multi_ocr_detector.py
    with open("multi_ocr_detector.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Ajouter l'import du module j_shape_analyzer
    import_pattern = r"import os\nimport cv2\nimport numpy as np\nimport time\nimport logging"
    import_replacement = "import os\nimport cv2\nimport numpy as np\nimport time\nimport logging\n\n# Importer l'analyseur de forme du J\nfrom j_shape_analyzer import analyze_j_shape"
    
    content = re.sub(import_pattern, import_replacement, content)
    
    # Remplacer la méthode analyze_j_shape par un appel à la fonction du module j_shape_analyzer
    method_pattern = r"def analyze_j_shape\(self, image\):.*?return False"
    method_replacement = """def analyze_j_shape(self, image):
        """Analyse si l'image contient un J en se basant sur sa forme
        
        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            
        Returns:
            bool: True si l'image contient probablement un J, False sinon
        """
        # Utiliser la fonction du module j_shape_analyzer
        return analyze_j_shape(image)"""
    
    # Utiliser re.DOTALL pour que le point corresponde également aux sauts de ligne
    content = re.sub(method_pattern, method_replacement, content, flags=re.DOTALL)
    
    # Écrire le contenu mis à jour dans le fichier multi_ocr_detector.py
    with open("multi_ocr_detector.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ Le module multi_ocr_detector.py a été mis à jour avec succès.")
    return True

if __name__ == "__main__":
    update_multi_ocr_detector()
