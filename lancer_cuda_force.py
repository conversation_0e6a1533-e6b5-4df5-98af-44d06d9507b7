#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur CUDA optimisé pour l'application de détection.
"""

import os
import sys
import subprocess

def force_cuda_environment():
    """Force l'utilisation de CUDA pour tous les composants"""
    print("🔥 CONFIGURATION CUDA FORCÉE")
    print("=" * 50)
    
    # Variables d'environnement pour forcer CUDA
    cuda_env = {
        'CUDA_VISIBLE_DEVICES': '0',  # Utiliser le GPU 0
        'CUDA_DEVICE_ORDER': 'PCI_BUS_ID',
        'USE_CUDA': '1',
        'FORCE_CUDA': '1',
        'PADDLE_USE_GPU': '1',  # Forcer PaddleOCR sur GPU
        'EASYOCR_GPU': '1',     # Forcer EasyOCR sur GPU
        'TORCH_USE_CUDA_DSA': '1',
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',  # Limiter les allocations
    }
    
    for key, value in cuda_env.items():
        os.environ[key] = value
        print(f"✅ {key} = {value}")
    
    print("\n🎯 VÉRIFICATION CUDA")
    print("=" * 30)
    
    # Vérifier PyTorch CUDA
    try:
        import torch
        print(f"✅ PyTorch CUDA disponible: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU détecté: {torch.cuda.get_device_name(0)}")
            print(f"✅ Mémoire GPU: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            
            # Optimiser la mémoire CUDA
            torch.cuda.empty_cache()
            torch.cuda.set_per_process_memory_fraction(0.8)  # Utiliser max 80% de la VRAM
            print("✅ Mémoire CUDA optimisée")
    except Exception as e:
        print(f"⚠️ Erreur PyTorch: {e}")
    
    print("\n🚀 Configuration CUDA appliquée !")
    return True

def main():
    """Lance l'application avec CUDA forcé"""
    print("🔥 LANCEMENT AVEC CUDA FORCÉ")
    print("=" * 60)
    
    # Configurer CUDA
    force_cuda_environment()
    
    print("\n🚀 Lancement de l'application...")
    
    # Changer vers le répertoire des régions
    os.chdir("Détection des regions")
    
    # Lancer l'application
    process = subprocess.Popen([sys.executable, "detector_gui.py"])
    
    try:
        process.wait()
        print("\n✅ Application fermée proprement")
    except KeyboardInterrupt:
        print("\n⚠️ Interruption par l'utilisateur")
        process.terminate()
        try:
            process.wait(timeout=5)
            print("✅ Application fermée proprement")
        except subprocess.TimeoutExpired:
            print("⚠️ Fermeture forcée de l'application")
            process.kill()
    
    print("\n🧹 Nettoyage final CUDA...")
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            print("✅ Cache CUDA final vidé")
    except:
        pass
    
    input("\nAppuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
