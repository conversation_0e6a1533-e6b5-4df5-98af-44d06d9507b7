#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test des recommandations proportionnelles au stack
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight

def test_recommandations_par_stack():
    """Test des recommandations selon différents stacks"""
    print("🧪 TEST DES RECOMMANDATIONS PROPORTIONNELLES")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Situation identique avec différents stacks
    equity = (65, 85)  # Main premium
    pot_odds = 20.0
    stack_to_pot_ratio = 5.0
    pot = 10
    bet_to_call = 0  # Personne n'a misé
    
    test_stacks = [
        {"stack": 10, "category": "très court"},
        {"stack": 20, "category": "court"},
        {"stack": 35, "category": "moyen"},
        {"stack": 75, "category": "profond"},
        {"stack": 200, "category": "très profond"},
        {"stack": 1000, "category": "très profond"}
    ]
    
    print("📊 SITUATION: Main premium (AA/KK), personne n'a misé")
    print("=" * 60)
    
    for test in test_stacks:
        stack = test["stack"]
        category = test["category"]
        
        action, reason = advisor.recommend_action(
            equity, pot_odds, stack_to_pot_ratio, stack, pot, bet_to_call
        )
        
        print(f"\n🎯 Stack: {stack} BB ({category})")
        print(f"   Action: {action}")
        print(f"   Raison: {reason}")
        
        # Analyser le montant recommandé
        if "raise" in action.lower() and "BB" in action:
            try:
                amount = float(action.split()[1])
                percentage = (amount / stack) * 100
                print(f"   💰 Montant: {amount} BB = {percentage:.1f}% du stack")
            except:
                print(f"   💰 Montant: Non analysable")
        elif "all-in" in action.lower():
            print(f"   💰 Montant: {stack} BB = 100% du stack")

def test_recommandations_avec_mise():
    """Test des recommandations face à une mise"""
    print("\n🧪 TEST AVEC MISE À SUIVRE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    equity = (55, 70)  # Main forte
    pot_odds = 25.0
    stack_to_pot_ratio = 4.0
    pot = 20
    bet_to_call = 5  # Petite mise à suivre
    
    test_stacks = [15, 30, 60, 150, 500]
    
    print("📊 SITUATION: Main forte (QQ/JJ), mise de 5 BB à suivre")
    print("=" * 60)
    
    for stack in test_stacks:
        action, reason = advisor.recommend_action(
            equity, pot_odds, stack_to_pot_ratio, stack, pot, bet_to_call
        )
        
        print(f"\n🎯 Stack: {stack} BB")
        print(f"   Action: {action}")
        print(f"   Raison: {reason}")
        
        # Analyser le montant recommandé
        if "raise" in action.lower() and "BB" in action:
            try:
                amount = float(action.split()[1])
                percentage = (amount / stack) * 100
                print(f"   💰 Montant: {amount} BB = {percentage:.1f}% du stack")
            except:
                print(f"   💰 Montant: Non analysable")
        elif "call" in action.lower():
            percentage = (bet_to_call / stack) * 100
            print(f"   💰 Coût: {bet_to_call} BB = {percentage:.1f}% du stack")

def test_calcul_proportionnel():
    """Test du calcul des montants proportionnels"""
    print("\n🧪 TEST DU CALCUL PROPORTIONNEL")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler la fonction interne
    def test_proportional_bet(stack, percentage, min_bb=2, max_bb=None):
        proportional = stack * (percentage / 100)
        proportional = max(proportional, min_bb)
        if max_bb:
            proportional = min(proportional, max_bb)
        return proportional
    
    test_cases = [
        {"stack": 20, "percentage": 12, "min_bb": 2, "max_bb": 6},
        {"stack": 50, "percentage": 8, "min_bb": 2, "max_bb": 4},
        {"stack": 100, "percentage": 4, "min_bb": 2, "max_bb": 3},
        {"stack": 500, "percentage": 2, "min_bb": 2, "max_bb": 3},
        {"stack": 1000, "percentage": 2, "min_bb": 2, "max_bb": 3}
    ]
    
    print("📊 CALCULS DE MONTANTS PROPORTIONNELS:")
    print("=" * 60)
    
    for case in test_cases:
        stack = case["stack"]
        percentage = case["percentage"]
        min_bb = case["min_bb"]
        max_bb = case["max_bb"]
        
        amount = test_proportional_bet(stack, percentage, min_bb, max_bb)
        actual_percentage = (amount / stack) * 100
        
        print(f"\n🎯 Stack: {stack} BB")
        print(f"   Cible: {percentage}% = {stack * percentage / 100:.1f} BB")
        print(f"   Min: {min_bb} BB, Max: {max_bb} BB")
        print(f"   Résultat: {amount:.1f} BB = {actual_percentage:.1f}% du stack")

def test_situation_complete():
    """Test d'une situation complète avec votre stack"""
    print("\n🧪 TEST SITUATION COMPLÈTE (VOTRE STACK)")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler votre situation
    simulated_results = {
        # Cartes premium pour test
        "carte_1m": {"text": "As", "colors": ["red"]},
        "carte_2m": {"text": "Ad", "colors": ["black"]},
        
        # Votre stack
        "mes_jetons": {"text": "1000", "colors": []},
        
        # Adversaires
        "jetons_joueur1": {"text": "800", "colors": ["white"]},
        "mise_joueur1": {"text": "20", "colors": ["white"]},
        
        # Pot
        "pot_total": {"text": "50", "colors": ["white"]},
    }
    
    print("📊 SITUATION: AA en main, 1000 BB de stack, adversaire mise 20 BB")
    
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"   Main: {analysis['hand_strength']}")
    print(f"   Équité: {analysis['equity'][0]}-{analysis['equity'][1]}%")
    print(f"   Action: {analysis['recommended_action']}")
    print(f"   Raison: {analysis['action_reason']}")
    
    # Analyser si la recommandation est proportionnelle
    action = analysis['recommended_action']
    if "raise" in action.lower() and "BB" in action:
        try:
            amount = float(action.split()[1])
            percentage = (amount / 1000) * 100
            print(f"   💰 Montant recommandé: {amount} BB = {percentage:.1f}% du stack")
            
            if percentage < 1:
                print(f"   ✅ EXCELLENT: Recommandation très conservatrice")
            elif percentage < 5:
                print(f"   ✅ BON: Recommandation proportionnelle")
            elif percentage < 15:
                print(f"   ⚠️ MOYEN: Recommandation un peu agressive")
            else:
                print(f"   ❌ PROBLÈME: Recommandation trop agressive")
        except:
            print(f"   ⚠️ Montant non analysable: {action}")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS DE RECOMMANDATIONS PROPORTIONNELLES")
    print("=" * 60)
    
    # Test principal : recommandations par stack
    test_recommandations_par_stack()
    
    # Test avec mise à suivre
    test_recommandations_avec_mise()
    
    # Test calcul proportionnel
    test_calcul_proportionnel()
    
    # Test situation complète
    test_situation_complete()
    
    print("\n✅ TESTS TERMINÉS")
    print("\n📋 RÉSUMÉ:")
    print("   Les recommandations doivent être proportionnelles au stack")
    print("   Plus le stack est gros, plus les % doivent être petits")
    print("   Plus le stack est petit, plus on tend vers l'all-in")
