#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'installation de Tesseract OCR pour Poker Advisor
========================================================

Ce script installe Tesseract OCR et la bibliothèque Python pytesseract
pour ajouter un moteur OCR supplémentaire à l'application.

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import sys
import subprocess
import platform
import time
import shutil
import urllib.request
import zipfile
import tempfile

def print_header(message):
    """Affiche un message d'en-tête formaté"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80 + "\n")

def print_step(message):
    """Affiche un message d'étape formaté"""
    print(f"\n>> {message}")

def run_command(command, description=None):
    """Exécute une commande et affiche le résultat"""
    if description:
        print_step(description)
    
    print(f"Exécution de: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Commande exécutée avec succès")
        if result.stdout.strip():
            print("\nSortie standard:")
            print(result.stdout.strip())
    else:
        print("❌ Erreur lors de l'exécution de la commande")
        if result.stderr.strip():
            print("\nErreur standard:")
            print(result.stderr.strip())
    
    return result.returncode == 0, result.stdout

def check_tesseract():
    """Vérifie si Tesseract OCR est installé"""
    print_step("Vérification de Tesseract OCR")
    
    try:
        # Vérifier si Tesseract est installé
        success, output = run_command("tesseract --version", "Vérification de Tesseract OCR")
        
        if success:
            print("✅ Tesseract OCR est installé")
            return True
        else:
            print("⚠️ Tesseract OCR n'est pas installé ou n'est pas dans le PATH")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de Tesseract OCR: {e}")
        return False

def check_pytesseract():
    """Vérifie si pytesseract est installé"""
    print_step("Vérification de pytesseract")
    
    try:
        # Vérifier si pytesseract est installé
        import_success, _ = run_command(
            f"{sys.executable} -c \"import pytesseract; print('pytesseract installé')\"",
            "Vérification de pytesseract"
        )
        
        if import_success:
            print("✅ pytesseract est installé")
            return True
        else:
            print("⚠️ pytesseract n'est pas installé")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de pytesseract: {e}")
        return False

def install_tesseract_windows():
    """Installe Tesseract OCR sur Windows"""
    print_step("Installation de Tesseract OCR sur Windows")
    
    # URL de téléchargement de Tesseract OCR
    tesseract_url = "https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.1.20230401.exe"
    
    # Chemin d'installation par défaut
    install_path = "C:\\Program Files\\Tesseract-OCR"
    
    # Créer un dossier temporaire pour le téléchargement
    temp_dir = tempfile.mkdtemp()
    installer_path = os.path.join(temp_dir, "tesseract-installer.exe")
    
    try:
        # Télécharger l'installateur
        print(f"Téléchargement de Tesseract OCR depuis {tesseract_url}...")
        urllib.request.urlretrieve(tesseract_url, installer_path)
        print("✅ Téléchargement terminé")
        
        # Exécuter l'installateur en mode silencieux
        print("Installation de Tesseract OCR...")
        print("⚠️ Une fenêtre d'installation va s'ouvrir. Suivez les instructions à l'écran.")
        print("⚠️ Assurez-vous d'installer les langues françaises et anglaises.")
        print("⚠️ Notez le chemin d'installation pour configurer pytesseract plus tard.")
        
        # Lancer l'installateur
        os.startfile(installer_path)
        
        # Attendre que l'utilisateur confirme l'installation
        input("\nAppuyez sur Entrée une fois l'installation terminée...")
        
        # Vérifier si Tesseract est installé
        if os.path.exists(install_path):
            print(f"✅ Tesseract OCR installé dans {install_path}")
        else:
            print("⚠️ Impossible de vérifier l'installation de Tesseract OCR")
            install_path = input("Veuillez entrer le chemin d'installation de Tesseract OCR: ")
        
        # Ajouter Tesseract au PATH
        print("Ajout de Tesseract OCR au PATH...")
        path_env = os.environ.get("PATH", "")
        if install_path not in path_env:
            os.environ["PATH"] = f"{install_path};{path_env}"
            print("✅ Tesseract OCR ajouté au PATH temporairement")
            
            # Ajouter au PATH de manière permanente
            try:
                import winreg
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_ALL_ACCESS)
                current_path = winreg.QueryValueEx(key, "PATH")[0]
                if install_path not in current_path:
                    new_path = f"{install_path};{current_path}"
                    winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_path)
                    winreg.CloseKey(key)
                    print("✅ Tesseract OCR ajouté au PATH de manière permanente")
                else:
                    print("✅ Tesseract OCR est déjà dans le PATH")
            except Exception as e:
                print(f"⚠️ Impossible d'ajouter Tesseract OCR au PATH de manière permanente: {e}")
                print("⚠️ Vous devrez peut-être ajouter manuellement Tesseract OCR au PATH")
        
        return True, install_path
    except Exception as e:
        print(f"❌ Erreur lors de l'installation de Tesseract OCR: {e}")
        return False, None
    finally:
        # Nettoyer le dossier temporaire
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def install_pytesseract():
    """Installe pytesseract"""
    print_step("Installation de pytesseract")
    
    try:
        # Installer pytesseract
        success, _ = run_command(
            f"{sys.executable} -m pip install pytesseract",
            "Installation de pytesseract"
        )
        
        if success:
            print("✅ pytesseract installé avec succès")
            return True
        else:
            print("❌ Échec de l'installation de pytesseract")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de l'installation de pytesseract: {e}")
        return False

def configure_pytesseract(tesseract_path):
    """Configure pytesseract pour utiliser Tesseract OCR"""
    print_step("Configuration de pytesseract")
    
    try:
        # Créer un fichier de configuration pour pytesseract
        config_code = f"""
import pytesseract

# Configurer le chemin vers Tesseract OCR
pytesseract.pytesseract.tesseract_cmd = r'{tesseract_path}\\tesseract.exe'

# Tester la configuration
try:
    print(pytesseract.get_tesseract_version())
    print("✅ pytesseract configuré avec succès")
except Exception as e:
    print(f"❌ Erreur lors de la configuration de pytesseract: {{e}}")
"""
        
        # Écrire le code de test dans un fichier temporaire
        with open("test_tesseract_config.py", "w") as f:
            f.write(config_code)
        
        # Exécuter le test
        success, _ = run_command(f"{sys.executable} test_tesseract_config.py", "Test de la configuration de pytesseract")
        
        # Supprimer le fichier temporaire
        try:
            os.remove("test_tesseract_config.py")
        except:
            pass
        
        return success
    except Exception as e:
        print(f"❌ Erreur lors de la configuration de pytesseract: {e}")
        return False

def main():
    """Fonction principale"""
    print_header("Installation de Tesseract OCR pour Poker Advisor")
    
    # Vérifier si Tesseract OCR est déjà installé
    tesseract_installed = check_tesseract()
    
    # Installer Tesseract OCR si nécessaire
    tesseract_path = "C:\\Program Files\\Tesseract-OCR"
    if not tesseract_installed:
        if platform.system() == "Windows":
            tesseract_installed, tesseract_path = install_tesseract_windows()
        else:
            print("⚠️ L'installation automatique de Tesseract OCR n'est pas prise en charge sur ce système d'exploitation.")
            print("⚠️ Veuillez installer Tesseract OCR manuellement.")
            tesseract_installed = False
    
    # Vérifier si pytesseract est déjà installé
    pytesseract_installed = check_pytesseract()
    
    # Installer pytesseract si nécessaire
    if not pytesseract_installed:
        pytesseract_installed = install_pytesseract()
    
    # Configurer pytesseract si Tesseract OCR et pytesseract sont installés
    if tesseract_installed and pytesseract_installed:
        configure_pytesseract(tesseract_path)
    
    print_header("Installation terminée")
    
    if tesseract_installed and pytesseract_installed:
        print("✅ Tesseract OCR et pytesseract ont été installés et configurés avec succès.")
        print("✅ Vous pouvez maintenant utiliser Tesseract OCR dans votre application.")
    else:
        print("⚠️ L'installation n'est pas complète. Veuillez résoudre les problèmes ci-dessus.")
    
    # Attendre avant de fermer
    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
