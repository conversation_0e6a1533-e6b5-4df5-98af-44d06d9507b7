#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'affichage des images
=============================

Ce script teste les améliorations apportées à l'affichage
des images de capture pour vérifier qu'elles ne sont plus coupées.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os
import cv2
import numpy as np

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_regions():
    """Crée des régions de test pour simuler une capture"""
    
    regions = {}
    
    # Créer des images de test de différentes tailles
    test_regions = [
        ("card_1", (120, 90)),
        ("card_2", (130, 95)),
        ("card_3", (125, 92)),
        ("card_4", (135, 98)),
        ("card_5", (128, 94)),
        ("carte_1m", (55, 52)),
        ("carte_2m", (58, 55)),
        ("chips_player", (80, 30)),
        ("bet_player", (70, 25)),
        ("pot", (90, 35)),
    ]
    
    for name, (width, height) in test_regions:
        # Créer une image colorée avec du texte
        image = np.random.randint(50, 200, (height, width, 3), dtype=np.uint8)
        
        # Ajouter une couleur de fond selon le type
        if 'card_' in name:
            image[:, :] = [240, 240, 255]  # Bleu clair pour les cartes
        elif 'carte_' in name:
            image[:, :] = [255, 240, 240]  # Rouge clair pour les cartes en main
        elif 'chips_' in name or 'bet_' in name:
            image[:, :] = [240, 255, 240]  # Vert clair pour les jetons/mises
        else:
            image[:, :] = [255, 255, 240]  # Jaune clair pour le pot
        
        # Ajouter du texte
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        color = (0, 0, 0)
        thickness = 1
        
        # Texte principal
        text = name.replace('_', ' ')
        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
        text_x = (width - text_size[0]) // 2
        text_y = (height + text_size[1]) // 2
        
        cv2.putText(image, text, (text_x, text_y), font, font_scale, color, thickness)
        
        # Ajouter une bordure
        cv2.rectangle(image, (2, 2), (width-3, height-3), (100, 100, 100), 1)
        
        regions[name] = image
    
    return regions

def test_preview_creation():
    """Teste la création de prévisualisation avec les nouveaux paramètres"""
    
    print("🧪 Test de l'affichage des images de capture")
    print("=" * 60)
    
    # Créer des régions de test
    regions_dict = create_test_regions()
    print(f"✅ {len(regions_dict)} régions de test créées")
    
    # Simuler la fonction create_regions_preview avec les nouveaux paramètres
    try:
        if not regions_dict:
            print("❌ Aucune région à afficher")
            return False

        # Nouveaux paramètres améliorés
        preview_width = 1600  # Largeur encore plus grande
        preview_height = 1200  # Hauteur encore plus grande
        preview = np.ones((preview_height, preview_width, 3), dtype=np.uint8) * 240

        # Paramètres optimisés
        margin = 50  # Marge plus grande
        spacing_x = 80  # Espacement horizontal plus généreux
        spacing_y = 120  # Espacement vertical plus généreux
        cards_per_row = 4  # Plus de cartes par ligne

        print(f"📐 Dimensions de la prévisualisation: {preview_width}x{preview_height}")
        print(f"📏 Paramètres: marge={margin}, espacement_x={spacing_x}, espacement_y={spacing_y}")
        print(f"🎯 Cartes par ligne: {cards_per_row}")

        # Calculer la taille maximale des régions
        max_width = 0
        max_height = 0
        for region_image in regions_dict.values():
            h, w = region_image.shape[:2]
            max_width = max(max_width, w)
            max_height = max(max_height, h)

        print(f"📊 Taille maximale des régions: {max_width}x{max_height}")

        # Ajouter chaque région à la prévisualisation
        i = 0
        regions_placed = 0
        regions_skipped = 0
        
        for name, region_image in regions_dict.items():
            # Calculer la position dans la grille
            col = i % cards_per_row
            row = i // cards_per_row

            # Obtenir les dimensions de l'image
            h, w = region_image.shape[:2]

            # Calculer les coordonnées avec un espacement uniforme
            x = margin + col * (max_width + spacing_x)
            y = margin + row * (max_height + spacing_y)

            # Vérifier si la région dépasse les limites
            if y + h >= preview_height or x + w >= preview_width:
                # Augmenter la taille de la prévisualisation si nécessaire
                new_width = max(preview_width, x + w + margin)
                new_height = max(preview_height, y + h + margin + 30)
                new_preview = np.ones((new_height, new_width, 3), dtype=np.uint8) * 240
                new_preview[:preview_height, :preview_width] = preview
                preview = new_preview
                preview_width, preview_height = new_width, new_height
                print(f"🔄 Prévisualisation agrandie pour {name}: {new_width}x{new_height}")

            # Ajouter la région à la prévisualisation
            preview[y:y+h, x:x+w] = region_image

            # Ajouter le nom de la région avec un fond
            text_size = cv2.getTextSize(name, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)[0]
            cv2.rectangle(preview, (x, y+h+5), (x+text_size[0], y+h+5+text_size[1]+10), (240, 240, 240), -1)
            cv2.putText(preview, name, (x, y+h+20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 1, cv2.LINE_AA)

            # Ajouter un rectangle autour de la région
            cv2.rectangle(preview, (x-2, y-2), (x+w+2, y+h+2), (0, 0, 255), 1)

            regions_placed += 1
            i += 1

        print(f"✅ Régions placées: {regions_placed}")
        print(f"⚠️ Régions ignorées: {regions_skipped}")
        print(f"📐 Taille finale de la prévisualisation: {preview_width}x{preview_height}")

        # Sauvegarder l'image de test
        test_image_path = "test_preview_display.jpg"
        cv2.imwrite(test_image_path, preview)
        print(f"💾 Image de test sauvegardée: {test_image_path}")

        # Test de redimensionnement pour l'affichage
        max_display_width = 1200
        max_display_height = 800
        
        h, w = preview.shape[:2]
        if w > max_display_width or h > max_display_height:
            ratio = min(max_display_width / w, max_display_height / h)
            new_width = int(w * ratio)
            new_height = int(h * ratio)
            
            resized_preview = cv2.resize(preview, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"📏 Image redimensionnée pour l'affichage: {w}x{h} → {new_width}x{new_height}")
            
            # Sauvegarder l'image redimensionnée
            resized_image_path = "test_preview_resized.jpg"
            cv2.imwrite(resized_image_path, resized_preview)
            print(f"💾 Image redimensionnée sauvegardée: {resized_image_path}")
        else:
            print(f"ℹ️ Pas besoin de redimensionnement: {w}x{h} ≤ {max_display_width}x{max_display_height}")

        print("\n✅ Test de l'affichage terminé avec succès!")
        print("\n📝 Améliorations apportées:")
        print("  ✅ Dimensions de prévisualisation agrandies (1600x1200)")
        print("  ✅ Marges et espacements plus généreux")
        print("  ✅ Agrandissement automatique si nécessaire")
        print("  ✅ Redimensionnement intelligent pour l'affichage")
        print("  ✅ Zone d'affichage agrandie (800x600)")
        print("  ✅ Bordure et style améliorés")
        
        return True

    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_preview_creation()
