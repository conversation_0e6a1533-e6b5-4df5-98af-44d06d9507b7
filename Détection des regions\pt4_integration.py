#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 INTÉGRATION POKERTRACKER 4 AVEC CONSEILLER POKER
==================================================

Module pour intégrer PokerTracker 4 avec votre conseiller poker.
Lit les données PT4 en temps réel pour des recommandations intelligentes.
"""

import psycopg2
import sqlite3
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import threading

@dataclass
class PT4PlayerStats:
    """Statistiques d'un joueur depuis PT4"""
    player_name: str
    hands: int = 0
    vpip: float = 0.0
    pfr: float = 0.0
    three_bet: float = 0.0
    fold_to_three_bet: float = 0.0
    cbet: float = 0.0
    fold_to_cbet: float = 0.0
    aggression_factor: float = 0.0
    wtsd: float = 0.0
    w_sd: float = 0.0
    
    # Stats par position
    vpip_ep: float = 0.0  # Early Position
    vpip_mp: float = 0.0  # Middle Position  
    vpip_lp: float = 0.0  # Late Position
    vpip_sb: float = 0.0  # Small Blind
    vpip_bb: float = 0.0  # Big Blind

class PT4Connector:
    """Connecteur pour lire les données PokerTracker 4"""
    
    def __init__(self, 
                 host: str = "localhost",
                 port: int = 5432,
                 database: str = "PT4DB",
                 username: str = "postgres",
                 password: str = ""):
        
        self.connection_params = {
            'host': host,
            'port': port,
            'database': database,
            'user': username,
            'password': password
        }
        
        self.connection = None
        self.is_connected = False
        
    def connect(self) -> bool:
        """Se connecte à la base PT4"""
        try:
            self.connection = psycopg2.connect(**self.connection_params)
            self.is_connected = True
            print("✅ Connecté à PokerTracker 4")
            return True
        except Exception as e:
            print(f"❌ Erreur connexion PT4: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Ferme la connexion"""
        if self.connection:
            self.connection.close()
            self.is_connected = False
    
    def get_player_stats(self, player_name: str, hands_min: int = 10) -> Optional[PT4PlayerStats]:
        """Récupère les stats d'un joueur depuis PT4"""
        if not self.is_connected:
            return None
            
        try:
            cursor = self.connection.cursor()
            
            # Requête PT4 pour les stats de base
            query = """
            SELECT 
                p.player_name,
                COUNT(hp.id_hand) as hands,
                AVG(CASE WHEN hp.flg_vpip THEN 100.0 ELSE 0.0 END) as vpip,
                AVG(CASE WHEN hp.flg_pfr THEN 100.0 ELSE 0.0 END) as pfr,
                AVG(CASE WHEN hp.flg_3bet THEN 100.0 ELSE 0.0 END) as three_bet,
                AVG(CASE WHEN hp.flg_fold_to_3bet THEN 100.0 ELSE 0.0 END) as fold_to_3bet,
                AVG(CASE WHEN hp.flg_cbet THEN 100.0 ELSE 0.0 END) as cbet,
                AVG(CASE WHEN hp.flg_fold_to_cbet THEN 100.0 ELSE 0.0 END) as fold_to_cbet,
                CASE 
                    WHEN SUM(hp.cnt_call + hp.cnt_check) > 0 
                    THEN SUM(hp.cnt_bet + hp.cnt_raise)::float / SUM(hp.cnt_call + hp.cnt_check)
                    ELSE 0 
                END as aggression_factor,
                AVG(CASE WHEN hp.flg_wtsd THEN 100.0 ELSE 0.0 END) as wtsd,
                AVG(CASE WHEN hp.flg_w_sd THEN 100.0 ELSE 0.0 END) as w_sd
            FROM player p
            JOIN holdem_hand_player_statistics hp ON p.id_player = hp.id_player
            WHERE p.player_name = %s
            GROUP BY p.player_name
            HAVING COUNT(hp.id_hand) >= %s
            """
            
            cursor.execute(query, (player_name, hands_min))
            result = cursor.fetchone()
            
            if result:
                return PT4PlayerStats(
                    player_name=result[0],
                    hands=result[1],
                    vpip=result[2] or 0.0,
                    pfr=result[3] or 0.0,
                    three_bet=result[4] or 0.0,
                    fold_to_three_bet=result[5] or 0.0,
                    cbet=result[6] or 0.0,
                    fold_to_cbet=result[7] or 0.0,
                    aggression_factor=result[8] or 0.0,
                    wtsd=result[9] or 0.0,
                    w_sd=result[10] or 0.0
                )
            
            cursor.close()
            return None
            
        except Exception as e:
            print(f"❌ Erreur requête PT4: {e}")
            return None
    
    def get_table_players(self, table_name: str = None) -> List[str]:
        """Récupère la liste des joueurs à une table active"""
        if not self.is_connected:
            return []
            
        try:
            cursor = self.connection.cursor()
            
            # Requête pour les joueurs actifs (dernière heure)
            query = """
            SELECT DISTINCT p.player_name
            FROM player p
            JOIN holdem_hand_player_statistics hp ON p.id_player = hp.id_player
            JOIN holdem_hand_summary h ON hp.id_hand = h.id_hand
            WHERE h.date_played > NOW() - INTERVAL '1 hour'
            ORDER BY p.player_name
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            cursor.close()
            return [row[0] for row in results]
            
        except Exception as e:
            print(f"❌ Erreur récupération joueurs: {e}")
            return []

class PT4AdvisorIntegration:
    """Intégration PT4 avec le conseiller poker"""
    
    def __init__(self, pt4_connector: PT4Connector):
        self.pt4 = pt4_connector
        self.player_cache = {}
        self.cache_timeout = 300  # 5 minutes
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """Démarre la surveillance automatique"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("🔄 Surveillance PT4 démarrée")
    
    def stop_monitoring(self):
        """Arrête la surveillance"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("⏹️ Surveillance PT4 arrêtée")
    
    def _monitor_loop(self):
        """Boucle de surveillance en arrière-plan"""
        while self.monitoring:
            try:
                # Mettre à jour le cache des joueurs actifs
                active_players = self.pt4.get_table_players()
                
                for player_name in active_players:
                    if player_name not in self.player_cache or \
                       time.time() - self.player_cache[player_name]['timestamp'] > self.cache_timeout:
                        
                        stats = self.pt4.get_player_stats(player_name)
                        if stats:
                            self.player_cache[player_name] = {
                                'stats': stats,
                                'timestamp': time.time()
                            }
                
                time.sleep(30)  # Vérifier toutes les 30 secondes
                
            except Exception as e:
                print(f"⚠️ Erreur surveillance: {e}")
                time.sleep(60)
    
    def get_enhanced_recommendation(self, 
                                  hand_cards: List[str],
                                  board_cards: List[str],
                                  current_players: List[str],
                                  pot_size: int = 0,
                                  my_stack: int = 0) -> Dict[str, Any]:
        """
        Génère une recommandation améliorée avec les données PT4
        """
        
        # Recommandation de base (votre logique existante)
        base_recommendation = self._get_base_recommendation(hand_cards, board_cards, pot_size)
        
        # Enrichissement avec PT4
        enhanced_recommendation = {
            "base_action": base_recommendation["action"],
            "base_reasoning": base_recommendation["reasoning"],
            "pt4_insights": {},
            "table_dynamics": {},
            "final_recommendation": base_recommendation["action"],
            "adjustments": []
        }
        
        # Analyser chaque joueur avec PT4
        for player_name in current_players:
            pt4_stats = self._get_cached_stats(player_name)
            if pt4_stats:
                player_analysis = self._analyze_pt4_player(pt4_stats)
                enhanced_recommendation["pt4_insights"][player_name] = player_analysis
                
                # Ajuster la recommandation
                adjustment = self._adjust_for_pt4_player(
                    enhanced_recommendation["final_recommendation"],
                    player_analysis,
                    hand_cards,
                    board_cards
                )
                
                if adjustment:
                    enhanced_recommendation["adjustments"].append(adjustment)
                    enhanced_recommendation["final_recommendation"] = adjustment["new_action"]
        
        # Analyse de table globale
        if enhanced_recommendation["pt4_insights"]:
            table_analysis = self._analyze_table_dynamics(enhanced_recommendation["pt4_insights"])
            enhanced_recommendation["table_dynamics"] = table_analysis
        
        return enhanced_recommendation
    
    def _get_cached_stats(self, player_name: str) -> Optional[PT4PlayerStats]:
        """Récupère les stats depuis le cache ou PT4"""
        
        # Vérifier le cache
        if player_name in self.player_cache:
            cache_entry = self.player_cache[player_name]
            if time.time() - cache_entry['timestamp'] < self.cache_timeout:
                return cache_entry['stats']
        
        # Récupérer depuis PT4
        stats = self.pt4.get_player_stats(player_name)
        if stats:
            self.player_cache[player_name] = {
                'stats': stats,
                'timestamp': time.time()
            }
        
        return stats
    
    def _analyze_pt4_player(self, stats: PT4PlayerStats) -> Dict[str, Any]:
        """Analyse un joueur basée sur ses stats PT4"""
        
        # Classification du style
        if stats.vpip < 15:
            if stats.pfr < 10:
                style = "Tight-Passive (Nit)"
                color = "#4CAF50"
            else:
                style = "Tight-Aggressive (TAG)"
                color = "#2196F3"
        elif stats.vpip < 25:
            if stats.pfr < 15:
                style = "Loose-Passive (Calling Station)"
                color = "#FF9800"
            else:
                style = "Loose-Aggressive (LAG)"
                color = "#F44336"
        else:
            style = "Maniac"
            color = "#9C27B0"
        
        # Tendances spécifiques
        tendencies = []
        
        if stats.three_bet > 8:
            tendencies.append("3bet élevé - joueur agressif preflop")
        
        if stats.cbet > 75:
            tendencies.append("Cbet très élevé - continuation bet systématique")
        
        if stats.fold_to_cbet > 60:
            tendencies.append("Fold beaucoup aux cbets - exploitable")
        
        if stats.wtsd < 20:
            tendencies.append("Va rarement au showdown - bluffeur potentiel")
        
        # Recommandations
        recommendations = self._generate_pt4_recommendations(stats, style)
        
        return {
            "style": style,
            "color": color,
            "stats": {
                "vpip": stats.vpip,
                "pfr": stats.pfr,
                "3bet": stats.three_bet,
                "cbet": stats.cbet,
                "af": stats.aggression_factor,
                "hands": stats.hands
            },
            "tendencies": tendencies,
            "recommendations": recommendations,
            "confidence": min(stats.hands / 100, 1.0)  # Confiance basée sur le nombre de mains
        }
    
    def _generate_pt4_recommendations(self, stats: PT4PlayerStats, style: str) -> List[str]:
        """Génère des recommandations basées sur les stats PT4"""
        recommendations = []
        
        # Recommandations basées sur le style
        if "Tight-Passive" in style:
            recommendations.extend([
                "🎯 Volez ses blinds fréquemment",
                "💰 Value bet thin contre lui",
                "🚫 Évitez de bluffer"
            ])
        elif "Tight-Aggressive" in style:
            recommendations.extend([
                "⚠️ Respectez ses relances",
                "💪 Jouez en position contre lui",
                "🎯 Exploitez sa tightness preflop"
            ])
        elif "Loose-Passive" in style:
            recommendations.extend([
                "💰 Value bet large",
                "🚫 Ne bluffez jamais",
                "⚡ Isolez-le avec des mains moyennes"
            ])
        elif "Loose-Aggressive" in style:
            recommendations.extend([
                "🛡️ Jouez plus tight",
                "🎭 Piégez avec vos nuts",
                "⚠️ Attention aux 3-bets light"
            ])
        
        # Recommandations spécifiques aux stats
        if stats.fold_to_cbet > 60:
            recommendations.append(f"🎯 Cbet souvent (fold {stats.fold_to_cbet:.0f}% aux cbets)")
        
        if stats.three_bet > 8:
            recommendations.append(f"⚠️ Attention aux 3-bets ({stats.three_bet:.1f}%)")
        
        if stats.cbet > 75:
            recommendations.append(f"🛡️ Défendez contre ses cbets ({stats.cbet:.0f}%)")
        
        return recommendations
    
    def _adjust_for_pt4_player(self, current_action: str, player_analysis: Dict, 
                              hand_cards: List[str], board_cards: List[str]) -> Optional[Dict]:
        """Ajuste l'action basée sur l'analyse PT4"""
        
        stats = player_analysis["stats"]
        style = player_analysis["style"]
        
        # Ajustements spécifiques
        if "Tight-Aggressive" in style and current_action == "call":
            if stats["3bet"] > 8:
                return {
                    "reason": f"Contre TAG avec 3bet {stats['3bet']:.1f}%",
                    "adjustment": "Être plus prudent",
                    "new_action": "fold"
                }
        
        elif "Loose-Passive" in style and current_action == "fold":
            if len(hand_cards) >= 2 and any(card[0] in ['A', 'K', 'Q'] for card in hand_cards):
                return {
                    "reason": f"Contre Calling Station (VPIP {stats['vpip']:.0f}%)",
                    "adjustment": "Élargir le range",
                    "new_action": "call"
                }
        
        return None
    
    def _analyze_table_dynamics(self, players_insights: Dict) -> Dict[str, Any]:
        """Analyse la dynamique générale de la table"""
        
        if not players_insights:
            return {}
        
        # Calculer les moyennes
        total_vpip = sum(p["stats"]["vpip"] for p in players_insights.values())
        total_pfr = sum(p["stats"]["pfr"] for p in players_insights.values())
        total_3bet = sum(p["stats"]["3bet"] for p in players_insights.values())
        player_count = len(players_insights)
        
        avg_vpip = total_vpip / player_count
        avg_pfr = total_pfr / player_count
        avg_3bet = total_3bet / player_count
        
        # Déterminer le style de table
        if avg_vpip < 20 and avg_pfr < 15:
            table_style = "Tight-Passive"
            recommendations = [
                "🎯 Table serrée: volez les blinds",
                "💰 Value bet thin",
                "⚡ Soyez plus agressif"
            ]
        elif avg_vpip > 25 and avg_pfr > 18:
            table_style = "Loose-Aggressive"
            recommendations = [
                "🛡️ Table agressive: jouez tight",
                "🎭 Piégez avec vos bonnes mains",
                "⚠️ Attention aux 3-bets"
            ]
        else:
            table_style = "Mixte"
            recommendations = [
                "🎯 Table mixte: adaptez-vous individuellement"
            ]
        
        return {
            "style": table_style,
            "avg_vpip": avg_vpip,
            "avg_pfr": avg_pfr,
            "avg_3bet": avg_3bet,
            "player_count": player_count,
            "recommendations": recommendations
        }
    
    def _get_base_recommendation(self, hand_cards: List[str], board_cards: List[str], pot_size: int) -> Dict[str, Any]:
        """Recommandation de base (remplacez par votre logique)"""
        # Placeholder - remplacez par votre logique de conseiller existante
        if not hand_cards:
            return {"action": "fold", "reasoning": "Pas de cartes"}
        
        high_cards = sum(1 for card in hand_cards if card[0] in ['A', 'K', 'Q', 'J'])
        
        if high_cards >= 2:
            return {"action": "raise", "reasoning": "Main forte"}
        elif high_cards == 1:
            return {"action": "call", "reasoning": "Main moyenne"}
        else:
            return {"action": "fold", "reasoning": "Main faible"}

# Fonctions utilitaires
def create_pt4_integration(host="localhost", database="PT4DB", username="postgres", password="") -> PT4AdvisorIntegration:
    """Crée une intégration PT4 prête à l'emploi"""
    
    connector = PT4Connector(host=host, database=database, username=username, password=password)
    
    if connector.connect():
        integration = PT4AdvisorIntegration(connector)
        return integration
    else:
        raise Exception("Impossible de se connecter à PokerTracker 4")

if __name__ == "__main__":
    # Test de l'intégration PT4
    print("🧪 Test de l'intégration PokerTracker 4")
    print("=" * 50)
    
    try:
        # Tentative de connexion PT4
        integration = create_pt4_integration()
        
        print("✅ Connexion PT4 réussie")
        
        # Démarrer la surveillance
        integration.start_monitoring()
        
        # Test avec des joueurs fictifs
        test_players = ["TestPlayer1", "TestPlayer2"]
        
        recommendation = integration.get_enhanced_recommendation(
            hand_cards=["As", "Kh"],
            board_cards=["Qd", "Jc", "Ts"],
            current_players=test_players,
            pot_size=1000
        )
        
        print(f"🎯 Recommandation de test:")
        print(f"Action: {recommendation['final_recommendation']}")
        print(f"Ajustements: {len(recommendation['adjustments'])}")
        
        # Arrêter la surveillance
        integration.stop_monitoring()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("💡 Assurez-vous que PokerTracker 4 est installé et configuré")
