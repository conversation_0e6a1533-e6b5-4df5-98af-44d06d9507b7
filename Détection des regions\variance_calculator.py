#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Calculateur de variance pour poker - Estimation des swings
"""

import numpy as np
import math
from scipy import stats
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

class VarianceCalculator:
    """Calculateur de variance et swings au poker"""
    
    def __init__(self):
        # Paramètres de variance par type de jeu
        self.game_variance = {
            "cash_6max": {"bb_per_100": 5, "std_dev": 80},
            "cash_9max": {"bb_per_100": 4, "std_dev": 70},
            "tournament": {"roi": 20, "std_dev": 300},
            "sit_n_go": {"roi": 15, "std_dev": 150}
        }
        
        # Facteurs d'ajustement selon le style de jeu
        self.style_adjustments = {
            "tight_passive": {"winrate_mult": 0.8, "variance_mult": 0.7},
            "tight_aggressive": {"winrate_mult": 1.0, "variance_mult": 1.0},
            "loose_passive": {"winrate_mult": 0.6, "variance_mult": 1.3},
            "loose_aggressive": {"winrate_mult": 1.2, "variance_mult": 1.5},
            "maniac": {"winrate_mult": 0.9, "variance_mult": 2.0}
        }
    
    def calculate_variance_metrics(self, game_type, winrate, hands_played, style="tight_aggressive"):
        """
        Calcule les métriques de variance pour une session/période
        
        Args:
            game_type (str): Type de jeu (cash_6max, tournament, etc.)
            winrate (float): Winrate en BB/100 ou ROI%
            hands_played (int): Nombre de mains jouées
            style (str): Style de jeu
            
        Returns:
            dict: Métriques de variance complètes
        """
        if game_type not in self.game_variance:
            return {"error": f"Type de jeu {game_type} non supporté"}
        
        base_params = self.game_variance[game_type]
        style_adj = self.style_adjustments.get(style, self.style_adjustments["tight_aggressive"])
        
        # Ajuster les paramètres selon le style
        adjusted_winrate = winrate * style_adj["winrate_mult"]
        adjusted_variance = base_params["std_dev"] * style_adj["variance_mult"]
        
        # Calculer les métriques de base
        expected_result = (adjusted_winrate / 100) * hands_played
        standard_deviation = adjusted_variance * math.sqrt(hands_played / 100)
        
        # Calculer les intervalles de confiance
        confidence_intervals = self._calculate_confidence_intervals(
            expected_result, standard_deviation
        )
        
        # Calculer les probabilités de downswing
        downswing_probabilities = self._calculate_downswing_probabilities(
            adjusted_winrate, adjusted_variance, hands_played
        )
        
        # Calculer la bankroll recommandée
        bankroll_requirements = self._calculate_bankroll_requirements(
            adjusted_winrate, adjusted_variance, game_type
        )
        
        # Simuler des résultats possibles
        simulation_results = self._simulate_results(
            adjusted_winrate, adjusted_variance, hands_played
        )
        
        return {
            "expected_result": expected_result,
            "standard_deviation": standard_deviation,
            "confidence_intervals": confidence_intervals,
            "downswing_probabilities": downswing_probabilities,
            "bankroll_requirements": bankroll_requirements,
            "simulation_results": simulation_results,
            "risk_metrics": self._calculate_risk_metrics(
                adjusted_winrate, adjusted_variance, hands_played
            ),
            "parameters_used": {
                "adjusted_winrate": adjusted_winrate,
                "adjusted_variance": adjusted_variance,
                "hands_played": hands_played,
                "style": style
            }
        }
    
    def _calculate_confidence_intervals(self, expected, std_dev):
        """Calcule les intervalles de confiance"""
        intervals = {}
        
        # Différents niveaux de confiance
        confidence_levels = [0.68, 0.95, 0.99]
        z_scores = [1.0, 1.96, 2.58]
        
        for conf_level, z_score in zip(confidence_levels, z_scores):
            margin = z_score * std_dev
            intervals[f"{int(conf_level*100)}%"] = {
                "lower": expected - margin,
                "upper": expected + margin,
                "range": margin * 2
            }
        
        return intervals
    
    def _calculate_downswing_probabilities(self, winrate, variance, hands):
        """Calcule les probabilités de downswings"""
        probabilities = {}
        
        # Différentes tailles de downswing en BB
        downswing_sizes = [50, 100, 200, 500, 1000]
        
        for size in downswing_sizes:
            # Probabilité d'un downswing de cette taille
            z_score = size / (variance * math.sqrt(hands / 100))
            prob = stats.norm.cdf(-z_score) * 100
            
            probabilities[f"{size}_bb"] = {
                "probability": prob,
                "description": f"Probabilité de perdre {size} BB ou plus"
            }
        
        # Probabilité de finir en négatif
        if winrate > 0:
            z_score = (winrate / 100 * hands) / (variance * math.sqrt(hands / 100))
            prob_negative = stats.norm.cdf(-z_score) * 100
        else:
            prob_negative = 50.0
        
        probabilities["negative_session"] = {
            "probability": prob_negative,
            "description": "Probabilité de finir en négatif"
        }
        
        return probabilities
    
    def _calculate_bankroll_requirements(self, winrate, variance, game_type):
        """Calcule les exigences de bankroll"""
        requirements = {}
        
        # Facteurs de risque de ruine
        risk_levels = {
            "conservative": 0.01,  # 1% de risque de ruine
            "moderate": 0.05,      # 5% de risque de ruine
            "aggressive": 0.10     # 10% de risque de ruine
        }
        
        for risk_name, risk_level in risk_levels.items():
            if winrate > 0:
                # Formule de Kelly simplifiée
                kelly_fraction = winrate / (variance ** 2)
                
                # Bankroll pour ce niveau de risque
                z_score = stats.norm.ppf(1 - risk_level)
                required_br = (z_score * variance) / (winrate / 100)
                
                requirements[risk_name] = {
                    "bankroll_bb": max(20, required_br),  # Minimum 20 BB
                    "risk_of_ruin": risk_level * 100,
                    "kelly_fraction": kelly_fraction
                }
            else:
                requirements[risk_name] = {
                    "bankroll_bb": float('inf'),
                    "risk_of_ruin": 100,
                    "kelly_fraction": 0
                }
        
        # Recommandations spécifiques au type de jeu
        if game_type.startswith("cash"):
            requirements["recommended"] = {
                "bankroll_bb": requirements["moderate"]["bankroll_bb"],
                "description": "Recommandation pour cash game"
            }
        elif game_type == "tournament":
            requirements["recommended"] = {
                "bankroll_buyins": 100,
                "description": "Recommandation pour tournois"
            }
        
        return requirements
    
    def _simulate_results(self, winrate, variance, hands, num_simulations=1000):
        """Simule des résultats possibles"""
        results = []
        
        expected = (winrate / 100) * hands
        std_dev = variance * math.sqrt(hands / 100)
        
        # Générer des simulations
        for _ in range(num_simulations):
            result = np.random.normal(expected, std_dev)
            results.append(result)
        
        results = np.array(results)
        
        return {
            "simulations": results.tolist(),
            "statistics": {
                "mean": float(np.mean(results)),
                "median": float(np.median(results)),
                "std": float(np.std(results)),
                "min": float(np.min(results)),
                "max": float(np.max(results)),
                "percentiles": {
                    "5th": float(np.percentile(results, 5)),
                    "25th": float(np.percentile(results, 25)),
                    "75th": float(np.percentile(results, 75)),
                    "95th": float(np.percentile(results, 95))
                }
            },
            "probability_positive": float(np.mean(results > 0) * 100),
            "probability_above_expected": float(np.mean(results > expected) * 100)
        }
    
    def _calculate_risk_metrics(self, winrate, variance, hands):
        """Calcule des métriques de risque avancées"""
        expected = (winrate / 100) * hands
        std_dev = variance * math.sqrt(hands / 100)
        
        # Sharpe ratio (adapté pour le poker)
        sharpe_ratio = expected / std_dev if std_dev > 0 else 0
        
        # Value at Risk (VaR) - perte maximale probable à 95%
        var_95 = expected - 1.96 * std_dev
        
        # Expected Shortfall - perte moyenne au-delà du VaR
        es_95 = expected - 2.5 * std_dev
        
        # Maximum Drawdown probable
        max_drawdown = -3 * std_dev
        
        return {
            "sharpe_ratio": sharpe_ratio,
            "value_at_risk_95": var_95,
            "expected_shortfall_95": es_95,
            "max_drawdown_probable": max_drawdown,
            "volatility": std_dev,
            "risk_adjusted_return": expected / abs(max_drawdown) if max_drawdown != 0 else 0
        }
    
    def analyze_session_variance(self, session_results):
        """
        Analyse la variance d'une session de jeu
        
        Args:
            session_results (list): Liste des résultats par main/tournoi
            
        Returns:
            dict: Analyse de variance de la session
        """
        if not session_results:
            return {"error": "Aucun résultat fourni"}
        
        results = np.array(session_results)
        
        # Statistiques de base
        total_result = np.sum(results)
        mean_result = np.mean(results)
        std_dev = np.std(results)
        
        # Analyse des runs
        runs_analysis = self._analyze_runs(results)
        
        # Détection des outliers
        outliers = self._detect_outliers(results)
        
        # Analyse de la distribution
        distribution_analysis = self._analyze_distribution(results)
        
        return {
            "session_summary": {
                "total_hands": len(results),
                "total_result": total_result,
                "mean_per_hand": mean_result,
                "standard_deviation": std_dev,
                "best_hand": float(np.max(results)),
                "worst_hand": float(np.min(results))
            },
            "runs_analysis": runs_analysis,
            "outliers": outliers,
            "distribution_analysis": distribution_analysis,
            "variance_metrics": {
                "coefficient_of_variation": std_dev / abs(mean_result) if mean_result != 0 else float('inf'),
                "variance": float(np.var(results)),
                "range": float(np.max(results) - np.min(results))
            }
        }
    
    def _analyze_runs(self, results):
        """Analyse les runs (séquences) de gains/pertes"""
        runs = []
        current_run = {"type": None, "length": 0, "total": 0}
        
        for result in results:
            run_type = "win" if result > 0 else "loss" if result < 0 else "break_even"
            
            if current_run["type"] == run_type:
                current_run["length"] += 1
                current_run["total"] += result
            else:
                if current_run["type"] is not None:
                    runs.append(current_run.copy())
                current_run = {"type": run_type, "length": 1, "total": result}
        
        # Ajouter le dernier run
        if current_run["type"] is not None:
            runs.append(current_run)
        
        # Analyser les runs
        win_runs = [r for r in runs if r["type"] == "win"]
        loss_runs = [r for r in runs if r["type"] == "loss"]
        
        return {
            "total_runs": len(runs),
            "win_runs": {
                "count": len(win_runs),
                "avg_length": np.mean([r["length"] for r in win_runs]) if win_runs else 0,
                "max_length": max([r["length"] for r in win_runs]) if win_runs else 0,
                "avg_total": np.mean([r["total"] for r in win_runs]) if win_runs else 0
            },
            "loss_runs": {
                "count": len(loss_runs),
                "avg_length": np.mean([r["length"] for r in loss_runs]) if loss_runs else 0,
                "max_length": max([r["length"] for r in loss_runs]) if loss_runs else 0,
                "avg_total": np.mean([r["total"] for r in loss_runs]) if loss_runs else 0
            }
        }
    
    def _detect_outliers(self, results):
        """Détecte les outliers dans les résultats"""
        q1 = np.percentile(results, 25)
        q3 = np.percentile(results, 75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = results[(results < lower_bound) | (results > upper_bound)]
        
        return {
            "count": len(outliers),
            "percentage": len(outliers) / len(results) * 100,
            "values": outliers.tolist(),
            "extreme_positive": outliers[outliers > upper_bound].tolist(),
            "extreme_negative": outliers[outliers < lower_bound].tolist()
        }
    
    def _analyze_distribution(self, results):
        """Analyse la distribution des résultats"""
        # Test de normalité
        shapiro_stat, shapiro_p = stats.shapiro(results[:5000])  # Limiter pour Shapiro-Wilk
        
        # Skewness et kurtosis
        skewness = stats.skew(results)
        kurtosis = stats.kurtosis(results)
        
        return {
            "normality_test": {
                "shapiro_statistic": shapiro_stat,
                "shapiro_p_value": shapiro_p,
                "is_normal": shapiro_p > 0.05
            },
            "skewness": skewness,
            "kurtosis": kurtosis,
            "distribution_type": self._classify_distribution(skewness, kurtosis)
        }
    
    def _classify_distribution(self, skewness, kurtosis):
        """Classifie le type de distribution"""
        if abs(skewness) < 0.5 and abs(kurtosis) < 0.5:
            return "approximately_normal"
        elif skewness > 0.5:
            return "right_skewed"
        elif skewness < -0.5:
            return "left_skewed"
        elif kurtosis > 0.5:
            return "heavy_tailed"
        elif kurtosis < -0.5:
            return "light_tailed"
        else:
            return "unknown"

if __name__ == "__main__":
    # Test du calculateur de variance
    calculator = VarianceCalculator()
    
    print("🧪 Test du calculateur de variance")
    print("=" * 50)
    
    # Test calcul de variance
    result = calculator.calculate_variance_metrics(
        game_type="cash_6max",
        winrate=5.0,  # 5 BB/100
        hands_played=10000,
        style="tight_aggressive"
    )
    
    if "error" not in result:
        print(f"Résultat attendu: {result['expected_result']:.1f} BB")
        print(f"Écart-type: {result['standard_deviation']:.1f} BB")
        print(f"Intervalle 95%: {result['confidence_intervals']['95%']['lower']:.1f} à {result['confidence_intervals']['95%']['upper']:.1f} BB")
        print(f"Probabilité de finir négatif: {result['downswing_probabilities']['negative_session']['probability']:.1f}%")
        print(f"Bankroll recommandée: {result['bankroll_requirements']['recommended']['bankroll_bb']:.0f} BB")
    else:
        print(f"Erreur: {result['error']}")
    
    # Test analyse de session
    session_results = np.random.normal(0.5, 5, 1000)  # Simulation de 1000 mains
    session_analysis = calculator.analyze_session_variance(session_results)
    
    print(f"\nAnalyse de session:")
    print(f"Total: {session_analysis['session_summary']['total_result']:.1f} BB")
    print(f"Runs de gains: {session_analysis['runs_analysis']['win_runs']['count']}")
    print(f"Outliers: {session_analysis['outliers']['count']} ({session_analysis['outliers']['percentage']:.1f}%)")
