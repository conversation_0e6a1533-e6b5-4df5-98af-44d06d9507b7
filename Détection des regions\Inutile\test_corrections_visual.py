#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test visuel des corrections
==========================

Ce script teste spécifiquement pourquoi les corrections
ne s'affichent pas visuellement dans l'interface.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def test_visual_corrections():
    """Test visuel des corrections avec logs détaillés"""
    
    print("🔍 Test visuel des corrections")
    print("=" * 60)
    
    # C<PERSON>er une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection réalistes (comme dans vos logs)
    test_results = {
        "carte_1m": {"text": "10", "colors": ["green", "white"], "confidence": 0.90},
        "carte_2m": {"text": "7", "colors": ["green", "white"], "confidence": 0.88},
        "card_1": {"text": "Q", "colors": ["black", "white"], "confidence": 0.85},
        "card_2": {"text": "A", "colors": ["black", "white"], "confidence": 0.82},
    }
    
    print("📊 Résultats de détection simulés (comme dans vos logs):")
    for region, data in test_results.items():
        print(f"  {region}: '{data['text']}' {data['colors']} (conf: {data['confidence']})")
    
    # Test 1: Analyse AVANT corrections
    print("\n🔍 Test 1: Analyse AVANT corrections")
    print("-" * 50)
    
    try:
        analysis_before, formatted_before = advisor.analyze_detection_results(test_results)
        print("✅ Analyse AVANT réussie")
        print(f"📊 Board AVANT: '{analysis_before.get('board_cards_text', 'N/A')}'")
        print(f"📊 Main AVANT: '{analysis_before.get('hand_cards_text', 'N/A')}'")
        print(f"📊 Recommandation AVANT: '{analysis_before.get('recommendation', 'N/A')}'")
        
        print(f"\n📝 Texte formaté AVANT (longueur: {len(formatted_before)}):")
        print("=" * 40)
        print(formatted_before[:200] + "..." if len(formatted_before) > 200 else formatted_before)
        print("=" * 40)
        
    except Exception as e:
        print(f"❌ Erreur dans l'analyse AVANT: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return False
    
    # Test 2: Appliquer des corrections
    print("\n🛠️ Test 2: Application de corrections")
    print("-" * 50)
    
    corrections = [
        {"region": "card_1", "detected": "Q", "corrected": "K", "suit": "Pique"},
        {"region": "card_2", "detected": "A", "corrected": "", "suit": ""},  # Pas de cartes
        {"region": "carte_1m", "detected": "10", "corrected": "A", "suit": "Cœur"},
    ]
    
    print("📝 Corrections actives AVANT:")
    print(f"  {advisor.get_manual_corrections()}")
    
    for correction in corrections:
        region = correction["region"]
        detected = correction["detected"]
        corrected = correction["corrected"]
        suit = correction["suit"]
        
        # Appliquer la correction
        success = advisor.set_manual_correction(region, corrected, suit)
        
        if corrected == "":
            print(f"  ✅ {region}: '{detected}' → 'Pas de cartes' (succès: {success})")
        else:
            print(f"  ✅ {region}: '{detected}' → '{corrected} de {suit}' (succès: {success})")
    
    print("\n📝 Corrections actives APRÈS:")
    corrections_after = advisor.get_manual_corrections()
    print(f"  {corrections_after}")
    
    # Test 3: Vider le cache (simulation de refresh_advisor_analysis)
    print("\n🗑️ Test 3: Vidage du cache")
    print("-" * 50)
    
    if hasattr(advisor, 'cache'):
        advisor.cache.clear()
        print("✅ Cache vidé")
    else:
        print("ℹ️ Pas de cache à vider")
    
    # Test 4: Analyse APRÈS corrections
    print("\n🔍 Test 4: Analyse APRÈS corrections")
    print("-" * 50)
    
    try:
        analysis_after, formatted_after = advisor.analyze_detection_results(test_results)
        print("✅ Analyse APRÈS réussie")
        print(f"📊 Board APRÈS: '{analysis_after.get('board_cards_text', 'N/A')}'")
        print(f"📊 Main APRÈS: '{analysis_after.get('hand_cards_text', 'N/A')}'")
        print(f"📊 Recommandation APRÈS: '{analysis_after.get('recommendation', 'N/A')}'")
        
        print(f"\n📝 Texte formaté APRÈS (longueur: {len(formatted_after)}):")
        print("=" * 40)
        print(formatted_after[:200] + "..." if len(formatted_after) > 200 else formatted_after)
        print("=" * 40)
        
    except Exception as e:
        print(f"❌ Erreur dans l'analyse APRÈS: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return False
    
    # Test 5: Comparaison détaillée
    print("\n📈 Test 5: Comparaison détaillée AVANT/APRÈS")
    print("-" * 50)
    
    board_before = analysis_before.get('board_cards_text', '')
    board_after = analysis_after.get('board_cards_text', '')
    hand_before = analysis_before.get('hand_cards_text', '')
    hand_after = analysis_after.get('hand_cards_text', '')
    
    print(f"🃏 Board:")
    print(f"  AVANT: '{board_before}'")
    print(f"  APRÈS: '{board_after}'")
    if board_before != board_after:
        print(f"  🔄 CHANGEMENT DÉTECTÉ ✅")
    else:
        print(f"  ⚠️ AUCUN CHANGEMENT")
    
    print(f"\n🃏 Main:")
    print(f"  AVANT: '{hand_before}'")
    print(f"  APRÈS: '{hand_after}'")
    if hand_before != hand_after:
        print(f"  🔄 CHANGEMENT DÉTECTÉ ✅")
    else:
        print(f"  ⚠️ AUCUN CHANGEMENT")
    
    print(f"\n📝 Texte formaté:")
    print(f"  AVANT: {len(formatted_before)} caractères")
    print(f"  APRÈS: {len(formatted_after)} caractères")
    if formatted_before != formatted_after:
        print(f"  🔄 CHANGEMENT DÉTECTÉ ✅")
        
        # Trouver les différences
        lines_before = formatted_before.split('\n')
        lines_after = formatted_after.split('\n')
        
        print(f"\n🔍 Différences détectées:")
        for i, (line_before, line_after) in enumerate(zip(lines_before, lines_after)):
            if line_before != line_after:
                print(f"  Ligne {i+1}:")
                print(f"    AVANT: '{line_before}'")
                print(f"    APRÈS: '{line_after}'")
    else:
        print(f"  ⚠️ AUCUN CHANGEMENT DANS LE TEXTE FORMATÉ")
    
    # Test 6: Vérification des corrections dans l'analyse
    print("\n🔍 Test 6: Vérification des corrections dans l'analyse")
    print("-" * 50)
    
    manual_corrections = analysis_after.get('manual_corrections', [])
    if manual_corrections:
        print(f"✅ Corrections détectées dans l'analyse: {manual_corrections}")
    else:
        print(f"⚠️ Aucune correction détectée dans l'analyse")
    
    detected_regions = analysis_after.get('detected_regions', [])
    print(f"📍 Régions détectées: {detected_regions}")
    
    print("\n✅ Test visuel des corrections terminé!")
    print("\n📝 Résumé:")
    print("  ✅ Analyse AVANT corrections")
    print("  ✅ Application des corrections")
    print("  ✅ Vidage du cache")
    print("  ✅ Analyse APRÈS corrections")
    print("  ✅ Comparaison détaillée")
    print("  ✅ Vérification des corrections")
    
    # Diagnostic final
    if board_before != board_after or hand_before != hand_after:
        print("\n🎉 DIAGNOSTIC: Les corrections FONCTIONNENT !")
        print("   Le problème est probablement dans l'affichage de l'interface.")
    else:
        print("\n⚠️ DIAGNOSTIC: Les corrections ne s'appliquent PAS !")
        print("   Le problème est dans la logique de correction.")
    
    return True

if __name__ == "__main__":
    test_visual_corrections()
