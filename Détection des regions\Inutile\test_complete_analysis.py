#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'analyse complète avec toutes les régions
=================================================

Ce script teste que le conseiller poker prend en compte
toutes les régions cochées pour donner une analyse complète.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def test_complete_analysis():
    """Test de l'analyse complète avec toutes les régions"""
    
    print("🎯 Test de l'analyse complète avec toutes les régions")
    print("=" * 70)
    
    # Créer une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection avec TOUTES les régions cochées
    test_results = {
        # Cartes du board
        "card_1": {"text": "A", "colors": ["red", "white"], "confidence": 0.90},
        "card_2": {"text": "K", "colors": ["black", "white"], "confidence": 0.85},
        "card_3": {"text": "Q", "colors": ["red", "white"], "confidence": 0.82},
        "card_4": {"text": "", "colors": [], "confidence": 0.0, "status": "selected_but_not_detected"},
        "card_5": {"text": "", "colors": [], "confidence": 0.0, "status": "selected_but_not_detected"},
        
        # Cartes en main
        "carte_1m": {"text": "J", "colors": ["black", "white"], "confidence": 0.88},
        "carte_2m": {"text": "10", "colors": ["red", "white"], "confidence": 0.86},
        
        # Jetons et mises
        "chips_player": {"text": "1250", "colors": ["white", "black"], "confidence": 0.75},
        "bet_player": {"text": "50", "colors": ["white", "red"], "confidence": 0.70},
        "pot": {"text": "150", "colors": ["yellow", "black"], "confidence": 0.80},
        
        # Adversaires
        "chips_player1": {"text": "800", "colors": ["white", "black"], "confidence": 0.65},
        "bet_player1": {"text": "50", "colors": ["white", "red"], "confidence": 0.60},
        "chips_player2": {"text": "2000", "colors": ["white", "black"], "confidence": 0.70},
        
        # Métadonnées des régions sélectionnées
        "_metadata": {
            "selected_regions": [
                "card_1", "card_2", "card_3", "card_4", "card_5",
                "carte_1m", "carte_2m",
                "chips_player", "bet_player", "pot",
                "chips_player1", "bet_player1", "chips_player2"
            ],
            "total_selected": 13,
            "detected_regions": [
                "card_1", "card_2", "card_3", "carte_1m", "carte_2m",
                "chips_player", "bet_player", "pot", "chips_player1", "bet_player1", "chips_player2"
            ],
            "total_detected": 11
        }
    }
    
    print("📊 Simulation d'une situation complète de poker:")
    print("   🃏 Board: A♥ K♠ Q♥ (flop)")
    print("   🎴 Main: J♠ 10♥ (quinte possible)")
    print("   💰 Vos jetons: 1250")
    print("   💲 Votre mise: 50")
    print("   🏆 Pot: 150")
    print("   👥 Adversaire 1: 800 jetons, mise 50")
    print("   👥 Adversaire 2: 2000 jetons")
    print()
    
    # Test 1: Analyse SANS enrichissement (ancienne méthode)
    print("🔍 Test 1: Analyse SANS enrichissement (ancienne méthode)")
    print("-" * 60)
    
    # Supprimer les métadonnées pour simuler l'ancienne méthode
    results_without_metadata = {k: v for k, v in test_results.items() if k != "_metadata"}
    
    analysis_old, formatted_old = advisor.analyze_detection_results(results_without_metadata)
    
    print(f"📊 Board détecté: {analysis_old.get('board_cards_text', 'N/A')}")
    print(f"📊 Main détectée: {analysis_old.get('hand_cards_text', 'N/A')}")
    print(f"💰 Jetons analysés: {analysis_old.get('my_stack', 'N/A')}")
    print(f"🏆 Pot analysé: {analysis_old.get('pot', 'N/A')}")
    print(f"🎯 Recommandation: {analysis_old.get('recommendation', 'N/A')}")
    print()
    
    # Test 2: Analyse AVEC enrichissement (nouvelle méthode)
    print("🔍 Test 2: Analyse AVEC enrichissement (nouvelle méthode)")
    print("-" * 60)
    
    analysis_new, formatted_new = advisor.analyze_detection_results(test_results)
    
    print(f"📊 Board détecté: {analysis_new.get('board_cards_text', 'N/A')}")
    print(f"📊 Main détectée: {analysis_new.get('hand_cards_text', 'N/A')}")
    print(f"💰 Jetons analysés: {analysis_new.get('my_stack', 'N/A')}")
    print(f"🏆 Pot analysé: {analysis_new.get('pot', 'N/A')}")
    print(f"🎯 Recommandation: {analysis_new.get('recommendation', 'N/A')}")
    print(f"📍 Régions sélectionnées: {analysis_new.get('selected_regions', [])}")
    print(f"📈 Portée d'analyse: {analysis_new.get('analysis_scope', 'N/A')}")
    print()
    
    # Test 3: Comparaison des analyses
    print("📈 Test 3: Comparaison des analyses")
    print("-" * 60)
    
    improvements = []
    
    # Comparer les cartes
    if analysis_old.get('board_cards_text') != analysis_new.get('board_cards_text'):
        improvements.append(f"Board: '{analysis_old.get('board_cards_text')}' → '{analysis_new.get('board_cards_text')}'")
    
    if analysis_old.get('hand_cards_text') != analysis_new.get('hand_cards_text'):
        improvements.append(f"Main: '{analysis_old.get('hand_cards_text')}' → '{analysis_new.get('hand_cards_text')}'")
    
    # Comparer les jetons
    if analysis_old.get('my_stack') != analysis_new.get('my_stack'):
        improvements.append(f"Jetons: {analysis_old.get('my_stack')} → {analysis_new.get('my_stack')}")
    
    # Comparer le pot
    if analysis_old.get('pot') != analysis_new.get('pot'):
        improvements.append(f"Pot: {analysis_old.get('pot')} → {analysis_new.get('pot')}")
    
    # Comparer les recommandations
    if analysis_old.get('recommendation') != analysis_new.get('recommendation'):
        improvements.append(f"Recommandation: '{analysis_old.get('recommendation')}' → '{analysis_new.get('recommendation')}'")
    
    if improvements:
        print("✅ Améliorations détectées:")
        for improvement in improvements:
            print(f"   🔄 {improvement}")
    else:
        print("ℹ️ Aucune différence détectée entre les deux analyses")
    
    print()
    
    # Test 4: Analyse du texte formaté
    print("📝 Test 4: Analyse du texte formaté")
    print("-" * 60)
    
    print(f"Longueur ancienne analyse: {len(formatted_old)} caractères")
    print(f"Longueur nouvelle analyse: {len(formatted_new)} caractères")
    
    if len(formatted_new) > len(formatted_old):
        print(f"✅ Analyse enrichie: +{len(formatted_new) - len(formatted_old)} caractères")
    
    # Chercher des mots-clés spécifiques
    keywords = ["régions", "sélectionnées", "jetons", "pot", "adversaire", "mise"]
    new_keywords = []
    
    for keyword in keywords:
        if keyword.lower() in formatted_new.lower() and keyword.lower() not in formatted_old.lower():
            new_keywords.append(keyword)
    
    if new_keywords:
        print(f"✅ Nouveaux éléments d'analyse: {', '.join(new_keywords)}")
    
    print()
    
    # Test 5: Affichage de l'analyse complète
    print("📋 Test 5: Analyse complète formatée")
    print("-" * 60)
    
    print("Analyse avec toutes les régions cochées:")
    print("=" * 50)
    print(formatted_new)
    print("=" * 50)
    
    # Test 6: Vérification des données extraites
    print("\n🔍 Test 6: Données extraites détaillées")
    print("-" * 60)
    
    data = advisor.extract_poker_data(test_results)
    
    print(f"📊 Portée d'analyse: {data.get('analysis_scope', 'N/A')}")
    print(f"📍 Régions sélectionnées: {len(data.get('selected_regions', []))}")
    print(f"🔍 Régions détectées: {len(data.get('detected_regions', []))}")
    print(f"💰 Stack du joueur: {data.get('my_stack', 'N/A')}")
    print(f"🏆 Pot: {data.get('pot', 'N/A')}")
    print(f"👥 Stacks adverses: {data.get('player_stacks', {})}")
    print(f"💲 Mises adverses: {data.get('player_bets', {})}")
    
    print("\n✅ Test de l'analyse complète terminé!")
    print("\n📝 Résumé des fonctionnalités testées:")
    print("  ✅ Enrichissement des résultats avec métadonnées")
    print("  ✅ Analyse des types de régions sélectionnées")
    print("  ✅ Portée d'analyse adaptative")
    print("  ✅ Prise en compte des jetons et mises")
    print("  ✅ Analyse des adversaires")
    print("  ✅ Recommandations basées sur la situation complète")
    
    # Diagnostic final
    if data.get('analysis_scope') == 'complete':
        print("\n🎉 SUCCÈS: Le conseiller analyse maintenant TOUTES les régions cochées !")
    else:
        print(f"\n⚠️ ATTENTION: Portée d'analyse limitée: {data.get('analysis_scope')}")
    
    return True

if __name__ == "__main__":
    test_complete_analysis()
