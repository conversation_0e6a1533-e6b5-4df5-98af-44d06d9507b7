#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la détection des jetons des adversaires avec gros montants
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAdvisorLight
import numpy as np
import cv2

def test_process_amount_text():
    """Test de l'algorithme de reconstitution des montants"""
    print("🧪 TEST DE L'ALGORITHME DE RECONSTITUTION DES MONTANTS")
    print("=" * 60)

    # Créer une instance simple pour tester la méthode
    class SimpleDetector:
        def process_amount_text(self, texts):
            """Version simplifiée de l'algorithme de reconstitution"""
            try:
                if not texts:
                    return ""

                print(f"🔍 Textes reçus: {texts}")

                best_text = ""
                best_score = 0

                for text in texts:
                    # Nettoyer le texte
                    cleaned = ''.join(c for c in text if c.isdigit() or c in '., ')
                    cleaned = cleaned.strip()

                    if ' ' in cleaned:
                        parts = [p.strip() for p in cleaned.split() if p.strip()]
                        if len(parts) == 2:
                            if len(parts[1]) <= 2:  # Décimal
                                cleaned = f"{parts[0]}.{parts[1]}"
                            else:  # Gros nombre
                                cleaned = f"{parts[0]}{parts[1]}"
                        elif len(parts) > 2:
                            # Reconstitution complexe
                            cleaned = ''.join(parts)

                    # Score basé sur la longueur et la cohérence
                    score = len(cleaned) + (2 if any(c.isdigit() for c in cleaned) else 0)

                    if score > best_score:
                        best_text = cleaned
                        best_score = score

                return best_text
            except Exception as e:
                print(f"❌ Erreur: {e}")
                return ""

    detector = SimpleDetector()

    # Tests avec différents types de montants fragmentés
    test_cases = [
        # Cas simples
        ["1234"], # Montant normal
        ["12 34"], # Montant en 2 parties
        ["1 234"], # Montant avec première partie courte

        # Cas complexes (gros montants)
        ["112 800"], # Gros montant en 2 parties
        ["1 12 800"], # Gros montant en 3 parties
        ["5 6 7 8"], # Montant fragmenté en 4 parties
        ["68 4 0"], # Montant avec zéro final
        ["6 84 0"], # Montant avec zéro final (variante)

        # Cas avec décimales
        ["68 4"], # Décimal simple
        ["112 50"], # Décimal plus gros
        ["6 8 4"], # Décimal fragmenté

        # Cas extrêmes
        ["1 2 3 4 5 6"], # Très fragmenté
        ["999 999"], # Très gros montant
    ]

    for i, test_texts in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_texts}")
        result = detector.process_amount_text(test_texts)
        print(f"   ✅ Résultat: '{result}'")

        # Essayer de convertir en float pour vérifier
        try:
            float_val = float(result.replace(',', '.'))
            print(f"   💰 Valeur numérique: {float_val}")
        except:
            print(f"   ❌ Impossible de convertir en nombre")

def test_jetons_simulation():
    """Simulation de détection de jetons d'adversaires"""
    advisor = PokerAdvisorLight()

    # Simuler des résultats de détection avec différents montants
    simulated_results = {
        "jetons_joueur1": {"text": "1234", "colors": []},
        "jetons_joueur2": {"text": "56 78", "colors": []},  # Fragmenté
        "jetons_joueur3": {"text": "9 87 65", "colors": []},  # Très fragmenté
        "jetons_joueur4": {"text": "12 3", "colors": []},  # Petit montant fragmenté
        "jetons_joueur5": {"text": "999 999", "colors": []},  # Très gros montant
        "mes_jetons": {"text": "500", "colors": []},
        "pot_total": {"text": "100", "colors": ["white"]},
    }

    print("\n🧪 TEST DE SIMULATION DES JETONS D'ADVERSAIRES")
    print("=" * 60)

    # Traiter les résultats simulés
    data = advisor.extract_poker_data(simulated_results)

    print(f"\n📊 RÉSULTATS DE L'EXTRACTION:")
    print(f"   💰 Mes jetons: {data['my_stack']}")
    print(f"   🎯 Pot total: {data['pot_total']}")
    print(f"   👥 Jetons adversaires:")
    for player, amount in data["player_stacks"].items():
        print(f"      - {player}: {amount} BB")

    print(f"\n📈 STATISTIQUES:")
    print(f"   🔍 Régions détectées: {len(data['detected_regions'])}")
    print(f"   💰 Tapis effectif: {data['effective_stack']} BB")

    # Calculer la moyenne des tapis
    all_stacks = [data['my_stack']] + list(data["player_stacks"].values())
    if all_stacks:
        avg_stack = sum(all_stacks) / len(all_stacks)
        print(f"   📊 Moyenne des tapis: {avg_stack:.1f} BB")

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS DE DÉTECTION DES JETONS")
    print("=" * 60)

    # Test 1: Algorithme de reconstitution
    test_process_amount_text()

    # Test 2: Simulation complète
    test_jetons_simulation()

    print("\n✅ TESTS TERMINÉS")
