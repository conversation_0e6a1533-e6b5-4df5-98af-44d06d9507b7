"""
Test de la disponibilité de CUDA et de l'initialisation de PaddleOCR
"""

import os
import sys
import time

def test_torch():
    """Teste l'installation de PyTorch et la disponibilité de CUDA"""
    print("\n=== Test de PyTorch et CUDA ===")
    try:
        import torch
        print(f"PyTorch installé: version {torch.__version__}")
        
        cuda_available = torch.cuda.is_available()
        print(f"CUDA disponible: {cuda_available}")
        
        if cuda_available:
            print(f"Version CUDA: {torch.version.cuda}")
            print(f"Nombre de GPUs: {torch.cuda.device_count()}")
            print(f"Nom du GPU: {torch.cuda.get_device_name(0)}")
            print(f"Mémoire totale: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024:.2f} Go")
            print(f"Mémoire disponible: {torch.cuda.memory_reserved(0) / 1024 / 1024 / 1024:.2f} Go")
            
            # Test simple avec CUDA
            print("\nTest de calcul avec CUDA...")
            start_time = time.time()
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.matmul(x, y)
            torch.cuda.synchronize()
            end_time = time.time()
            print(f"Temps d'exécution: {end_time - start_time:.4f} secondes")
            
            # Test simple sans CUDA
            print("\nTest de calcul sans CUDA...")
            start_time = time.time()
            x = torch.randn(1000, 1000)
            y = torch.randn(1000, 1000)
            z = torch.matmul(x, y)
            end_time = time.time()
            print(f"Temps d'exécution: {end_time - start_time:.4f} secondes")
            
        return cuda_available
    except ImportError:
        print("PyTorch n'est pas installé")
        return False
    except Exception as e:
        print(f"Erreur lors du test de PyTorch: {e}")
        return False

def test_paddleocr():
    """Teste l'initialisation de PaddleOCR avec et sans CUDA"""
    print("\n=== Test de PaddleOCR ===")
    try:
        from paddleocr import PaddleOCR
        print("PaddleOCR est installé")
        
        # Test avec CUDA si disponible
        cuda_available = False
        try:
            import torch
            cuda_available = torch.cuda.is_available()
        except:
            pass
        
        print(f"\nTest d'initialisation de PaddleOCR (GPU: {cuda_available})...")
        try:
            start_time = time.time()
            ocr = PaddleOCR(use_angle_cls=True, lang='fr', use_gpu=cuda_available, show_log=True)
            end_time = time.time()
            print(f"Temps d'initialisation: {end_time - start_time:.2f} secondes")
            print("PaddleOCR initialisé avec succès")
            
            # Test de détection sur une image simple si disponible
            test_image = "test.jpg"
            if os.path.exists(test_image):
                print(f"\nTest de détection sur {test_image}...")
                start_time = time.time()
                result = ocr.ocr(test_image, cls=True)
                end_time = time.time()
                print(f"Temps de détection: {end_time - start_time:.2f} secondes")
                print(f"Résultat: {result}")
            
            return True
        except Exception as e:
            print(f"Erreur lors de l'initialisation de PaddleOCR avec GPU={cuda_available}: {e}")
            
            print("\nTentative avec des paramètres minimaux...")
            try:
                ocr = PaddleOCR(use_angle_cls=False, lang='en', use_gpu=False, show_log=True)
                print("PaddleOCR initialisé avec des paramètres minimaux")
                return True
            except Exception as e2:
                print(f"Erreur lors de l'initialisation de PaddleOCR avec paramètres minimaux: {e2}")
                return False
    except ImportError:
        print("PaddleOCR n'est pas installé")
        return False
    except Exception as e:
        print(f"Erreur lors du test de PaddleOCR: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Test de compatibilité CUDA pour Poker Advisor ===")
    print(f"Python version: {sys.version}")
    
    # Test de PyTorch et CUDA
    cuda_available = test_torch()
    
    # Test de PaddleOCR
    paddleocr_ok = test_paddleocr()
    
    # Résumé
    print("\n=== Résumé ===")
    print(f"PyTorch avec CUDA: {'✅ OK' if cuda_available else '❌ Non disponible'}")
    print(f"PaddleOCR: {'✅ OK' if paddleocr_ok else '❌ Problème d'initialisation'}")
    
    if not cuda_available:
        print("\nRecommandations pour activer CUDA:")
        print("1. Assurez-vous d'avoir une carte graphique NVIDIA compatible CUDA")
        print("2. Installez les pilotes NVIDIA à jour")
        print("3. Installez CUDA Toolkit (https://developer.nvidia.com/cuda-downloads)")
        print("4. Réinstallez PyTorch avec support CUDA:")
        print("   pip install torch==2.0.1+cu118 torchvision==0.15.2+cu118 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118")
    
    if not paddleocr_ok:
        print("\nRecommandations pour PaddleOCR:")
        print("1. Réinstallez PaddleOCR:")
        print("   pip uninstall -y paddleocr paddlepaddle")
        print("   pip install paddlepaddle==2.4.2")
        print("   pip install paddleocr==*******")

if __name__ == "__main__":
    main()
