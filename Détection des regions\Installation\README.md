# Poker Advisor - Module de Détection des Régions

Module de détection pour l'application Poker Advisor. Ce module permet de détecter le texte et les couleurs dans des régions spécifiques d'une image.

## Fichiers principaux

- **detector.py** : Module principal de détection des cartes et des couleurs
- **detector_gui.py** : Interface graphique pour le module de détection
- **detector_gui_all_regions.py** : Version améliorée qui utilise toutes les régions disponibles avec les coordonnées de calibration_simple.py

## Scripts de lancement

- **lancer_detector.bat** : Lance l'interface graphique standard
- **lancer_detector_all_regions.bat** : Lance l'interface graphique améliorée avec toutes les régions

## Fonctionnalités

- Interface graphique conviviale avec thème sombre
- Chargement de la configuration depuis `C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json`
- Sélection des régions à analyser en temps réel
- Détection de texte avec PaddleOCR
- Support CUDA pour des performances optimales
- Détection de couleurs avec OpenCV
- Génération de résultats au format JSON
- Génération d'une image de prévisualisation des régions

## Prérequis

- Python 3.8 ou supérieur
- OpenCV
- PaddleOCR
- NumPy
- PyQt5
- PyTorch (pour le support CUDA)

## Installation

1. Utilisez le script d'installation automatique :

```bash
install_dependencies.bat
```

Ce script va :
- Installer PyTorch avec support CUDA si disponible
- Installer les autres dépendances
- Télécharger les modèles PaddleOCR
- Créer les dossiers nécessaires

Ou installez manuellement les dépendances :

```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

## Utilisation

### Interface graphique améliorée (recommandée)

```bash
lancer_detector_all_regions.bat
```

Cette version utilise toutes les régions disponibles, mais avec les coordonnées de la section 'roi' du fichier de configuration, comme dans calibration_simple.py.

### Interface graphique standard

```bash
lancer_detector.bat
```

L'interface graphique offre les fonctionnalités suivantes :
- Capture d'écran en temps réel
- Chargement d'images par bouton ou glisser-déposer
- Sélection des régions à analyser
- Détection automatique des cartes et des couleurs
- Affichage des résultats dans une interface conviviale
- Sauvegarde des résultats au format JSON
- Visualisation des régions sélectionnées

### En ligne de commande

```bash
python detector.py chemin/vers/image.jpg [options]
```

Options disponibles :
- `--config` : Chemin vers le fichier de configuration (par défaut: `config/poker_advisor_config.json`)
- `--output` : Chemin vers le fichier de sortie JSON (par défaut: `detection_results.json`)
- `--debug` : Générer une image de débogage
- `--debug-output` : Chemin vers l'image de débogage (par défaut: `detection_debug.jpg`)

## Configuration

Le module utilise en priorité le fichier de configuration situé dans `C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json`. Si ce fichier n'existe pas, il utilise le fichier local `config/poker_advisor_config.json`.

Le fichier de configuration contient deux sections principales :

1. **roi** : Coordonnées utilisées par calibration_simple.py
2. **all_regions** : Coordonnées alternatives pour toutes les régions

Format de la configuration :

```json
{
  "roi": {
    "card_1": {
      "left": 794,
      "top": 581,
      "width": 118,
      "height": 89
    },
    "card_2": {
      "left": 912,
      "top": 581,
      "width": 118,
      "height": 89
    },
    ...
  },
  "all_regions": {
    "card_1": {
      "x": 773,
      "y": 595,
      "width": 155,
      "height": 98
    },
    "card_2": {
      "x": 928,
      "y": 595,
      "width": 155,
      "height": 98
    },
    ...
  }
}
```

L'application `detector_gui_all_regions.py` utilise toutes les régions disponibles, mais avec les coordonnées de la section 'roi' du fichier de configuration, comme dans calibration_simple.py.

## Détection de couleurs

Le module détecte les couleurs suivantes :
- Rouge (hearts / cœur)
- Vert (clubs / trèfle)
- Bleu (diamonds / carreau)
- Noir (spades / pique)
