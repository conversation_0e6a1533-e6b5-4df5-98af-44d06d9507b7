#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test spécifique pour une situation avec relance recommandée
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "Détection des regions"))

from poker_advisor_light import PokerAd<PERSON><PERSON>ight

def test_situation_relance():
    """Test d'une situation qui devrait recommander une relance"""
    print("🧪 TEST SITUATION AVEC RELANCE RECOMMANDÉE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Situation avec main premium qui devrait recommander une relance
    simulated_results = {
        # Main premium AA
        "carte_1m": {"text": "As", "colors": ["red"]},
        "carte_2m": {"text": "Ah", "colors": ["red"]},
        
        # Stack moyen
        "mes_jetons": {"text": "100", "colors": []},
        
        # Pas de mise adverse (personne n'a misé)
        # Pot initial
        "pot_total": {"text": "3", "colors": ["white"]},
    }
    
    print("📊 SITUATION: AA en main, 100 BB de stack, personne n'a misé")
    
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"   Main: {analysis['hand_strength']}")
    print(f"   Équité: {analysis['equity'][0]}-{analysis['equity'][1]}%")
    print(f"   Action: {analysis['recommended_action']}")
    print(f"   Raison: {analysis['action_reason']}")
    print(f"   Montant extrait: {analysis.get('recommended_amount', 'Non trouvé')}")
    
    # Extraire et afficher la ligne du montant recommandé
    print(f"\n📋 AFFICHAGE DANS L'ANALYSE DÉTAILLÉE:")
    lines = formatted_analysis.split('\n')
    for line in lines:
        if "Montant recommandé" in line:
            print(f"   {line.strip()}")
            
            # Vérifier que c'est bien une relance
            if "relance" in line.lower():
                print(f"   ✅ EXCELLENT: Affiche une relance")
            elif "0 BB" in line and "se coucher" not in line:
                print(f"   ❌ PROBLÈME: Affiche encore 0 BB")
            else:
                print(f"   ⚠️ AUTRE: {line}")
            break
    else:
        print("   ❌ Ligne 'Montant recommandé' non trouvée")
    
    # Afficher l'analyse complète pour debug
    print(f"\n📄 ANALYSE COMPLÈTE:")
    print("=" * 60)
    print(formatted_analysis)

def test_situation_avec_mise():
    """Test d'une situation avec une mise à suivre"""
    print("\n🧪 TEST SITUATION AVEC MISE À SUIVRE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Situation avec main forte et mise adverse
    simulated_results = {
        # Main forte KK
        "carte_1m": {"text": "Ks", "colors": ["black"]},
        "carte_2m": {"text": "Kh", "colors": ["red"]},
        
        # Stack moyen
        "mes_jetons": {"text": "80", "colors": []},
        
        # Adversaire avec mise
        "jetons_joueur1": {"text": "60", "colors": ["white"]},
        "mise_joueur1": {"text": "5", "colors": ["white"]},
        
        # Pot
        "pot_total": {"text": "12", "colors": ["white"]},
    }
    
    print("📊 SITUATION: KK en main, 80 BB de stack, adversaire mise 5 BB")
    
    analysis, formatted_analysis = advisor.analyze_detection_results(simulated_results)
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"   Main: {analysis['hand_strength']}")
    print(f"   Action: {analysis['recommended_action']}")
    print(f"   Montant extrait: {analysis.get('recommended_amount', 'Non trouvé')}")
    
    # Extraire et afficher la ligne du montant recommandé
    lines = formatted_analysis.split('\n')
    for line in lines:
        if "Montant recommandé" in line:
            print(f"   Affichage: {line.strip()}")
            break

def test_extraction_directe():
    """Test direct de l'extraction de montant"""
    print("\n🧪 TEST EXTRACTION DIRECTE")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Test avec différentes actions
    test_actions = [
        "raise 2.5 BB",
        "RAISE 4BB", 
        "raise 3",
        "call 5 BB",
        "all-in",
        "fold"
    ]
    
    for action in test_actions:
        amount = advisor.extract_recommended_amount(action, 0)
        formatted = advisor.format_recommended_amount(amount, action, 0)
        
        print(f"🔍 Action: '{action}'")
        print(f"   Montant extrait: {amount}")
        print(f"   Formaté: '{formatted}'")
        print()

if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS DE RELANCE RECOMMANDÉE")
    print("=" * 60)
    
    # Test situation relance
    test_situation_relance()
    
    # Test situation avec mise
    test_situation_avec_mise()
    
    # Test extraction directe
    test_extraction_directe()
    
    print("\n✅ TESTS TERMINÉS")
    print("\n📋 RÉSUMÉ:")
    print("   L'application doit maintenant afficher le montant de relance recommandé")
    print("   Au lieu de 'Mise à suivre: 0 BB' on doit voir 'Montant recommandé: X BB (relance)'")
