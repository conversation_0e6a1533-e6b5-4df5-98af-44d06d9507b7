#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de debug pour analyser les causes de fermeture d'application.
"""

import os
import sys
import traceback
import signal
import atexit
from datetime import datetime

# Configuration du logging de debug
DEBUG_LOG = "debug_crash.log"

def log_debug(message):
    """Enregistre un message de debug avec timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    try:
        with open(DEBUG_LOG, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"Erreur lors de l'écriture du log de debug: {e}")

def signal_handler(signum, frame):
    """Gestionnaire de signaux pour capturer les arrêts inattendus"""
    log_debug(f"🚨 SIGNAL REÇU: {signum}")
    log_debug(f"📍 Frame: {frame}")
    log_debug("🔍 Stack trace:")
    for line in traceback.format_stack(frame):
        log_debug(f"   {line.strip()}")
    log_debug("💀 Application interrompue par signal")

def exception_handler(exc_type, exc_value, exc_traceback):
    """Gestionnaire d'exceptions non capturées"""
    if issubclass(exc_type, KeyboardInterrupt):
        log_debug("⌨️ Interruption clavier (Ctrl+C)")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    log_debug("💥 EXCEPTION NON CAPTURÉE:")
    log_debug(f"   Type: {exc_type.__name__}")
    log_debug(f"   Valeur: {exc_value}")
    log_debug("🔍 Traceback complet:")
    
    for line in traceback.format_exception(exc_type, exc_value, exc_traceback):
        for subline in line.strip().split('\n'):
            if subline:
                log_debug(f"   {subline}")

def cleanup_handler():
    """Fonction appelée à la sortie du programme"""
    log_debug("🧹 Nettoyage à la sortie du programme")
    log_debug("📊 Informations système finales:")
    
    try:
        import psutil
        cpu = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        log_debug(f"   CPU: {cpu:.1f}%")
        log_debug(f"   RAM: {memory.percent:.1f}%")
    except:
        log_debug("   Impossible d'obtenir les stats système")
    
    log_debug("👋 Fin du programme")

def setup_crash_monitoring():
    """Configure la surveillance des crashes"""
    log_debug("🔧 Configuration de la surveillance des crashes")
    
    # Gestionnaires de signaux
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Terminaison
    if hasattr(signal, 'SIGBREAK'):
        signal.signal(signal.SIGBREAK, signal_handler)  # Ctrl+Break (Windows)
    
    # Gestionnaire d'exceptions
    sys.excepthook = exception_handler
    
    # Fonction de nettoyage
    atexit.register(cleanup_handler)
    
    log_debug("✅ Surveillance des crashes configurée")

def monitor_memory_usage():
    """Surveille l'utilisation mémoire en continu"""
    try:
        import psutil
        import threading
        import time
        
        def memory_monitor():
            while True:
                try:
                    memory = psutil.virtual_memory()
                    if memory.percent > 90:
                        log_debug(f"🚨 ALERTE MÉMOIRE: {memory.percent:.1f}% utilisée")
                    elif memory.percent > 80:
                        log_debug(f"⚠️ Mémoire élevée: {memory.percent:.1f}%")
                    
                    time.sleep(10)  # Vérifier toutes les 10 secondes
                except Exception as e:
                    log_debug(f"❌ Erreur dans le moniteur mémoire: {e}")
                    break
        
        monitor_thread = threading.Thread(target=memory_monitor, daemon=True)
        monitor_thread.start()
        log_debug("✅ Moniteur mémoire démarré")
        
    except ImportError:
        log_debug("⚠️ psutil non disponible, pas de surveillance mémoire")

def check_common_issues():
    """Vérifie les problèmes courants qui peuvent causer des crashes"""
    log_debug("🔍 Vérification des problèmes courants:")
    
    # Vérifier l'espace disque
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_percent = (free / total) * 100
        log_debug(f"💾 Espace disque libre: {free_percent:.1f}%")
        if free_percent < 10:
            log_debug("🚨 ALERTE: Espace disque faible!")
    except Exception as e:
        log_debug(f"❌ Impossible de vérifier l'espace disque: {e}")
    
    # Vérifier les modules critiques
    critical_modules = ['torch', 'cv2', 'numpy', 'psutil']
    for module in critical_modules:
        try:
            __import__(module)
            log_debug(f"✅ Module {module} disponible")
        except ImportError:
            log_debug(f"❌ Module {module} manquant")
    
    # Vérifier CUDA
    try:
        import torch
        if torch.cuda.is_available():
            log_debug(f"✅ CUDA disponible: {torch.cuda.get_device_name(0)}")
            log_debug(f"💾 Mémoire GPU: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            log_debug("⚠️ CUDA non disponible")
    except Exception as e:
        log_debug(f"❌ Erreur CUDA: {e}")

def main():
    """Fonction principale de debug"""
    log_debug("=" * 60)
    log_debug("🚀 DÉMARRAGE DU SYSTÈME DE DEBUG CRASH")
    log_debug("=" * 60)
    
    # Configuration
    setup_crash_monitoring()
    monitor_memory_usage()
    check_common_issues()
    
    log_debug("✅ Système de debug configuré et actif")
    log_debug("📝 Les événements seront enregistrés dans: " + DEBUG_LOG)
    
    return True

if __name__ == "__main__":
    main()
    print("🔍 Système de debug des crashes configuré")
    print(f"📝 Logs dans: {DEBUG_LOG}")
    print("🚀 Vous pouvez maintenant lancer votre application")
