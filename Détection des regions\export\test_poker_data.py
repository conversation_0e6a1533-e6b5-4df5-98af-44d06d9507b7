#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script pour générer des données de poker réalistes pour tester l'application Conseiller Poker.
"""

import os
import time
import random
from datetime import datetime

# Chemin vers le fichier à mettre à jour
FILE_PATH = r"C:\Users\<USER>\PokerAdvisor\Détection des regions\export\realtime_results"

# Données pour la simulation
CARD_VALUES = ["As", "Roi", "Dame", "Valet", "10", "9", "8", "7", "6", "5", "4", "3", "2"]
SUITS = ["Cœur", "Pi<PERSON>", "Trèfle", "Carreau"]

# Scénarios de poker prédéfinis
POKER_SCENARIOS = [
    {
        "hand": ["As de Cœur", "As de Pique"],
        "board": ["Roi de Trèfle", "Dame de Carreau", "10 de Cœur"],
        "probability": 82.5,
        "action": "Relancer"
    },
    {
        "hand": ["Roi de Cœur", "Dame de Cœur"],
        "board": ["Valet de Cœur", "10 de Pique", "2 de Trèfle"],
        "probability": 65.3,
        "action": "Suivre"
    },
    {
        "hand": ["10 de Carreau", "10 de Trèfle"],
        "board": ["As de Pique", "Roi de Pique", "Dame de Pique"],
        "probability": 28.7,
        "action": "Se coucher"
    },
    {
        "hand": ["As de Carreau", "Roi de Carreau"],
        "board": ["Dame de Carreau", "Valet de Carreau", "9 de Pique"],
        "probability": 91.2,
        "action": "All-in"
    },
    {
        "hand": ["7 de Trèfle", "8 de Trèfle"],
        "board": ["9 de Cœur", "10 de Carreau", "Valet de Pique"],
        "probability": 78.9,
        "action": "Relancer"
    },
    {
        "hand": ["2 de Cœur", "7 de Pique"],
        "board": ["As de Trèfle", "Roi de Cœur", "Valet de Carreau"],
        "probability": 12.4,
        "action": "Se coucher"
    }
]

def generate_random_card():
    """Génère une carte aléatoire."""
    value = random.choice(CARD_VALUES)
    suit = random.choice(SUITS)
    return f"{value} de {suit}"

def generate_random_hand():
    """Génère une main aléatoire."""
    cards = []
    while len(cards) < 2:
        card = generate_random_card()
        if card not in cards:
            cards.append(card)
    return cards

def generate_random_board(num_cards=3):
    """Génère un board aléatoire avec un nombre spécifié de cartes."""
    cards = []
    while len(cards) < num_cards:
        card = generate_random_card()
        if card not in cards:
            cards.append(card)
    return cards

def update_file_with_scenario():
    """Met à jour le fichier avec un scénario prédéfini ou aléatoire."""
    # 70% du temps, utiliser un scénario prédéfini
    if random.random() < 0.7:
        scenario = random.choice(POKER_SCENARIOS)
        hand = scenario["hand"]
        board_cards = len(scenario["board"])
        board = scenario["board"]
        probability = scenario["probability"]
        action = scenario["action"]
    else:
        # Générer un scénario aléatoire
        hand = generate_random_hand()
        board_cards = random.randint(3, 5)
        board = generate_random_board(board_cards)
        probability = round(random.uniform(0, 100), 1)
        
        # Déterminer l'action en fonction de la probabilité
        if probability > 80:
            action = "All-in" if random.random() < 0.3 else "Relancer"
        elif probability > 60:
            action = "Relancer" if random.random() < 0.7 else "Suivre"
        elif probability > 40:
            action = "Suivre" if random.random() < 0.7 else "Checker"
        elif probability > 20:
            action = "Checker" if random.random() < 0.6 else "Se coucher"
        else:
            action = "Se coucher"
    
    # Formater les cartes pour l'affichage
    hand_str = ", ".join(hand)
    board_str = ", ".join(board)
    
    current_time = datetime.now().strftime("%H:%M:%S")
    
    content = f"""Cartes en main: {hand_str}
Cartes sur le board: {board_str}
Probabilité de gagner: {probability}%
Action recommandée: {action}

Dernière mise à jour: {current_time}"""
    
    with open(FILE_PATH, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Fichier mis à jour à {current_time}")
    print(f"Main: {hand_str}")
    print(f"Board: {board_str}")
    print(f"Probabilité: {probability}%")
    print(f"Action: {action}")
    print("-" * 40)

def main():
    """Fonction principale."""
    print("Démarrage de la simulation de données de poker...")
    print(f"Le fichier sera mis à jour toutes les 3 secondes à {FILE_PATH}")
    print("Appuyez sur Ctrl+C pour arrêter")
    print("=" * 40)
    
    try:
        while True:
            update_file_with_scenario()
            time.sleep(3)
    except KeyboardInterrupt:
        print("\nSimulation arrêtée")

if __name__ == "__main__":
    main()
