#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de persistance des corrections
==================================

Ce script teste que les corrections persistent bien
dans le conseiller poker.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def test_corrections_persistence():
    """Test de persistance des corrections"""
    
    print("🔍 Test de persistance des corrections")
    print("=" * 60)
    
    # Créer une instance du conseiller
    advisor = PokerAdvisorLight()
    
    print("✅ Conseiller poker initialisé")
    
    # Test 1: Vérifier l'état initial
    print("\n📝 Test 1: État initial")
    print("-" * 40)
    
    initial_corrections = advisor.get_manual_corrections()
    print(f"Corrections initiales: {initial_corrections}")
    
    # Test 2: Appliquer une correction simple
    print("\n🔧 Test 2: Application d'une correction")
    print("-" * 40)
    
    region = "card_1"
    value = "K"
    suit = "Pique"
    
    print(f"Application: {region} = '{value}' de {suit}")
    success = advisor.set_manual_correction(region, value, suit)
    print(f"Résultat: {success}")
    
    # Vérifier immédiatement
    corrections_after = advisor.get_manual_corrections()
    print(f"Corrections après application: {corrections_after}")
    
    # Test 3: Appliquer une correction "Pas de cartes"
    print("\n🔧 Test 3: Application d'une correction 'Pas de cartes'")
    print("-" * 40)
    
    region2 = "card_2"
    value2 = ""
    suit2 = ""
    
    print(f"Application: {region2} = 'Pas de cartes'")
    success2 = advisor.set_manual_correction(region2, value2, suit2)
    print(f"Résultat: {success2}")
    
    # Vérifier immédiatement
    corrections_after2 = advisor.get_manual_corrections()
    print(f"Corrections après 2ème application: {corrections_after2}")
    
    # Test 4: Vérifier la persistance avec une nouvelle instance
    print("\n🔄 Test 4: Persistance avec nouvelle instance")
    print("-" * 40)
    
    # Créer une nouvelle instance
    advisor2 = PokerAdvisorLight()
    corrections_new_instance = advisor2.get_manual_corrections()
    print(f"Corrections dans nouvelle instance: {corrections_new_instance}")
    
    # Test 5: Analyse avec corrections
    print("\n📊 Test 5: Analyse avec corrections")
    print("-" * 40)
    
    # Simuler des résultats de détection
    test_results = {
        "card_1": {"text": "Q", "colors": ["black", "white"], "confidence": 0.85},
        "card_2": {"text": "A", "colors": ["red", "white"], "confidence": 0.82},
        "carte_1m": {"text": "J", "colors": ["blue", "white"], "confidence": 0.90},
    }
    
    print("Résultats de détection simulés:")
    for region, data in test_results.items():
        print(f"  {region}: '{data['text']}' {data['colors']}")
    
    # Analyser avec l'instance qui a les corrections
    print("\nAnalyse avec corrections:")
    analysis, formatted = advisor.analyze_detection_results(test_results)
    
    print(f"Board: {analysis.get('board_cards_text', 'N/A')}")
    print(f"Main: {analysis.get('hand_cards_text', 'N/A')}")
    
    # Analyser avec l'instance sans corrections
    print("\nAnalyse sans corrections:")
    analysis2, formatted2 = advisor2.analyze_detection_results(test_results)
    
    print(f"Board: {analysis2.get('board_cards_text', 'N/A')}")
    print(f"Main: {analysis2.get('hand_cards_text', 'N/A')}")
    
    # Test 6: Effacer les corrections
    print("\n🗑️ Test 6: Effacement des corrections")
    print("-" * 40)
    
    cleared_count = advisor.clear_manual_corrections()
    print(f"Corrections effacées: {cleared_count}")
    
    final_corrections = advisor.get_manual_corrections()
    print(f"Corrections finales: {final_corrections}")
    
    print("\n✅ Test de persistance terminé!")
    print("\n📝 Résumé:")
    print("  ✅ État initial vérifié")
    print("  ✅ Application de corrections testée")
    print("  ✅ Correction 'Pas de cartes' testée")
    print("  ✅ Persistance entre instances testée")
    print("  ✅ Analyse avec corrections testée")
    print("  ✅ Effacement des corrections testé")
    
    return True

if __name__ == "__main__":
    test_corrections_persistence()
