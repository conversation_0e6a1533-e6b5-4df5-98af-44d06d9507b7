#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de débogage des corrections
===============================

Ce script teste spécifiquement pourquoi les corrections
ne s'affichent pas correctement.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def test_corrections_debug():
    """Test de débogage des corrections"""
    
    print("🔍 Test de débogage des corrections")
    print("=" * 50)
    
    # Créer une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection avec des cartes réelles
    test_results = {
        "card_1": {"text": "8", "colors": ["black", "white"], "confidence": 0.75},
        "card_2": {"text": "K", "colors": ["red", "white"], "confidence": 0.82},
        "carte_1m": {"text": "A", "colors": ["red", "white"], "confidence": 0.90},
    }
    
    print("📊 Résultats de détection simulés:")
    for region, data in test_results.items():
        print(f"  {region}: '{data['text']}' {data['colors']}")
    
    # Test 1: Analyse AVANT corrections
    print("\n🔍 Test 1: Analyse AVANT corrections")
    print("-" * 40)
    
    data_before = advisor.extract_poker_data(test_results)
    print(f"Board cards: {data_before.get('board_cards_text', 'N/A')}")
    print(f"Hand cards: {data_before.get('hand_cards_text', 'N/A')}")
    print(f"Detected regions: {data_before.get('detected_regions', [])}")
    
    # Test 2: Appliquer une correction
    print("\n🛠️ Test 2: Application d'une correction")
    print("-" * 40)
    
    print("Avant correction - corrections actives:")
    corrections = advisor.get_manual_corrections()
    print(f"  {corrections}")
    
    # Appliquer une correction
    success = advisor.set_manual_correction("card_1", "6", "Pique")
    print(f"Correction appliquée: {success}")
    
    print("Après correction - corrections actives:")
    corrections = advisor.get_manual_corrections()
    print(f"  {corrections}")
    
    # Test 3: Analyse APRÈS correction
    print("\n🔍 Test 3: Analyse APRÈS correction")
    print("-" * 40)
    
    data_after = advisor.extract_poker_data(test_results)
    print(f"Board cards: {data_after.get('board_cards_text', 'N/A')}")
    print(f"Hand cards: {data_after.get('hand_cards_text', 'N/A')}")
    print(f"Detected regions: {data_after.get('detected_regions', [])}")
    print(f"Manual corrections: {data_after.get('manual_corrections', [])}")
    
    # Test 4: Analyse complète
    print("\n📈 Test 4: Analyse complète")
    print("-" * 40)
    
    analysis, formatted = advisor.analyze_detection_results(test_results)
    print(f"Analysis board cards: {analysis.get('board_cards_text', 'N/A')}")
    print(f"Analysis hand cards: {analysis.get('hand_cards_text', 'N/A')}")
    
    print("\n📝 Formatted analysis:")
    print(formatted)
    
    return True

if __name__ == "__main__":
    test_corrections_debug()
