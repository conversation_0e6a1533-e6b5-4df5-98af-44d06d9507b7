#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script d'intégration de la logique avancée dans le conseiller poker existant.
Ce script remplace les fonctions défaillantes par la nouvelle logique.
"""

import os
import shutil
from datetime import datetime

def backup_existing_files():
    """Sauvegarde les fichiers existants avant modification"""
    print("📦 Sauvegarde des fichiers existants...")
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        "detector_gui.py",
        "../Conseiller Poker/poker_advisor_app.py"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            print(f"   ✅ {file_path} → {backup_path}")
    
    print(f"📦 Sauvegarde terminée dans: {backup_dir}")
    return backup_dir

def create_integration_wrapper():
    """Crée un wrapper pour intégrer la logique avancée"""
    wrapper_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Wrapper d'intégration pour la logique avancée de poker.
Ce module fait le pont entre l'ancienne interface et la nouvelle logique.
"""

from poker_logic_advanced import AdvancedPokerLogic

class PokerAdvisorIntegration:
    """Intégration de la logique avancée dans le conseiller poker"""
    
    def __init__(self):
        self.advanced_logic = AdvancedPokerLogic()
    
    def convert_card_format(self, card_value, card_suit):
        """Convertit le format des cartes du détecteur vers le format avancé
        
        Args:
            card_value: Valeur de la carte (A, K, Q, J, 10, 9, etc.)
            card_suit: Couleur de la carte (Cœur, Pique, Trèfle, Carreau)
            
        Returns:
            str: Carte au format "As de Cœur"
        """
        # Mapping des valeurs
        value_mapping = {
            'A': 'As', 'K': 'Roi', 'Q': 'Dame', 'J': 'Valet',
            '10': '10', '9': '9', '8': '8', '7': '7', '6': '6', 
            '5': '5', '4': '4', '3': '3', '2': '2'
        }
        
        french_value = value_mapping.get(card_value, card_value)
        return f"{french_value} de {card_suit}"
    
    def evaluate_hand_advanced(self, hand_values, hand_suits, board_values, board_suits):
        """Évalue une main avec la logique avancée
        
        Returns:
            dict: {
                'hand_rank': int,
                'hand_strength': int, 
                'hand_description': str,
                'draws': dict,
                'equity': float,
                'recommendations': dict
            }
        """
        # Convertir au format français
        hand_cards = []
        for i, value in enumerate(hand_values):
            if i < len(hand_suits):
                hand_cards.append(self.convert_card_format(value, hand_suits[i]))
        
        board_cards = []
        for i, value in enumerate(board_values):
            if i < len(board_suits):
                board_cards.append(self.convert_card_format(value, board_suits[i]))
        
        # Parser les cartes
        h_vals, h_suits, b_vals, b_suits, all_cards = self.advanced_logic.parse_cards(
            hand_cards, board_cards
        )
        
        # Évaluer la main
        hand_rank, hand_strength, hand_description = self.advanced_logic.evaluate_made_hand(all_cards)
        
        # Calculer les tirages
        draws = self.advanced_logic.calculate_draws_and_outs(h_vals, h_suits, b_vals, b_suits)
        
        # Calculer l'équité
        equity = self.advanced_logic.calculate_equity(h_vals, h_suits, b_vals, b_suits)
        
        # Générer des recommandations
        recommendations = self.generate_recommendations(
            hand_rank, hand_description, draws, equity, len(board_cards)
        )
        
        return {
            'hand_rank': hand_rank,
            'hand_strength': hand_strength,
            'hand_description': hand_description,
            'draws': draws,
            'equity': equity,
            'recommendations': recommendations
        }
    
    def generate_recommendations(self, hand_rank, hand_desc, draws, equity, board_stage):
        """Génère des recommandations d'action intelligentes"""
        recommendations = {
            'action': 'check',
            'reason': 'Action par défaut',
            'aggression_level': 'passive',
            'bet_sizing': '1/3 pot'
        }
        
        # Recommandations selon la force de la main
        if hand_rank >= 6:  # Full house ou mieux
            recommendations.update({
                'action': 'bet/raise',
                'reason': f'{hand_desc} - Main très forte, extraire de la valeur',
                'aggression_level': 'aggressive',
                'bet_sizing': '2/3 pot'
            })
        elif hand_rank >= 4:  # Quinte ou couleur
            recommendations.update({
                'action': 'bet/call',
                'reason': f'{hand_desc} - Main forte, jouer pour la valeur',
                'aggression_level': 'moderate',
                'bet_sizing': '1/2 pot'
            })
        elif hand_rank >= 2:  # Deux paires ou brelan
            recommendations.update({
                'action': 'bet/call',
                'reason': f'{hand_desc} - Main correcte, jouer prudemment',
                'aggression_level': 'moderate',
                'bet_sizing': '1/3 pot'
            })
        elif hand_rank == 1:  # Paire
            if equity > 50:
                recommendations.update({
                    'action': 'bet/call',
                    'reason': f'{hand_desc} - Paire avec bonne équité',
                    'aggression_level': 'moderate',
                    'bet_sizing': '1/3 pot'
                })
            else:
                recommendations.update({
                    'action': 'check/call',
                    'reason': f'{hand_desc} - Paire faible, jouer défensivement',
                    'aggression_level': 'passive',
                    'bet_sizing': 'check'
                })
        else:  # Hauteur
            # Vérifier les tirages
            total_outs = draws.get('total_outs', 0)
            if total_outs >= 8:  # Bon tirage
                recommendations.update({
                    'action': 'bet/call',
                    'reason': f'Hauteur avec {total_outs} outs - Bon tirage',
                    'aggression_level': 'moderate',
                    'bet_sizing': '1/3 pot'
                })
            elif total_outs >= 4:  # Tirage moyen
                recommendations.update({
                    'action': 'check/call',
                    'reason': f'Hauteur avec {total_outs} outs - Tirage moyen',
                    'aggression_level': 'passive',
                    'bet_sizing': 'check'
                })
            else:  # Pas de tirage
                if equity < 25:
                    recommendations.update({
                        'action': 'fold',
                        'reason': 'Hauteur sans tirage - Main trop faible',
                        'aggression_level': 'fold',
                        'bet_sizing': 'fold'
                    })
                else:
                    recommendations.update({
                        'action': 'check',
                        'reason': 'Hauteur sans tirage - Contrôler le pot',
                        'aggression_level': 'passive',
                        'bet_sizing': 'check'
                    })
        
        # Ajustements selon le stade de la partie
        if board_stage == 0:  # Preflop
            if equity > 70:
                recommendations['action'] = 'raise'
                recommendations['reason'] = 'Main premium preflop'
            elif equity > 50:
                recommendations['action'] = 'call/raise'
                recommendations['reason'] = 'Main jouable preflop'
            elif equity < 30:
                recommendations['action'] = 'fold'
                recommendations['reason'] = 'Main trop faible preflop'
        
        return recommendations
    
    def format_analysis_text(self, analysis_result):
        """Formate le résultat d'analyse pour l'affichage"""
        hand_desc = analysis_result['hand_description']
        draws = analysis_result['draws']
        equity = analysis_result['equity']
        recommendations = analysis_result['recommendations']
        
        # Construire la description des tirages
        draw_descriptions = []
        for draw_type, draw_info in draws.items():
            if draw_type not in ['total_outs', 'clean_outs'] and draw_info.get('possible', False):
                draw_descriptions.append(draw_info['description'])
        
        # Texte principal
        analysis_text = f"Main actuelle: {hand_desc}\\n"
        analysis_text += f"Équité estimée: {equity:.1f}%\\n"
        
        if draw_descriptions:
            analysis_text += f"Tirages: {', '.join(draw_descriptions)}\\n"
        
        analysis_text += f"\\nRecommandation: {recommendations['action'].upper()}\\n"
        analysis_text += f"Raison: {recommendations['reason']}"
        
        return analysis_text

# Instance globale pour l'intégration
poker_integration = PokerAdvisorIntegration()
'''
    
    with open("poker_advisor_integration.py", "w", encoding="utf-8") as f:
        f.write(wrapper_code)
    
    print("✅ Wrapper d'intégration créé: poker_advisor_integration.py")

def main():
    """Fonction principale d'intégration"""
    print("🚀 INTÉGRATION DE LA LOGIQUE AVANCÉE DE POKER")
    print("=" * 60)
    
    # 1. Sauvegarde
    backup_dir = backup_existing_files()
    
    # 2. Créer le wrapper d'intégration
    create_integration_wrapper()
    
    # 3. Instructions pour l'utilisateur
    print("\\n📋 ÉTAPES SUIVANTES:")
    print("=" * 40)
    print("1. ✅ Logique avancée créée (poker_logic_advanced.py)")
    print("2. ✅ Wrapper d'intégration créé (poker_advisor_integration.py)")
    print("3. ✅ Fichiers sauvegardés dans:", backup_dir)
    print("\\n🔧 MODIFICATIONS À FAIRE MANUELLEMENT:")
    print("   → Remplacer les appels aux anciennes fonctions dans detector_gui.py")
    print("   → Utiliser poker_integration.evaluate_hand_advanced() au lieu de l'ancienne logique")
    print("   → Tester avec l'application complète")
    
    print("\\n🎯 AMÉLIORATIONS APPORTÉES:")
    print("   ✅ Détection correcte des combinaisons (quinte, couleur, etc.)")
    print("   ✅ Calcul précis des outs et tirages")
    print("   ✅ Équité réaliste selon les statistiques de poker")
    print("   ✅ Recommandations intelligentes basées sur la situation")
    print("   ✅ Gestion des cartes déjà sorties")
    print("   ✅ Support complet des projections (couleur, quinte, full, etc.)")
    
    print("\\n🎉 INTÉGRATION PRÊTE!")

if __name__ == "__main__":
    main()
