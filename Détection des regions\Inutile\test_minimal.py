#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test minimal pour identifier les problèmes de l'application.
"""

import sys
import os

def test_imports():
    """Test des imports un par un"""
    print("🔍 Test des imports...")
    
    try:
        print("1. Test import sys, os...")
        import sys, os
        print("✅ sys, os OK")
        
        print("2. Test import PyQt5...")
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5.QtWidgets OK")
        
        print("3. Test import cv2...")
        import cv2
        print("✅ cv2 OK")
        
        print("4. Test import numpy...")
        import numpy as np
        print("✅ numpy OK")
        
        print("5. Test import torch...")
        import torch
        print("✅ torch OK")
        
        print("6. Test import detector...")
        from detector import Detector
        print("✅ detector OK")
        
        print("7. Test import multi_ocr_detector...")
        from multi_ocr_detector import MultiOCRDetector
        print("✅ multi_ocr_detector OK")
        
        print("8. Test import poker_advisor_light...")
        from poker_advisor_light import PokerAdvisorLight
        print("✅ poker_advisor_light OK")
        
        print("9. Test import monitor_app...")
        from monitor_app import AppMonitor
        print("✅ monitor_app OK")
        
        print("10. Test import debug_crash...")
        from debug_crash import setup_crash_monitoring
        print("✅ debug_crash OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'import: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pyqt_app():
    """Test de création d'une application PyQt5 minimale"""
    print("\n🔍 Test de l'application PyQt5...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt5.QtCore import Qt
        
        # Créer l'application
        app = QApplication(sys.argv)
        print("✅ QApplication créée")
        
        # Créer une fenêtre simple
        window = QMainWindow()
        window.setWindowTitle("Test Minimal")
        window.setGeometry(100, 100, 300, 200)
        
        label = QLabel("Test PyQt5 OK")
        label.setAlignment(Qt.AlignCenter)
        window.setCentralWidget(label)
        
        print("✅ Fenêtre créée")
        
        # Afficher la fenêtre
        window.show()
        print("✅ Fenêtre affichée")
        
        # Fermer immédiatement
        window.close()
        app.quit()
        print("✅ Application fermée proprement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur PyQt5: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detector_creation():
    """Test de création du détecteur"""
    print("\n🔍 Test de création du détecteur...")
    
    try:
        from detector import Detector
        
        # Vérifier le fichier de configuration
        config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
        print(f"📁 Chemin de configuration: {config_path}")
        
        if not os.path.exists(config_path):
            print(f"⚠️ Fichier de configuration non trouvé: {config_path}")
            return False
        
        print("✅ Fichier de configuration trouvé")
        
        # Créer le détecteur avec une région simple
        detector = Detector(config_path, ['card_1'], False)  # Sans CUDA pour le test
        print("✅ Détecteur créé avec succès")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du détecteur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TESTS MINIMAUX POUR IDENTIFIER LE PROBLÈME")
    print("=" * 60)
    
    # Test 1: Imports
    if not test_imports():
        print("\n❌ ÉCHEC: Problème d'import")
        return False
    
    # Test 2: PyQt5
    if not test_pyqt_app():
        print("\n❌ ÉCHEC: Problème PyQt5")
        return False
    
    # Test 3: Détecteur
    if not test_detector_creation():
        print("\n❌ ÉCHEC: Problème de détecteur")
        return False
    
    print("\n✅ TOUS LES TESTS SONT RÉUSSIS!")
    print("Le problème doit être ailleurs dans l'application principale.")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 ERREUR CRITIQUE: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 Tests terminés")
