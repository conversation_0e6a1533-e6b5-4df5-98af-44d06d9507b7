#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Analyseur de ranges pour poker - Range vs Range
"""

import itertools
import numpy as np
from collections import defaultdict
import json

class RangeAnalyzer:
    """Analyseur de ranges poker"""
    
    def __init__(self):
        self.rank_order = ['2', '3', '4', '5', '6', '7', '8', '9', 'T', 'J', 'Q', 'K', 'A']
        self.suits = ['h', 'd', 'c', 's']
        
        # Ranges prédéfinis par position
        self.predefined_ranges = {
            "UTG_tight": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99"],
                "suited": ["AKs", "AQs", "AJs", "KQs"],
                "offsuit": ["AKo", "AQo"]
            },
            "UTG_loose": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88", "77"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "KQs", "KJs", "QJs"],
                "offsuit": ["AKo", "AQo", "AJo", "KQo"]
            },
            "MP_tight": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "KQs", "KJs", "QJs", "JTs"],
                "offsuit": ["AKo", "AQo", "AJo", "KQo"]
            },
            "MP_loose": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88", "77", "66"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "A9s", "KQs", "KJs", "KTs", "QJs", "QTs", "JTs", "T9s"],
                "offsuit": ["AKo", "AQo", "AJo", "ATo", "KQo", "KJo", "QJo"]
            },
            "CO_tight": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88", "77"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "A9s", "KQs", "KJs", "KTs", "QJs", "QTs", "JTs", "T9s", "98s"],
                "offsuit": ["AKo", "AQo", "AJo", "ATo", "KQo", "KJo", "QJo"]
            },
            "BTN_tight": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88", "77", "66", "55"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "A9s", "A8s", "A7s", "A6s", "A5s", "A4s", "A3s", "A2s", 
                          "KQs", "KJs", "KTs", "K9s", "QJs", "QTs", "Q9s", "JTs", "J9s", "T9s", "T8s", "98s", "87s", "76s"],
                "offsuit": ["AKo", "AQo", "AJo", "ATo", "A9o", "KQo", "KJo", "KTo", "QJo", "QTo", "JTo"]
            },
            "SB_tight": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88", "77", "66"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "A9s", "A8s", "A7s", "A6s", "A5s", "KQs", "KJs", "KTs", "K9s", 
                          "QJs", "QTs", "JTs", "J9s", "T9s", "98s", "87s"],
                "offsuit": ["AKo", "AQo", "AJo", "ATo", "A9o", "KQo", "KJo", "QJo"]
            },
            "BB_defend": {
                "pairs": ["AA", "KK", "QQ", "JJ", "TT", "99", "88", "77", "66", "55", "44", "33", "22"],
                "suited": ["AKs", "AQs", "AJs", "ATs", "A9s", "A8s", "A7s", "A6s", "A5s", "A4s", "A3s", "A2s",
                          "KQs", "KJs", "KTs", "K9s", "K8s", "K7s", "K6s", "K5s", "K4s", "K3s", "K2s",
                          "QJs", "QTs", "Q9s", "Q8s", "Q7s", "Q6s", "JTs", "J9s", "J8s", "J7s", "T9s", "T8s", "T7s",
                          "98s", "97s", "87s", "86s", "76s", "65s", "54s"],
                "offsuit": ["AKo", "AQo", "AJo", "ATo", "A9o", "A8o", "A7o", "A6o", "A5o", "KQo", "KJo", "KTo", "K9o",
                           "QJo", "QTo", "Q9o", "JTo", "J9o", "T9o", "98o"]
            }
        }
    
    def analyze_range_vs_range(self, hero_range, villain_range, board=None):
        """
        Analyse l'équité d'un range contre un autre range
        
        Args:
            hero_range (str/dict): Range du héros
            villain_range (str/dict): Range du villain
            board (list): Cartes du board
            
        Returns:
            dict: Analyse complète range vs range
        """
        # Convertir les ranges en listes de mains
        hero_hands = self._parse_range(hero_range)
        villain_hands = self._parse_range(villain_range)
        
        if not hero_hands or not villain_hands:
            return {"error": "Ranges invalides"}
        
        # Calculer les équités pour chaque combinaison
        equity_matrix = []
        total_combinations = 0
        total_equity = 0
        
        hand_vs_hand_results = {}
        
        for hero_hand in hero_hands:
            for villain_hand in villain_hands:
                # Vérifier qu'il n'y a pas de cartes en commun
                if not self._hands_overlap(hero_hand, villain_hand, board):
                    equity = self._calculate_hand_vs_hand_equity(hero_hand, villain_hand, board)
                    equity_matrix.append({
                        "hero_hand": hero_hand,
                        "villain_hand": villain_hand,
                        "equity": equity
                    })
                    
                    total_combinations += 1
                    total_equity += equity
                    
                    # Stocker pour analyse détaillée
                    hero_key = "".join(hero_hand)
                    villain_key = "".join(villain_hand)
                    hand_vs_hand_results[f"{hero_key}_vs_{villain_key}"] = equity
        
        if total_combinations == 0:
            return {"error": "Aucune combinaison valide"}
        
        average_equity = total_equity / total_combinations
        
        # Analyser la distribution des équités
        equities = [result["equity"] for result in equity_matrix]
        equity_distribution = self._analyze_equity_distribution(equities)
        
        # Identifier les meilleures et pires mains
        best_hands = sorted(equity_matrix, key=lambda x: x["equity"], reverse=True)[:10]
        worst_hands = sorted(equity_matrix, key=lambda x: x["equity"])[:10]
        
        return {
            "average_equity": average_equity,
            "total_combinations": total_combinations,
            "equity_distribution": equity_distribution,
            "best_hands": best_hands,
            "worst_hands": worst_hands,
            "detailed_matrix": equity_matrix,
            "hero_range_size": len(hero_hands),
            "villain_range_size": len(villain_hands),
            "board": board or []
        }
    
    def _parse_range(self, range_def):
        """Parse une définition de range en liste de mains"""
        if isinstance(range_def, str):
            # Range prédéfini
            if range_def in self.predefined_ranges:
                return self._expand_predefined_range(self.predefined_ranges[range_def])
            else:
                # Range textuel personnalisé
                return self._parse_text_range(range_def)
        elif isinstance(range_def, dict):
            # Range défini par dictionnaire
            return self._expand_predefined_range(range_def)
        elif isinstance(range_def, list):
            # Liste de mains
            return range_def
        else:
            return []
    
    def _expand_predefined_range(self, range_dict):
        """Expanse un range prédéfini en mains spécifiques"""
        hands = []
        
        # Ajouter les paires
        if "pairs" in range_dict:
            for pair in range_dict["pairs"]:
                hands.extend(self._get_all_combinations(pair))
        
        # Ajouter les suited
        if "suited" in range_dict:
            for suited in range_dict["suited"]:
                hands.extend(self._get_all_combinations(suited))
        
        # Ajouter les offsuit
        if "offsuit" in range_dict:
            for offsuit in range_dict["offsuit"]:
                hands.extend(self._get_all_combinations(offsuit))
        
        return hands
    
    def _parse_text_range(self, text_range):
        """Parse un range textuel comme 'AA,KK,QQ,AKs,AKo'"""
        hands = []
        parts = text_range.replace(" ", "").split(",")
        
        for part in parts:
            hands.extend(self._get_all_combinations(part))
        
        return hands
    
    def _get_all_combinations(self, hand_notation):
        """Obtient toutes les combinaisons pour une notation de main"""
        hands = []
        
        if len(hand_notation) == 2 and hand_notation[0] == hand_notation[1]:
            # Paire comme "AA"
            rank = hand_notation[0]
            for suit1, suit2 in itertools.combinations(self.suits, 2):
                hands.append([rank + suit1, rank + suit2])
        
        elif len(hand_notation) == 3:
            rank1, rank2 = hand_notation[0], hand_notation[1]
            
            if hand_notation[2] == 's':
                # Suited comme "AKs"
                for suit in self.suits:
                    hands.append([rank1 + suit, rank2 + suit])
            
            elif hand_notation[2] == 'o':
                # Offsuit comme "AKo"
                for suit1 in self.suits:
                    for suit2 in self.suits:
                        if suit1 != suit2:
                            hands.append([rank1 + suit1, rank2 + suit2])
        
        return hands
    
    def _hands_overlap(self, hand1, hand2, board=None):
        """Vérifie si deux mains ont des cartes en commun"""
        all_cards = set(hand1 + hand2)
        if board:
            all_cards.update(board)
        
        return len(all_cards) != len(hand1) + len(hand2) + len(board or [])
    
    def _calculate_hand_vs_hand_equity(self, hand1, hand2, board=None):
        """Calcule l'équité d'une main contre une autre"""
        # Simplification pour l'instant - utiliser Monte Carlo plus tard
        # Retourner une équité basée sur la force relative des mains
        
        strength1 = self._estimate_hand_strength(hand1)
        strength2 = self._estimate_hand_strength(hand2)
        
        if strength1 > strength2:
            return 65 + (strength1 - strength2) * 5
        elif strength1 < strength2:
            return 35 - (strength2 - strength1) * 5
        else:
            return 50
    
    def _estimate_hand_strength(self, hand):
        """Estime la force d'une main preflop"""
        rank1, rank2 = hand[0][0], hand[1][0]
        suit1, suit2 = hand[0][1], hand[1][1]
        
        # Valeurs des rangs
        rank_values = {rank: i for i, rank in enumerate(self.rank_order)}
        val1, val2 = rank_values[rank1], rank_values[rank2]
        
        # Paire
        if rank1 == rank2:
            return 50 + val1 * 2
        
        # Suited
        elif suit1 == suit2:
            return 20 + max(val1, val2) + min(val1, val2) * 0.5
        
        # Offsuit
        else:
            return 10 + max(val1, val2) + min(val1, val2) * 0.3
    
    def _analyze_equity_distribution(self, equities):
        """Analyse la distribution des équités"""
        if not equities:
            return {}
        
        equities_array = np.array(equities)
        
        return {
            "min": float(np.min(equities_array)),
            "max": float(np.max(equities_array)),
            "mean": float(np.mean(equities_array)),
            "median": float(np.median(equities_array)),
            "std": float(np.std(equities_array)),
            "percentiles": {
                "25th": float(np.percentile(equities_array, 25)),
                "75th": float(np.percentile(equities_array, 75)),
                "90th": float(np.percentile(equities_array, 90)),
                "95th": float(np.percentile(equities_array, 95))
            }
        }
    
    def get_range_description(self, range_name):
        """Obtient la description d'un range prédéfini"""
        if range_name in self.predefined_ranges:
            range_def = self.predefined_ranges[range_name]
            total_hands = len(self._expand_predefined_range(range_def))
            
            return {
                "name": range_name,
                "total_hands": total_hands,
                "percentage": round(total_hands / 1326 * 100, 1),  # 1326 = total des mains possibles
                "pairs": range_def.get("pairs", []),
                "suited": range_def.get("suited", []),
                "offsuit": range_def.get("offsuit", [])
            }
        return None
    
    def get_all_predefined_ranges(self):
        """Retourne tous les ranges prédéfinis avec leurs descriptions"""
        ranges = {}
        for range_name in self.predefined_ranges:
            ranges[range_name] = self.get_range_description(range_name)
        return ranges

if __name__ == "__main__":
    # Test de l'analyseur de ranges
    analyzer = RangeAnalyzer()
    
    print("🧪 Test de l'analyseur Range vs Range")
    print("=" * 50)
    
    # Test simple
    hero_range = "UTG_tight"
    villain_range = "BTN_tight"
    
    result = analyzer.analyze_range_vs_range(hero_range, villain_range)
    
    if "error" not in result:
        print(f"Équité moyenne: {result['average_equity']:.1f}%")
        print(f"Combinaisons: {result['total_combinations']}")
        print(f"Range héros: {result['hero_range_size']} mains")
        print(f"Range villain: {result['villain_range_size']} mains")
        print(f"Distribution: {result['equity_distribution']['mean']:.1f}% ± {result['equity_distribution']['std']:.1f}%")
    else:
        print(f"Erreur: {result['error']}")
    
    # Afficher les ranges disponibles
    print(f"\n📊 Ranges prédéfinis disponibles:")
    ranges = analyzer.get_all_predefined_ranges()
    for name, desc in ranges.items():
        print(f"  {name}: {desc['total_hands']} mains ({desc['percentage']}%)")
