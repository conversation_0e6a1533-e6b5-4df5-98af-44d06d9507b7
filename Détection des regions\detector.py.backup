#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Module de détection
===================================

Module de détection pour l'application Poker Advisor.
Utilise PaddleOCR pour la reconnaissance de texte et OpenCV pour la détection de couleurs.

Ce module permet de :
1. Charger une configuration contenant les coordonnées des régions à analyser
2. Extraire ces régions d'une image
3. Détecter le texte et les couleurs dans chaque région
4. Générer des résultats et des images de débogage

Auteur: Augment Agent
Date: 2023
"""

import os
import json
import argparse
import cv2
import numpy as np
from paddleocr import PaddleOCR

class Detector:
    """Classe pour la détection de cartes et de couleurs dans une image

    Cette classe permet de :
    - Charger une configuration depuis un fichier JSON
    - Extraire des régions d'intérêt d'une image
    - Détecter le texte dans ces régions avec PaddleOCR
    - Détecter les couleurs dominantes dans ces régions
    - Générer des résultats et des images de débogage
    """

    def __init__(self, config_path=None, selected_regions=None, use_cuda=None):
        """Initialise le détecteur avec un fichier de configuration

        Args:
            config_path (str, optional): Chemin vers le fichier de configuration JSON.
                Si None, utilise le fichier par défaut 'config/poker_advisor_config.json'.
            selected_regions (list, optional): Liste des noms des régions à analyser.
                Si None, toutes les régions définies dans la configuration sont analysées.
            use_cuda (bool, optional): Indique si CUDA doit être utilisé pour l'accélération GPU.
                Si None, la disponibilité de CUDA est détectée automatiquement.
        """
        # Chemin par défaut de la configuration
        if config_path is None:
            config_path = os.path.join('config', 'poker_advisor_config.json')
            print(f"✅ Utilisation de la configuration par défaut: {config_path}")

        # Stocker le chemin de la configuration
        self.config_path = config_path

        # Stocker les régions sélectionnées
        self.selected_regions = selected_regions

        # Charger la configuration
        self.config = self._load_config(config_path)

        # Vérifier si CUDA est disponible
        if use_cuda is None:
            try:
                import torch
                use_cuda = torch.cuda.is_available()
                if use_cuda:
                    try:
                        print(f"✅ CUDA détecté: {torch.cuda.get_device_name(0)}")
                        print(f"✅ Mémoire CUDA disponible: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024:.2f} Go")
                        print(f"✅ Version CUDA: {torch.version.cuda}")

                        # Optimisations CUDA pour PyTorch
                        torch.backends.cudnn.benchmark = True
                        torch.backends.cudnn.deterministic = False
                        print("✅ Optimisations CUDA activées pour PyTorch")
                    except Exception as e:
                        print(f"⚠️ Erreur lors de la récupération des informations CUDA: {e}")
                        print("⚠️ CUDA peut être disponible mais mal configuré")
                        use_cuda = False
                else:
                    print("⚠️ CUDA non disponible, utilisation du CPU")
            except ImportError:
                print("⚠️ PyTorch non installé, impossible de détecter CUDA")
                use_cuda = False
            except Exception as e:
                print(f"⚠️ Erreur lors de la détection de CUDA: {e}")
                use_cuda = False
        elif use_cuda:
            # Si CUDA est explicitement demandé, vérifier qu'il est disponible
            try:
                import torch
                if torch.cuda.is_available():
                    print(f"✅ CUDA activé manuellement: {torch.cuda.get_device_name(0)}")

                    # Optimisations CUDA pour PyTorch
                    torch.backends.cudnn.benchmark = True
                    torch.backends.cudnn.deterministic = False
                    print("✅ Optimisations CUDA activées pour PyTorch")
                else:
                    print("⚠️ CUDA demandé mais non disponible, utilisation du CPU")
                    use_cuda = False
            except ImportError:
                print("⚠️ PyTorch non installé, impossible d'utiliser CUDA")
                use_cuda = False
            except Exception as e:
                print(f"⚠️ Erreur lors de l'activation de CUDA: {e}")
                use_cuda = False

        # Initialiser PaddleOCR (mode hors ligne)
        try:
            # Vérifier si PaddlePaddle est compilé avec CUDA
            try:
                import paddle
                paddle_cuda = paddle.device.is_compiled_with_cuda()
                if paddle_cuda:
                    print(f"✅ PaddlePaddle compilé avec CUDA")
                    # Forcer l'utilisation de CUDA pour PaddlePaddle
                    if use_cuda:
                        paddle.device.set_device('gpu:0')
                        print(f"✅ PaddlePaddle configuré pour utiliser le GPU")
                else:
                    print(f"⚠️ PaddlePaddle non compilé avec CUDA, utilisation du CPU")
                    use_cuda = False
            except ImportError:
                print("⚠️ PaddlePaddle non importable, impossible de configurer CUDA")
            except Exception as e:
                print(f"⚠️ Erreur lors de la configuration de PaddlePaddle: {e}")

            # Essayer d'initialiser PaddleOCR avec les paramètres optimisés pour CUDA
            if use_cuda:
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='fr',
                    use_gpu=True,
                    show_log=False,
                    enable_mkldnn=False,  # Désactiver MKLDNN car nous utilisons CUDA
                    use_mp=False,         # Désactiver le multiprocessing pour éviter les conflits avec CUDA
                    use_tensorrt=False,   # TensorRT peut être activé si installé, mais peut causer des problèmes
                    gpu_mem=2000          # Limiter l'utilisation de la mémoire GPU à 2 Go
                )
                print(f"✅ PaddleOCR initialisé avec succès (GPU: True, optimisé pour CUDA)")
            else:
                # Initialiser avec CPU
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='fr',
                    use_gpu=False,
                    show_log=False,
                    enable_mkldnn=True,   # Activer MKLDNN pour accélérer le CPU
                    use_mp=True           # Activer le multiprocessing pour le CPU
                )
                print(f"✅ PaddleOCR initialisé avec succès (CPU optimisé)")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'initialisation de PaddleOCR: {e}")
            print("⚠️ Tentative d'initialisation avec des paramètres minimaux...")
            try:
                # Essayer avec des paramètres minimaux
                self.ocr = PaddleOCR(
                    use_angle_cls=False,
                    lang='en',
                    use_gpu=False,
                    show_log=False,
                    use_mp=False,
                    enable_mkldnn=False
                )
                print("✅ PaddleOCR initialisé avec des paramètres minimaux (GPU: False)")
            except Exception as e2:
                print(f"❌ Échec de l'initialisation de PaddleOCR: {e2}")
                print("❌ La détection de texte ne sera pas disponible")

        # Définir les plages de couleurs HSV pour la détection
        # Plages ajustées pour une meilleure détection des couleurs des cartes
        self.color_ranges = {
            'red': [
                # Rouge est à cheval sur 0°, donc deux plages
                # Plage élargie pour mieux détecter les cœurs
                {'lower': np.array([0, 80, 80]), 'upper': np.array([15, 255, 255])},
                {'lower': np.array([160, 80, 80]), 'upper': np.array([179, 255, 255])}
            ],
            'green': [
                # Plage ajustée pour mieux détecter les trèfles
                {'lower': np.array([35, 30, 30]), 'upper': np.array([85, 255, 255])}
            ],
            'blue': [
                # Plage ajustée pour mieux détecter les carreaux
                {'lower': np.array([90, 80, 80]), 'upper': np.array([140, 255, 255])}
            ],
            'black': [
                # Plage ajustée pour mieux détecter les piques
                # Réduit la plage de valeur pour éviter la confusion avec le gris
                {'lower': np.array([0, 0, 0]), 'upper': np.array([180, 30, 30])}
            ],
            'white': [
                # Plage ajustée pour mieux détecter les chiffres/lettres blancs
                {'lower': np.array([0, 0, 180]), 'upper': np.array([180, 30, 255])}
            ]
        }

        # Correspondance entre couleurs et symboles de cartes
        self.color_to_suit = {
            'red': 'hearts',    # Cœur
            'green': 'clubs',   # Trèfle
            'blue': 'diamonds', # Carreau
            'black': 'spades'   # Pique
        }

        print(f"✅ Détecteur initialisé avec la configuration: {config_path}")

    def _load_config(self, config_path):
        """Charge la configuration depuis un fichier JSON

        Args:
            config_path (str): Chemin vers le fichier de configuration JSON

        Returns:
            dict: Dictionnaire contenant la configuration chargée.
                  Si le chargement échoue, retourne une configuration vide.
        """
        try:
            # Charger la configuration depuis le fichier JSON
            with open(config_path, 'r') as f:
                config = json.load(f)
                print(f"✅ Configuration chargée depuis {config_path}")
                return config
        except FileNotFoundError:
            print(f"❌ Fichier de configuration non trouvé: {config_path}")
            # Créer une configuration vide par défaut
            return {"regions": {}}
        except json.JSONDecodeError:
            print(f"❌ Format JSON invalide dans le fichier: {config_path}")
            # Créer une configuration vide par défaut
            return {"regions": {}}
        except Exception as e:
            print(f"❌ Erreur lors du chargement de la configuration: {e}")
            # Créer une configuration vide par défaut
            return {"regions": {}}

    def extract_regions(self, image):
        """Extrait les régions d'intérêt de l'image selon la configuration

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            dict: Dictionnaire contenant les régions extraites sous forme d'images.
                  Les clés sont les noms des régions, les valeurs sont les images extraites.
        """
        regions = {}

        # Utiliser 'all_regions' si disponible, sinon utiliser 'roi'
        region_config = self.config.get('all_regions', self.config.get('roi', {}))

        if not region_config:
            print("⚠️ Aucune région définie dans la configuration")
            return regions

        # Filtrer les régions si une liste de régions sélectionnées est spécifiée
        if self.selected_regions:
            filtered_config = {}
            for name in self.selected_regions:
                if name in region_config:
                    filtered_config[name] = region_config[name]
                else:
                    print(f"⚠️ Région sélectionnée non trouvée dans la configuration: {name}")
            region_config = filtered_config

        for name, coords in region_config.items():
            try:
                # Vérifier si les coordonnées utilisent le format 'x,y,width,height' ou 'left,top,width,height'
                if 'x' in coords and 'y' in coords:
                    x, y = coords['x'], coords['y']
                elif 'left' in coords and 'top' in coords:
                    x, y = coords['left'], coords['top']
                else:
                    print(f"⚠️ Format de coordonnées non reconnu pour la région {name}")
                    continue

                # Récupérer la largeur et la hauteur
                width = coords.get('width', 0)
                height = coords.get('height', 0)

                # Vérifier que les dimensions sont valides
                if width <= 0 or height <= 0:
                    print(f"⚠️ Dimensions invalides pour la région {name}: {width}x{height}")
                    continue

                # Vérifier que les coordonnées sont dans l'image
                if x < 0 or y < 0 or x + width > image.shape[1] or y + height > image.shape[0]:
                    print(f"⚠️ Coordonnées hors limites pour la région {name}: ({x}, {y}, {width}, {height})")
                    continue

                # Extraire la région
                region = image[y:y+height, x:x+width]
                regions[name] = region

            except Exception as e:
                print(f"❌ Erreur lors de l'extraction de la région {name}: {e}")

        return regions

    def preprocess_image_for_ocr(self, image):
        """Prétraite l'image pour améliorer la détection OCR, en particulier pour les cartes J et Q

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à prétraiter

        Returns:
            tuple: Quatre images prétraitées différemment pour une meilleure détection
        """
        try:
            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie au prétraitement")
                return image, image, image, image

            # Convertir en niveaux de gris
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Appliquer un flou gaussien pour réduire le bruit
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # Améliorer le contraste de l'image
            # Cela aide particulièrement pour les lettres comme J et Q sur fond sombre
            clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8, 8))  # Augmenté à 2.5 pour plus de contraste
            enhanced = clahe.apply(blurred)

            # Appliquer une binarisation adaptative pour améliorer le contraste
            binary = cv2.adaptiveThreshold(
                enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )

            # Appliquer une dilatation pour renforcer les traits des lettres (important pour J et Q)
            kernel = np.ones((2, 2), np.uint8)
            dilated = cv2.dilate(binary, kernel, iterations=2)  # 2 itérations pour renforcer les traits

            # Appliquer une érosion légère pour éviter que les lettres ne se fondent
            eroded = cv2.erode(dilated, np.ones((1, 1), np.uint8), iterations=1)

            # Convertir en BGR pour être compatible avec PaddleOCR
            processed = cv2.cvtColor(eroded, cv2.COLOR_GRAY2BGR)

            # Créer une version alternative avec plus de contraste pour les fonds sombres
            # Utiliser une méthode différente pour la binarisation
            _, binary_inv = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # Appliquer une dilatation plus forte pour les lettres comme Q qui ont des formes arrondies
            kernel_q = np.ones((3, 3), np.uint8)  # Kernel plus grand pour Q
            dilated_inv = cv2.dilate(binary_inv, kernel_q, iterations=2)

            # Appliquer une érosion légère pour préserver la forme du Q
            eroded_inv = cv2.erode(dilated_inv, np.ones((2, 2), np.uint8), iterations=1)

            processed_inv = cv2.cvtColor(eroded_inv, cv2.COLOR_GRAY2BGR)

            # Créer une troisième version spécifiquement pour Q
            # Utiliser une méthode de binarisation différente
            _, binary_q = cv2.threshold(enhanced, 127, 255, cv2.THRESH_BINARY)

            # Appliquer des opérations morphologiques pour mieux détecter les formes arrondies
            kernel_circle = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            closed_q = cv2.morphologyEx(binary_q, cv2.MORPH_CLOSE, kernel_circle, iterations=1)

            # Convertir en BGR
            processed_q = cv2.cvtColor(closed_q, cv2.COLOR_GRAY2BGR)

            # Créer une quatrième version spécifiquement pour J
            # Utiliser une méthode de binarisation différente avec un seuil plus bas
            # pour mieux détecter les traits fins du J
            _, binary_j = cv2.threshold(enhanced, 100, 255, cv2.THRESH_BINARY)

            # Appliquer une dilatation verticale pour renforcer le trait vertical du J
            kernel_j_vertical = np.ones((3, 1), np.uint8)
            dilated_j_vertical = cv2.dilate(binary_j, kernel_j_vertical, iterations=2)  # Augmenté à 2 itérations

            # Appliquer une dilatation horizontale pour renforcer le crochet du J
            kernel_j_horizontal = np.ones((1, 3), np.uint8)
            dilated_j = cv2.dilate(dilated_j_vertical, kernel_j_horizontal, iterations=2)  # Augmenté à 2 itérations

            # Appliquer une fermeture pour connecter les traits du J
            kernel_j_close = np.ones((3, 3), np.uint8)  # Augmenté à 3x3
            closed_j = cv2.morphologyEx(dilated_j, cv2.MORPH_CLOSE, kernel_j_close, iterations=2)  # Augmenté à 2 itérations

            # Créer une version alternative avec un traitement encore plus agressif pour le J
            # Cette méthode a bien fonctionné dans nos tests
            _, binary_j2 = cv2.threshold(enhanced, 80, 255, cv2.THRESH_BINARY)  # Seuil plus bas
            kernel_j2 = np.ones((2, 2), np.uint8)
            dilated_j2 = cv2.dilate(binary_j2, kernel_j2, iterations=3)  # Dilatation plus forte

            # Convertir en BGR
            processed_j = cv2.cvtColor(closed_j, cv2.COLOR_GRAY2BGR)
            processed_j2 = cv2.cvtColor(dilated_j2, cv2.COLOR_GRAY2BGR)

            # Retourner les cinq versions prétraitées (ajout de processed_j2)
            return processed, processed_inv, processed_q, processed_j, processed_j2
        except Exception as e:
            print(f"❌ Erreur lors du prétraitement de l'image: {e}")
            return image, image, image, image, image

    def detect_text(self, image):
        """Détecte le texte dans une image avec PaddleOCR

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            str: Texte détecté dans l'image, ou chaîne vide si aucun texte n'est détecté
                 ou si une erreur se produit
        """
        try:
            # Vérifier si l'OCR est disponible
            if not hasattr(self, 'ocr'):
                print("⚠️ PaddleOCR n'est pas disponible, impossible de détecter le texte")
                return ""

            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie à detect_text")
                return ""

            # Prétraiter l'image pour améliorer la détection OCR
            # Obtenir cinq versions prétraitées différemment (ajout de processed_j2)
            processed_image, processed_inv, processed_q, processed_j, processed_j2 = self.preprocess_image_for_ocr(image)

            # Exécuter la détection de texte sur l'image originale
            result_original = self.ocr.ocr(image, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée standard
            result_processed = self.ocr.ocr(processed_image, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée inversée (meilleure pour les fonds sombres)
            result_processed_inv = self.ocr.ocr(processed_inv, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée spéciale pour Q
            result_processed_q = self.ocr.ocr(processed_q, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée spéciale pour J
            result_processed_j = self.ocr.ocr(processed_j, cls=True)

            # Exécuter la détection de texte sur la version alternative pour J
            result_processed_j2 = self.ocr.ocr(processed_j2, cls=True)

            # Extraire le texte des résultats (image originale)
            text_original = ""
            if result_original and len(result_original) > 0 and result_original[0]:
                for line in result_original[0]:
                    text_original += line[1][0] + " "
                text_original = text_original.strip()

            # Extraire le texte des résultats (image prétraitée standard)
            text_processed = ""
            if result_processed and len(result_processed) > 0 and result_processed[0]:
                for line in result_processed[0]:
                    text_processed += line[1][0] + " "
                text_processed = text_processed.strip()

            # Extraire le texte des résultats (image prétraitée inversée)
            text_processed_inv = ""
            if result_processed_inv and len(result_processed_inv) > 0 and result_processed_inv[0]:
                for line in result_processed_inv[0]:
                    text_processed_inv += line[1][0] + " "
                text_processed_inv = text_processed_inv.strip()

            # Extraire le texte des résultats (image prétraitée spéciale pour Q)
            text_processed_q = ""
            if result_processed_q and len(result_processed_q) > 0 and result_processed_q[0]:
                for line in result_processed_q[0]:
                    text_processed_q += line[1][0] + " "
                text_processed_q = text_processed_q.strip()

            # Extraire le texte des résultats (image prétraitée spéciale pour J)
            text_processed_j = ""
            if result_processed_j and len(result_processed_j) > 0 and result_processed_j[0]:
                for line in result_processed_j[0]:
                    text_processed_j += line[1][0] + " "
                text_processed_j = text_processed_j.strip()

            # Extraire le texte des résultats (version alternative pour J)
            text_processed_j2 = ""
            if result_processed_j2 and len(result_processed_j2) > 0 and result_processed_j2[0]:
                for line in result_processed_j2[0]:
                    text_processed_j2 += line[1][0] + " "
                text_processed_j2 = text_processed_j2.strip()

            # Vérifier spécifiquement pour la lettre J dans les versions spéciales pour J
            if 'J' in text_processed_j:
                print("Détection: J trouvé dans l'image prétraitée spéciale pour J")
                return text_processed_j

            if 'J' in text_processed_j2:
                print("Détection: J trouvé dans la version alternative pour J")
                return text_processed_j2

            # Vérifier spécifiquement pour la lettre Q dans la version spéciale pour Q
            if 'Q' in text_processed_q:
                print("Détection: Q trouvé dans l'image prétraitée spéciale pour Q")
                return text_processed_q

            # Sinon, choisir le meilleur résultat parmi les versions principales
            text = self.select_best_text_result(text_processed, text_original, text_processed_inv, text_processed_j, text_processed_j2)

            return text
        except Exception as e:
            print(f"❌ Erreur lors de la détection de texte: {e}")
            return ""

    def select_best_text_result(self, text_processed, text_original, text_processed_inv=None, text_processed_j=None, text_processed_j2=None):
        """Sélectionne le meilleur résultat de texte parmi les différentes versions prétraitées

        Args:
            text_processed (str): Texte détecté sur l'image prétraitée standard
            text_original (str): Texte détecté sur l'image originale
            text_processed_inv (str, optional): Texte détecté sur l'image prétraitée inversée
            text_processed_j (str, optional): Texte détecté sur l'image prétraitée spéciale pour J
            text_processed_j2 (str, optional): Texte détecté sur la version alternative pour J

        Returns:
            str: Le meilleur texte détecté
        """
        # Liste des valeurs de cartes valides
        card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']

        # Vérifier si chaque texte contient une valeur de carte valide
        contains_valid_processed = any(value in text_processed for value in card_values)
        contains_valid_original = any(value in text_original for value in card_values)
        contains_valid_inv = False
        contains_valid_j = False
        contains_valid_j2 = False

        if text_processed_inv:
            contains_valid_inv = any(value in text_processed_inv for value in card_values)

        if text_processed_j:
            contains_valid_j = any(value in text_processed_j for value in card_values)

        if text_processed_j2:
            contains_valid_j2 = any(value in text_processed_j2 for value in card_values)

        # Vérifier spécifiquement pour les lettres J et Q qui posent problème
        contains_j_processed = 'J' in text_processed
        contains_j_original = 'J' in text_original
        contains_j_inv = text_processed_inv and 'J' in text_processed_inv
        contains_j_special = text_processed_j and 'J' in text_processed_j
        contains_j_special2 = text_processed_j2 and 'J' in text_processed_j2

        contains_q_processed = 'Q' in text_processed
        contains_q_original = 'Q' in text_original
        contains_q_inv = text_processed_inv and 'Q' in text_processed_inv

        # Si l'une des versions contient spécifiquement un J, la privilégier
        # Priorité donnée aux versions spéciales pour J
        if contains_j_special2:
            print("Détection: J trouvé dans la version alternative pour J")
            return text_processed_j2
        elif contains_j_special:
            print("Détection: J trouvé dans l'image prétraitée spéciale pour J")
            return text_processed_j
        elif contains_j_processed:
            print("Détection: J trouvé dans l'image prétraitée standard")
            return text_processed
        elif contains_j_inv:
            print("Détection: J trouvé dans l'image prétraitée inversée")
            return text_processed_inv
        elif contains_j_original:
            print("Détection: J trouvé dans l'image originale")
            return text_original

        # Si l'une des versions contient spécifiquement un Q, la privilégier
        if contains_q_processed:
            print("Détection: Q trouvé dans l'image prétraitée standard")
            return text_processed
        elif contains_q_inv:
            print("Détection: Q trouvé dans l'image prétraitée inversée")
            return text_processed_inv
        elif contains_q_original:
            print("Détection: Q trouvé dans l'image originale")
            return text_original

        # Si l'une des versions contient une valeur de carte valide, la privilégier
        if contains_valid_j2:
            print("Détection: Valeur de carte trouvée dans la version alternative pour J")
            return text_processed_j2
        elif contains_valid_j:
            print("Détection: Valeur de carte trouvée dans l'image prétraitée spéciale pour J")
            return text_processed_j
        elif contains_valid_inv:
            print("Détection: Valeur de carte trouvée dans l'image prétraitée inversée")
            return text_processed_inv
        elif contains_valid_processed:
            print("Détection: Valeur de carte trouvée dans l'image prétraitée standard")
            return text_processed
        elif contains_valid_original:
            print("Détection: Valeur de carte trouvée dans l'image originale")
            return text_original

        # Si aucune ne contient de valeur valide, prendre la plus longue
        # car elle contient probablement plus d'informations
        texts = [text_processed, text_original]
        if text_processed_inv:
            texts.append(text_processed_inv)
        if text_processed_j:
            texts.append(text_processed_j)
        if text_processed_j2:
            texts.append(text_processed_j2)

        longest_text = max(texts, key=len)
        return longest_text

    def detect_colors(self, image):
        """Détecte les couleurs dominantes dans une image

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            list: Liste des noms des couleurs dominantes détectées dans l'image
        """
        try:
            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie à detect_colors")
                return []

            # Convertir l'image en HSV pour une meilleure détection des couleurs
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Créer une version améliorée de l'image pour la détection des couleurs
            # Augmenter la saturation pour rendre les couleurs plus vives
            hsv_enhanced = hsv.copy()
            hsv_enhanced[:,:,1] = np.clip(hsv_enhanced[:,:,1] * 1.3, 0, 255).astype(np.uint8)  # Augmenter la saturation

            # Détecter chaque couleur définie dans color_ranges
            detected_colors = []
            color_percentages = {}
            color_masks = {}

            for color_name, ranges in self.color_ranges.items():
                # Créer un masque combiné pour toutes les plages de cette couleur
                combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)

                for range_dict in ranges:
                    # Utiliser l'image HSV améliorée pour les couleurs (sauf pour le blanc et le noir)
                    if color_name in ['white', 'black']:
                        mask = cv2.inRange(hsv, range_dict['lower'], range_dict['upper'])
                    else:
                        mask = cv2.inRange(hsv_enhanced, range_dict['lower'], range_dict['upper'])

                    combined_mask = cv2.bitwise_or(combined_mask, mask)

                # Appliquer une opération morphologique pour éliminer le bruit
                if color_name in ['red', 'green', 'blue']:
                    # Pour les couleurs, appliquer une ouverture pour éliminer les petits points
                    kernel = np.ones((3, 3), np.uint8)
                    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
                    # Puis une dilatation pour renforcer les zones de couleur
                    combined_mask = cv2.dilate(combined_mask, kernel, iterations=1)

                # Calculer le pourcentage de pixels de cette couleur
                color_pixels = cv2.countNonZero(combined_mask)
                total_pixels = image.shape[0] * image.shape[1]
                percentage = (color_pixels / total_pixels) * 100

                # Stocker le pourcentage et le masque pour cette couleur
                color_percentages[color_name] = percentage
                color_masks[color_name] = combined_mask

                # Seuils adaptés pour chaque couleur
                threshold = 5  # Seuil par défaut

                # Ajuster les seuils en fonction de la couleur
                if color_name == 'red':
                    threshold = 4  # Seuil plus bas pour le rouge (cœur)
                elif color_name == 'blue':
                    threshold = 4  # Seuil plus bas pour le bleu (carreau)
                elif color_name == 'white':
                    threshold = 3  # Seuil plus bas pour le blanc (chiffres/lettres)

                # Si le pourcentage dépasse le seuil, considérer que la couleur est présente
                if percentage > threshold:
                    detected_colors.append(color_name)

            # Logique améliorée pour résoudre les conflits de couleurs

            # 1. Traitement spécial pour le blanc (chiffres/lettres) et les couleurs
            if 'white' in detected_colors:
                # Le blanc est presque toujours présent pour les chiffres/lettres
                # Si une autre couleur est détectée avec un pourcentage significatif, c'est probablement la couleur de la carte

                # Vérifier si une couleur dominante est présente (autre que blanc et noir)
                color_candidates = [c for c in ['red', 'green', 'blue'] if c in detected_colors]

                if len(color_candidates) == 1:
                    # Une seule couleur dominante détectée avec le blanc, c'est probablement la bonne
                    dominant_color = color_candidates[0]
                    print(f"Détection: {dominant_color} avec blanc (probablement un {self.color_to_suit[dominant_color]} avec chiffres/lettres)")

                elif len(color_candidates) > 1:
                    # Plusieurs couleurs détectées, garder la plus dominante
                    max_color = max(color_candidates, key=lambda c: color_percentages[c])

                    # Supprimer les autres couleurs
                    for color in color_candidates:
                        if color != max_color:
                            if color in detected_colors:
                                detected_colors.remove(color)
                                print(f"Détection: Suppression de {color} car {max_color} est plus dominant")

            # 2. Cas spécial pour le bleu (carreau) qui peut être confondu avec d'autres couleurs
            if 'blue' in detected_colors and 'white' in detected_colors:
                # Si le blanc est beaucoup plus dominant que le bleu, vérifier si c'est vraiment un carreau
                if color_percentages['white'] > 4 * color_percentages['blue']:
                    # Le blanc est très dominant, le bleu pourrait être une erreur
                    # Vérifier la distribution spatiale du bleu

                    # Si le bleu est concentré au centre (où se trouvent les symboles), c'est probablement un carreau
                    # Sinon, c'est probablement une erreur

                    # Simplification: si le pourcentage de bleu est très faible, le supprimer
                    if color_percentages['blue'] < 3:
                        detected_colors.remove('blue')
                        print("Détection: Bleu supprimé car pourcentage trop faible avec blanc dominant")

            # 3. Cas où aucune couleur n'est détectée mais du blanc est présent
            # C'est probablement une carte noire (pique) avec des chiffres/lettres blancs
            if len(detected_colors) == 1 and 'white' in detected_colors:
                # Ajouter le noir (pique) par défaut si le pourcentage de noir est significatif
                if 'black' not in color_percentages or color_percentages['black'] > 2:
                    detected_colors.append('black')
                    print("Détection: Noir ajouté par défaut avec blanc (probablement un pique avec chiffres/lettres)")

            # 4. Cas où aucune couleur n'est détectée du tout
            if not detected_colors:
                # Par défaut, considérer comme un pique (noir)
                detected_colors.append('black')
                print("Détection: Aucune couleur détectée, noir (pique) ajouté par défaut")

            # Afficher les pourcentages pour le débogage
            print(f"Pourcentages de couleurs détectés: {color_percentages}")

            return detected_colors
        except Exception as e:
            print(f"❌ Erreur lors de la détection de couleurs: {e}")
            return []

    def correct_card_value(self, text):
        """Corrige les confusions courantes dans la détection des valeurs de cartes

        Args:
            text (str): Texte détecté par l'OCR

        Returns:
            str: Texte corrigé
        """
        # Nettoyer le texte
        text = text.strip().upper()

        # Dictionnaire des corrections courantes
        corrections = {
            # Confusions entre K et A
            'K': ['A', 'R'],  # K peut être confondu avec A ou R (Roi)
            'A': ['K', 'R', '4'],  # A peut être confondu avec K, R ou 4

            # Confusions entre 8 et 6
            '8': ['6', 'B'],  # 8 peut être confondu avec 6 ou B
            '6': ['8', 'G'],  # 6 peut être confondu avec 8 ou G

            # Confusions entre J et 10
            # Étendu pour mieux détecter J sur différents fonds de couleur
            'J': ['1', '10', 'I', 'T', 'L', 'J.', '.J', 'j', 'i', 'l', '!', '|', '7', 'F', 'f', 'r', 'Y', 'y', 'v', 'V', 'U', 'u', 'H', 'h', 'n', 'N', 'M', 'm', 'W', 'w', 'JI', 'IJ', 'LI', 'IL', 'TI', 'IT', '1I', 'I1', 'JL', 'LJ', 'JT', 'TJ', 'J1', '1J', 'J7', '7J', 'JF', 'FJ', 'P', 'p', 'R', 'r', 'B', 'b', 'E', 'e', 'C', 'c', 'G', 'g', 'S', 's', 'Z', 'z', 'X', 'x', 'D', 'd', 'O', 'o', '0', 'Q', 'q'],
            '10': ['J', 'IO', 'LO', 'TO', '1O', 'l0', 'i0', 'lo', 'io', 'I0', 'L0'],  # 10 peut être confondu avec J, IO, LO ou TO

            # Confusions entre 9 et 5
            '9': ['5', 'S', 'G'],  # 9 peut être confondu avec 5, S ou G
            '5': ['9', 'S'],  # 5 peut être confondu avec 9 ou S

            # Confusions entre 7 et 1
            '7': ['1', 'T', 'I', 'L'],  # 7 peut être confondu avec 1, T, I ou L

            # Confusions avec Q
            'Q': ['O', '0', 'D', 'o', 'd', 'q'],  # Q peut être confondu avec O, 0 ou D (Dame)
        }

        # Liste des valeurs de cartes valides
        valid_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']

        # Vérifier si le texte contient déjà une valeur de carte valide
        for value in valid_values:
            if value in text:
                return value  # Retourner la valeur valide trouvée

        # Si aucune valeur valide n'est trouvée, essayer de corriger
        for value, confusions in corrections.items():
            for confusion in confusions:
                if confusion in text:
                    print(f"Correction de carte: '{confusion}' corrigé en '{value}'")
                    return value

        # Si aucune correction n'est possible, retourner le texte original
        return text

    def process_image(self, image_path):
        """Traite une image pour détecter le texte et les couleurs dans chaque région

        Args:
            image_path (str): Chemin vers l'image à analyser

        Returns:
            dict: Dictionnaire contenant les résultats de la détection pour chaque région.
                  Format: {nom_region: {"text": texte_détecté, "colors": couleurs_détectées}}
        """
        try:
            # Charger l'image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Impossible de charger l'image: {image_path}")

            # Extraire les régions définies dans la configuration
            regions = self.extract_regions(image)

            if not regions:
                print("⚠️ Aucune région extraite de l'image")
                return {}

            # Analyser chaque région extraite
            results = {}
            for name, region_img in regions.items():
                print(f"Analyse de la région: {name}")

                # Détecter le texte dans la région
                text = self.detect_text(region_img)

                # Détecter les couleurs dominantes dans la région
                colors = self.detect_colors(region_img)

                # Traitement spécial pour les régions de cartes
                if name.startswith('card_') or name.startswith('hand_card_') or name.startswith('carte_'):
                    # Si c'est une région de carte, appliquer un traitement spécial

                    # 1. Corriger les confusions courantes dans la détection des valeurs
                    corrected_text = self.correct_card_value(text)

                    # Si le texte a été corrigé, l'utiliser
                    if corrected_text != text:
                        print(f"Texte corrigé pour la région {name}: '{text}' -> '{corrected_text}'")
                        text = corrected_text

                    # 2. Vérifier si le texte détecté contient des chiffres ou des lettres de cartes
                    card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
                    detected_value = False

                    for value in card_values:
                        if value in text:
                            detected_value = True
                            break

                    # 3. Si un chiffre/lettre de carte est détecté mais que le blanc n'est pas dans les couleurs,
                    # ajouter le blanc aux couleurs détectées
                    if detected_value and 'white' not in colors:
                        print(f"Ajout de la couleur 'white' pour la région {name} car un chiffre/lettre de carte a été détecté")
                        colors.append('white')

                    # 4. Si aucune valeur n'est détectée mais qu'il y a du texte, vérifier s'il pourrait s'agir d'une carte
                    if not detected_value and text:
                        # Essayer de détecter des motifs qui pourraient être des cartes
                        if any(c in text for c in ['A', 'K', 'Q', 'J', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0']):
                            print(f"Texte potentiellement une carte pour la région {name}: '{text}'")
                            # Ne pas modifier le texte ici, juste un avertissement

                    # 5. Si aucune couleur n'est détectée mais qu'une valeur de carte est détectée,
                    # ajouter une couleur par défaut (noir/pique)
                    if detected_value and not colors:
                        print(f"Ajout de la couleur 'black' par défaut pour la région {name} car une valeur de carte a été détectée sans couleur")
                        colors.append('black')

                    # 6. Traitement spécial pour les lettres J et Q qui posent problème
                    if text == 'J' or text == 'Q':
                        print(f"Détection de {text} dans la région {name}")

                        # Si c'est un J, appliquer un traitement spécial supplémentaire
                        if text == 'J':
                            # Appliquer les méthodes qui ont fonctionné le mieux dans nos tests
                            # Méthodes 5, 6, 7, 9 pour le J de cœur
                            # Méthodes 7, 8, 9 et Spécial J, Spécial J2 pour le J de pique

                            # Prétraiter l'image avec les méthodes spéciales pour J
                            _, _, _, processed_j, processed_j2 = self.preprocess_image_for_ocr(region_img)

                            # Créer des méthodes supplémentaires qui ont bien fonctionné dans les tests
                            # Méthode 5 - Binarisation avec seuil bas
                            gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)
                            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
                            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4, 4))  # Plus de contraste
                            enhanced = clahe.apply(blurred)
                            _, binary = cv2.threshold(enhanced, 80, 255, cv2.THRESH_BINARY)  # Seuil plus bas

                            # Méthode 7 - Dilatation forte
                            kernel = np.ones((2, 2), np.uint8)
                            dilated = cv2.dilate(binary, kernel, iterations=3)  # Dilatation plus forte

                            # Convertir en BGR
                            method5 = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
                            method7 = cv2.cvtColor(dilated, cv2.COLOR_GRAY2BGR)

                            # Tester toutes les méthodes
                            j_detected = False
                            for img, method_name in [(processed_j, "Spécial J"), (processed_j2, "Spécial J2"),
                                                    (method5, "Méthode 5"), (method7, "Méthode 7")]:
                                result = self.ocr.ocr(img, cls=True)
                                if result and len(result) > 0 and result[0]:
                                    text_detected = ""
                                    for line in result[0]:
                                        text_detected += line[1][0] + " "
                                    text_detected = text_detected.strip()

                                    if 'J' in text_detected:
                                        print(f"J détecté avec {method_name} dans la région {name}")
                                        text = 'J'  # Confirmer que c'est bien un J
                                        j_detected = True
                                        break

                            if j_detected:
                                print(f"✅ J confirmé dans la région {name}")
                            else:
                                print(f"⚠️ J non confirmé par les méthodes supplémentaires dans la région {name}")

                        # Si c'est un J ou Q et qu'aucune couleur n'est détectée ou seulement le blanc
                        if not colors or (len(colors) == 1 and 'white' in colors):
                            # Examiner l'image pour déterminer la couleur
                            hsv = cv2.cvtColor(region_img, cv2.COLOR_BGR2HSV)

                            # Trouver la teinte dominante (en excluant les zones de faible saturation)
                            mask = cv2.inRange(hsv, (0, 50, 50), (180, 255, 255))  # Masque pour les pixels colorés
                            if cv2.countNonZero(mask) > 0:  # S'il y a des pixels colorés
                                hist_masked = cv2.calcHist([hsv], [0], mask, [180], [0, 180])
                                max_hue = np.argmax(hist_masked)

                                # Déterminer la couleur en fonction de la teinte dominante
                                if (0 <= max_hue <= 15) or (160 <= max_hue <= 179):
                                    print(f"Ajout de la couleur 'red' pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'red' not in colors:
                                        colors.append('red')
                                elif 35 <= max_hue <= 85:
                                    print(f"Ajout de la couleur 'green' pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'green' not in colors:
                                        colors.append('green')
                                elif 90 <= max_hue <= 140:
                                    print(f"Ajout de la couleur 'blue' pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'blue' not in colors:
                                        colors.append('blue')
                                else:
                                    print(f"Ajout de la couleur 'black' par défaut pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'black' not in colors:
                                        colors.append('black')
                            else:
                                # Si pas de pixels colorés, considérer comme noir
                                print(f"Ajout de la couleur 'black' par défaut pour le {text} dans la région {name} (pas de pixels colorés)")
                                if 'black' not in colors:
                                    colors.append('black')

                        # Traitement spécial pour J
                        if text == 'J':
                            # Analyse plus approfondie pour J qui est difficile à détecter
                            # Vérifier la distribution des couleurs dans différentes parties de l'image
                            height, width = region_img.shape[:2]

                            # Amélioration de la détection des couleurs pour le J
                            # Créer une version avec saturation augmentée pour mieux détecter les couleurs
                            hsv_enhanced = hsv.copy()
                            hsv_enhanced[:,:,1] = np.clip(hsv_enhanced[:,:,1] * 1.5, 0, 255).astype(np.uint8)  # Augmenter davantage la saturation

                            # Vérifier spécifiquement les couleurs difficiles à détecter pour le J
                            # Plages de couleurs ajustées spécifiquement pour le J
                            j_color_ranges = {
                                'red': [
                                    {'lower': np.array([0, 60, 60]), 'upper': np.array([20, 255, 255])},
                                    {'lower': np.array([160, 60, 60]), 'upper': np.array([179, 255, 255])}
                                ],
                                'green': [
                                    {'lower': np.array([30, 20, 20]), 'upper': np.array([90, 255, 255])}
                                ],
                                'blue': [
                                    {'lower': np.array([85, 60, 60]), 'upper': np.array([145, 255, 255])}
                                ]
                            }

                            # Détecter chaque couleur avec les plages ajustées
                            j_color_percentages = {}
                            for color_name, ranges in j_color_ranges.items():
                                combined_mask = np.zeros(hsv_enhanced.shape[:2], dtype=np.uint8)
                                for range_dict in ranges:
                                    mask = cv2.inRange(hsv_enhanced, range_dict['lower'], range_dict['upper'])
                                    combined_mask = cv2.bitwise_or(combined_mask, mask)

                                # Calculer le pourcentage de pixels de cette couleur
                                color_pixels = cv2.countNonZero(combined_mask)
                                total_pixels = hsv_enhanced.shape[0] * hsv_enhanced.shape[1]
                                percentage = (color_pixels / total_pixels) * 100
                                j_color_percentages[color_name] = percentage

                            print(f"Pourcentages de couleurs spécifiques pour J: {j_color_percentages}")

                            # Si une couleur dépasse un seuil plus bas (3% au lieu de 5%)
                            for color, percentage in j_color_percentages.items():
                                if percentage > 3 and color not in colors:
                                    print(f"Ajout de la couleur '{color}' pour le J avec détection améliorée ({percentage:.1f}%)")
                                    colors.append(color)

                            # Diviser l'image en 4 quadrants pour analyser les couleurs
                            top_left = region_img[0:height//2, 0:width//2]
                            top_right = region_img[0:height//2, width//2:width]
                            bottom_left = region_img[height//2:height, 0:width//2]
                            bottom_right = region_img[height//2:height, width//2:width]

                            # Analyser chaque quadrant pour détecter les couleurs dominantes
                            quadrants = [top_left, top_right, bottom_left, bottom_right]
                            quadrant_names = ["haut gauche", "haut droite", "bas gauche", "bas droite"]
                            quadrant_colors = []

                            for i, quad in enumerate(quadrants):
                                hsv_quad = cv2.cvtColor(quad, cv2.COLOR_BGR2HSV)

                                # Détecter les couleurs dans ce quadrant
                                red_mask1 = cv2.inRange(hsv_quad, (0, 80, 80), (15, 255, 255))
                                red_mask2 = cv2.inRange(hsv_quad, (160, 80, 80), (179, 255, 255))
                                red_mask = cv2.bitwise_or(red_mask1, red_mask2)

                                green_mask = cv2.inRange(hsv_quad, (35, 30, 30), (85, 255, 255))
                                blue_mask = cv2.inRange(hsv_quad, (90, 80, 80), (140, 255, 255))

                                # Calculer les pourcentages de pixels pour chaque couleur
                                total_pixels = quad.shape[0] * quad.shape[1]
                                red_percent = (cv2.countNonZero(red_mask) / total_pixels) * 100
                                green_percent = (cv2.countNonZero(green_mask) / total_pixels) * 100
                                blue_percent = (cv2.countNonZero(blue_mask) / total_pixels) * 100

                                # Déterminer la couleur dominante dans ce quadrant
                                color_percents = {"red": red_percent, "green": green_percent, "blue": blue_percent}
                                dominant_color = max(color_percents, key=color_percents.get)

                                # Si le pourcentage est significatif, considérer cette couleur
                                if color_percents[dominant_color] > 5:
                                    quadrant_colors.append(dominant_color)
                                    print(f"Quadrant {quadrant_names[i]} du J: couleur dominante {dominant_color} ({color_percents[dominant_color]:.1f}%)")
                                else:
                                    quadrant_colors.append("black")  # Par défaut si aucune couleur n'est dominante

                            # Déterminer la couleur globale en fonction des quadrants
                            if quadrant_colors:
                                # Compter les occurrences de chaque couleur
                                from collections import Counter
                                color_counts = Counter(quadrant_colors)

                                # Prendre la couleur la plus fréquente
                                most_common_color = color_counts.most_common(1)[0][0]

                                # Si la couleur la plus fréquente n'est pas déjà dans les couleurs détectées
                                if most_common_color not in colors and most_common_color != "black":
                                    print(f"Ajout de la couleur '{most_common_color}' pour le J basé sur l'analyse des quadrants")
                                    colors.append(most_common_color)
                                elif most_common_color == "black" and "black" not in colors:
                                    print(f"Ajout de la couleur 'black' pour le J basé sur l'analyse des quadrants")
                                    colors.append("black")

                            # Analyse supplémentaire basée sur la forme du J
                            # Le J a généralement une forme verticale avec un crochet en bas
                            # Essayer de détecter cette forme caractéristique
                            try:
                                # Convertir en niveaux de gris et binariser
                                gray_j = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)
                                _, binary_j = cv2.threshold(gray_j, 100, 255, cv2.THRESH_BINARY)

                                # Trouver les contours
                                contours, _ = cv2.findContours(binary_j, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                                # Si des contours sont trouvés, analyser leur forme
                                if contours:
                                    # Trouver le plus grand contour (probablement le J)
                                    largest_contour = max(contours, key=cv2.contourArea)

                                    # Calculer le rectangle englobant
                                    x, y, w, h = cv2.boundingRect(largest_contour)

                                    # Calculer le ratio hauteur/largeur (le J est généralement plus haut que large)
                                    aspect_ratio = h / w if w > 0 else 0

                                    # Si le ratio est supérieur à 1.5, c'est probablement un J
                                    if aspect_ratio > 1.5:
                                        print(f"Forme de J détectée (ratio hauteur/largeur: {aspect_ratio:.2f})")

                                        # Si le texte n'est pas déjà J, le corriger
                                        if text != 'J':
                                            print(f"Correction du texte '{text}' en 'J' basée sur la forme")
                                            text = 'J'
                            except Exception as e:
                                print(f"Erreur lors de l'analyse de forme du J: {e}")

                            # Si aucune couleur n'est détectée après toutes ces analyses, ajouter noir par défaut
                            if not colors or (len(colors) == 1 and 'white' in colors):
                                print(f"Ajout de la couleur 'black' par défaut pour le J après analyse approfondie")
                                if 'black' not in colors:
                                    colors.append('black')

                        # Pour Q spécifiquement, vérifier si la couleur détectée est cohérente
                        if text == 'Q' and len(colors) > 1:
                            # Si plusieurs couleurs sont détectées, garder la plus probable pour une carte
                            color_priority = ['red', 'green', 'blue', 'black']
                            for priority_color in color_priority:
                                if priority_color in colors:
                                    # Garder uniquement cette couleur et blanc si présent
                                    new_colors = [priority_color]
                                    if 'white' in colors:
                                        new_colors.append('white')
                                    colors = new_colors
                                    print(f"Simplification des couleurs pour Q: {colors}")
                                    break

                # Stocker les résultats pour cette région
                results[name] = {
                    "text": text,
                    "colors": colors
                }

                # Afficher les résultats pour le débogage
                print(f"Résultats pour la région {name}: Texte='{text}', Couleurs={colors}")

            return results
        except Exception as e:
            print(f"❌ Erreur lors du traitement de l'image: {e}")
            return {}

    def save_results(self, results, output_path=None):
        """Sauvegarde les résultats dans un fichier JSON

        Args:
            results (dict): Résultats de la détection à sauvegarder
            output_path (str, optional): Chemin du fichier de sortie.
                Si None, utilise "detection_results.json" dans le répertoire courant.

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if not results:
            print("⚠️ Aucun résultat à sauvegarder")
            return False

        if output_path is None:
            output_path = "detection_results.json"

        try:
            # Créer le répertoire parent si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Sauvegarder les résultats au format JSON
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=4)

            print(f"✅ Résultats sauvegardés dans {output_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde des résultats: {e}")
            return False

    def generate_debug_image(self, image_path, results, output_path=None):
        """Génère une image de débogage avec les régions et les résultats

        Args:
            image_path (str): Chemin vers l'image originale
            results (dict): Résultats de la détection
            output_path (str, optional): Chemin du fichier de sortie.
                Si None, utilise "detection_debug.jpg" dans le répertoire courant.

        Returns:
            bool: True si la génération a réussi, False sinon
        """
        if not results:
            print("⚠️ Aucun résultat à visualiser")
            return False

        if output_path is None:
            output_path = "detection_debug.jpg"

        try:
            # Charger l'image originale
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Impossible de charger l'image: {image_path}")

            # Créer une copie pour le débogage
            debug_image = image.copy()

            # Récupérer la configuration des régions
            region_config = self.config.get('all_regions', self.config.get('roi', {}))

            if not region_config:
                print("⚠️ Aucune région définie dans la configuration")
                return False

            # Dessiner les régions et ajouter les résultats
            for name, coords in region_config.items():
                if name in results:
                    # Récupérer les coordonnées selon le format utilisé
                    if 'x' in coords and 'y' in coords:
                        x, y = coords['x'], coords['y']
                    elif 'left' in coords and 'top' in coords:
                        x, y = coords['left'], coords['top']
                    else:
                        print(f"⚠️ Format de coordonnées non reconnu pour la région {name}")
                        continue

                    width = coords.get('width', 0)
                    height = coords.get('height', 0)

                    if width <= 0 or height <= 0:
                        print(f"⚠️ Dimensions invalides pour la région {name}: {width}x{height}")
                        continue

                    # Dessiner le rectangle de la région
                    cv2.rectangle(debug_image, (x, y), (x + width, y + height), (0, 255, 0), 2)

                    # Ajouter le nom de la région
                    cv2.putText(debug_image, name, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                    # Ajouter le texte détecté
                    text = results[name]["text"]
                    if text:
                        cv2.putText(debug_image, text, (x, y + height + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

                    # Ajouter les couleurs détectées
                    colors = results[name]["colors"]
                    if colors:
                        color_text = ", ".join(colors)

                        # Utiliser des couleurs différentes pour chaque type de couleur détectée
                        color_display = (255, 0, 0)  # Bleu par défaut

                        if 'red' in colors:
                            color_display = (0, 0, 255)  # Rouge
                        elif 'green' in colors:
                            color_display = (0, 255, 0)  # Vert
                        elif 'blue' in colors:
                            color_display = (255, 0, 0)  # Bleu
                        elif 'white' in colors:
                            color_display = (255, 255, 255)  # Blanc
                        elif 'black' in colors:
                            color_display = (0, 0, 0)  # Noir

                        cv2.putText(debug_image, color_text, (x, y + height + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color_display, 1)

            # Créer le répertoire parent si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Sauvegarder l'image de débogage
            cv2.imwrite(output_path, debug_image)
            print(f"✅ Image de débogage sauvegardée dans {output_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la génération de l'image de débogage: {e}")
            return False

def main():
    """Fonction principale pour l'exécution en ligne de commande

    Cette fonction permet d'utiliser le détecteur directement depuis la ligne de commande.
    Exemple d'utilisation:
        python detector.py image.jpg --config config.json --debug
    """
    # Analyser les arguments de la ligne de commande
    parser = argparse.ArgumentParser(
        description="Détecteur de cartes et de couleurs pour Poker Advisor",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("image_path",
                      help="Chemin vers l'image à analyser")
    parser.add_argument("--config",
                      help="Chemin vers le fichier de configuration",
                      default="config/poker_advisor_config.json")
    parser.add_argument("--output",
                      help="Chemin vers le fichier de sortie JSON",
                      default="detection_results.json")
    parser.add_argument("--debug",
                      action="store_true",
                      help="Générer une image de débogage")
    parser.add_argument("--debug-output",
                      help="Chemin vers l'image de débogage",
                      default="detection_debug.jpg")
    parser.add_argument("--use-cuda",
                      action="store_true",
                      help="Utiliser CUDA (GPU) si disponible")

    args = parser.parse_args()

    # Vérifier si l'image existe
    if not os.path.exists(args.image_path):
        print(f"❌ L'image spécifiée n'existe pas: {args.image_path}")
        return

    print(f"🔍 Analyse de l'image: {args.image_path}")
    print(f"📋 Configuration: {args.config}")

    # Créer le détecteur avec les options spécifiées
    try:
        detector = Detector(args.config, use_cuda=args.use_cuda)
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation du détecteur: {e}")
        return

    # Traiter l'image
    print("⏳ Traitement de l'image en cours...")
    results = detector.process_image(args.image_path)

    if not results:
        print("❌ Aucun résultat obtenu")
        return

    # Afficher les résultats
    print("\n=== Résultats de la détection ===")
    for name, data in results.items():
        print(f"Région: {name}")
        print(f"  Texte: {data['text'] or '(aucun texte détecté)'}")
        print(f"  Couleurs: {', '.join(data['colors']) or '(aucune couleur détectée)'}")

    # Sauvegarder les résultats
    if detector.save_results(results, args.output):
        print(f"💾 Résultats sauvegardés dans: {args.output}")

    # Générer l'image de débogage si demandé
    if args.debug:
        if detector.generate_debug_image(args.image_path, results, args.debug_output):
            print(f"🖼️ Image de débogage générée: {args.debug_output}")

    print("✅ Traitement terminé")

if __name__ == "__main__":
    main()
